<!-- 直接区域颜色测试 -->
<template>
  <div class="test-container">
    <h2>直接区域颜色测试</h2>
    
    <div class="controls">
      <el-button @click="testWithColors">测试有颜色</el-button>
      <el-button @click="testWithoutColors">测试无颜色</el-button>
      <el-button @click="showDebugInfo">显示调试信息</el-button>
    </div>
    
    <div class="chart-wrapper">
      <cpt-chart-geo-scatter 
        :width="800" 
        :height="600" 
        :option="currentOption"
        :key="chartKey"
      />
    </div>
    
    <div class="debug-info">
      <h3>当前配置</h3>
      <div class="config-item">
        <h4>区域颜色配置:</h4>
        <pre>{{ JSON.stringify(currentOption.attribute.regionColors, null, 2) }}</pre>
      </div>
      
      <div class="config-item">
        <h4>完整attribute配置:</h4>
        <pre>{{ JSON.stringify(currentOption.attribute, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';

defineOptions({
  name: 'direct-region-test'
});

const chartKey = ref(0);

// 基础配置
const baseOption = {
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify([
      { name: '七星关区', lng: 105.284852, lat: 27.302085, value: 100 },
      { name: '大方县', lng: 105.609254, lat: 27.143521, value: 80 }
    ])
  },
  attribute: {
    titleText: '毕节市区域颜色直接测试',
    map: 'bijie',
    roam: true,
    zoom: 4,
    center: [105.5, 27.0],
    
    showMapLabel: true,
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,
    
    showMapEmphasisLabel: true,
    mapEmphasisLabelColor: '#000',
    mapEmphasisAreaColor: '#e0e0e0',
    mapEmphasisBorderColor: '#666',
    
    seriesName: '测试数据',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 10,
    scatterColor: '#ff6b6b',
    showScatterLabel: true,
    
    regionColors: {},
    regionValues: {}
  }
};

const currentOption = ref({ ...baseOption });

const testWithColors = () => {
  console.log('=== 测试有颜色配置 ===');

  // 创建新的配置对象，确保响应式更新
  const newOption = {
    cptDataForm: { ...currentOption.value.cptDataForm },
    attribute: {
      ...currentOption.value.attribute,
      map: 'china',
      regionColors: {
        '北京市': '#FF6B6B',
        '上海市': '#4ECDC4',
        '广东省': '#45B7D1',
        '浙江省': '#96CEB4',
        '江苏省': '#FFEAA7'
      }
    }
  };

  currentOption.value = newOption;
  chartKey.value++;

  console.log('设置区域颜色:', newOption.attribute.regionColors);
  console.log('新选项:', newOption);
};

const testWithoutColors = () => {
  console.log('=== 测试无颜色配置 ===');

  // 创建新的配置对象，清空区域颜色
  const newOption = {
    cptDataForm: { ...currentOption.value.cptDataForm },
    attribute: {
      ...currentOption.value.attribute,
      regionColors: {}
    }
  };

  currentOption.value = newOption;
  chartKey.value++;

  console.log('清除区域颜色');
};

const showDebugInfo = () => {
  console.log('=== 调试信息 ===');
  console.log('当前选项:', currentOption.value);
  console.log('区域颜色配置:', currentOption.value.attribute.regionColors);
  console.log('区域颜色配置类型:', typeof currentOption.value.attribute.regionColors);
  console.log('区域颜色配置键:', Object.keys(currentOption.value.attribute.regionColors));
};

onMounted(() => {
  console.log('直接区域颜色测试页面加载完成');
  // 默认测试有颜色的情况
  testWithColors();
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.debug-info {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.config-item {
  margin: 15px 0;
  padding: 10px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.config-item h4 {
  margin-top: 0;
  color: #333;
}

.config-item pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

h2, h3 {
  color: #333;
}
</style>
