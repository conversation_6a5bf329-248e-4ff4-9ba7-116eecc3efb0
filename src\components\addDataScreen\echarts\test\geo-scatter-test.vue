<!-- GEO SVG 散点图测试页面 -->
<template>
  <div class="test-container">
    <h2>GEO SVG 散点图测试</h2>
    
    <div class="controls">
      <el-button @click="toggleScatterType">
        切换为{{ currentType === 'scatter' ? '涟漪散点' : '普通散点' }}
      </el-button>
      <el-select v-model="currentMap" placeholder="选择地图" @change="changeMap">
        <el-option label="中国" value="china" />
        <el-option label="贵州省" value="guizhou" />
        <el-option label="常州市" value="changZhou" />
        <el-option label="四会市" value="sihui" />
      </el-select>
      <el-button @click="toggleVisualMap">
        {{ showVisualMap ? '隐藏' : '显示' }}视觉映射
      </el-button>
      <el-button @click="randomizeData">随机数据</el-button>
    </div>
    
    <div class="chart-wrapper">
      <cpt-chart-geo-scatter 
        :width="800" 
        :height="600" 
        :option="testOption" 
      />
    </div>
    
    <div class="info-panel">
      <h3>功能特性</h3>
      <ul>
        <li>✅ 支持多种地图底图切换</li>
        <li>✅ 支持普通散点和涟漪散点</li>
        <li>✅ 支持地图缩放和平移</li>
        <li>✅ 支持散点大小根据数值动态调整</li>
        <li>✅ 支持视觉映射颜色渐变</li>
        <li>✅ 支持散点标签显示</li>
        <li>✅ 支持鼠标悬停交互</li>
        <li>✅ 支持多种数据格式</li>
      </ul>
      
      <h3>数据格式示例</h3>
      <pre>{{ JSON.stringify(sampleData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';

defineOptions({
  name: 'geo-scatter-test'
});

const currentType = ref('scatter');
const currentMap = ref('china');
const showVisualMap = ref(false);

const sampleData = [
  { name: '北京', lng: 116.46, lat: 39.92, value: 150 },
  { name: '上海', lng: 121.48, lat: 31.22, value: 200 },
  { name: '广州', lng: 113.23, lat: 23.16, value: 120 },
  { name: '深圳', lng: 114.07, lat: 22.62, value: 180 }
];

const testData = ref([...sampleData]);

const testOption = computed(() => ({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify(testData.value)
  },
  attribute: {
    // 标题配置
    titleText: `${getMapName(currentMap.value)}数据分布`,
    subtext: '可拖拽的散点图组件测试',
    titleLeft: 'center',
    titleTop: 10,
    titleFontSize: 18,
    titleColor: '#333',
    subTitleColor: '#666',
    subTitleFontSize: 12,
    
    // 地图配置
    map: currentMap.value,
    roam: true,
    zoom: 1.2,
    center: getMapCenter(currentMap.value),
    
    // 地图样式
    showMapLabel: true,
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,
    
    // 散点配置
    seriesName: '数据点',
    scatterType: currentType.value,
    scatterSymbol: 'circle',
    scatterBaseSize: 8,
    scatterSizeMultiplier: 0.5,
    scatterColor: '#ff6b6b',
    scatterOpacity: 0.8,
    scatterBorderWidth: 1,
    scatterBorderColor: '#fff',
    
    // 散点标签
    showScatterLabel: true,
    scatterLabelPosition: 'top',
    scatterLabelColor: '#333',
    scatterLabelSize: 12,
    scatterLabelFormat: 'name',
    
    // 涟漪效果
    rippleBrushType: 'stroke',
    rippleScale: 2.5,
    ripplePeriod: 4,
    
    // 视觉映射
    showVisualMap: showVisualMap.value,
    visualMapMin: 0,
    visualMapMax: 200,
    visualMapLeft: 'left',
    visualMapTop: 'bottom',
    visualMapMaxText: '高',
    visualMapMinText: '低',
    visualMapTextColor: '#333',
    visualMapMinColor: '#50a3ba',
    visualMapMaxColor: '#eac736'
  }
}));

const toggleScatterType = () => {
  currentType.value = currentType.value === 'scatter' ? 'effectScatter' : 'scatter';
};

const changeMap = () => {
  // 根据地图类型调整数据
  if (currentMap.value === 'guizhou') {
    testData.value = [
      { name: '贵阳市', lng: 106.713478, lat: 26.578343, value: 150 },
      { name: '遵义市', lng: 107.297104, lat: 27.725654, value: 120 },
      { name: '毕节市', lng: 105.300492, lat: 27.302612, value: 100 },
      { name: '铜仁市', lng: 109.191555, lat: 27.718346, value: 90 }
    ];
  } else if (currentMap.value === 'changZhou') {
    testData.value = [
      { name: '天宁区', lng: 119.946973, lat: 31.772752, value: 80 },
      { name: '钟楼区', lng: 119.926762, lat: 31.795048, value: 75 },
      { name: '新北区', lng: 119.973129, lat: 31.810689, value: 70 }
    ];
  } else {
    testData.value = [...sampleData];
  }
};

const toggleVisualMap = () => {
  showVisualMap.value = !showVisualMap.value;
};

const randomizeData = () => {
  testData.value = testData.value.map(item => ({
    ...item,
    value: Math.floor(Math.random() * 200) + 50
  }));
};

const getMapName = (mapType: string) => {
  const mapNames: Record<string, string> = {
    china: '全国',
    guizhou: '贵州省',
    changZhou: '常州市',
    sihui: '四会市'
  };
  return mapNames[mapType] || '地图';
};

const getMapCenter = (mapType: string) => {
  const centers: Record<string, number[]> = {
    china: [104, 35],
    guizhou: [106.7, 26.6],
    changZhou: [119.95, 31.78],
    sihui: [112.7, 23.3]
  };
  return centers[mapType] || [104, 35];
};

// 初始化时根据地图调整数据
onMounted(() => {
  changeMap();
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #f5f5f5;
}

.info-panel {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.info-panel h3 {
  margin-top: 0;
  color: #333;
}

.info-panel ul {
  margin: 10px 0;
  padding-left: 20px;
}

.info-panel li {
  margin: 5px 0;
  color: #666;
}

.info-panel pre {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  border: 1px solid #ddd;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}
</style>
