<!-- 区域颜色测试页面 -->
<template>
  <div class="test-container">
    <h2>地图区域颜色测试</h2>
    
    <div class="controls">
      <el-select v-model="currentMap" @change="onMapChange">
        <el-option label="中国" value="china" />
        <el-option label="贵州省" value="guizhou" />
        <el-option label="毕节市" value="bijie" />
        <el-option label="常州市" value="changZhou" />
      </el-select>
      <el-button @click="enableRegionColors">启用区域颜色</el-button>
      <el-button @click="randomizeColors">随机颜色</el-button>
      <el-button @click="resetColors">重置颜色</el-button>
    </div>
    
    <div class="layout">
      <div class="chart-section">
        <h3>地图预览</h3>
        <div class="chart-wrapper">
          <cpt-chart-geo-scatter 
            :width="600" 
            :height="500" 
            :option="chartOption"
            :key="chartKey"
          />
        </div>
      </div>
      
      <div class="config-section">
        <h3>区域颜色配置</h3>
        <div class="region-list">
          <div v-for="region in availableRegions" :key="region" class="region-item">
            <span class="region-name">{{ region }}</span>
            <el-color-picker 
              v-model="regionColors[region]" 
              @change="updateChart"
              show-alpha 
            />
            <el-button 
              size="small" 
              @click="clearRegionColor(region)"
              :disabled="!regionColors[region]"
            >
              清除
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="info-section">
      <h3>当前配置</h3>
      <pre>{{ JSON.stringify(chartOption.attribute.regionColors, null, 2) }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';
import * as echarts from 'echarts';

defineOptions({
  name: 'region-colors-test'
});

const currentMap = ref('bijie');
const chartKey = ref(0);
const regionColors = ref<Record<string, string>>({});

// 获取当前地图的可用区域
const availableRegions = computed(() => {
  const mapData = echarts.getMap(currentMap.value);
  if (!mapData?.geoJson?.features) return [];
  
  return mapData.geoJson.features
    .map((feature: any) => feature.properties?.name)
    .filter(Boolean);
});

const chartOption = computed(() => ({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify([
      { name: '测试点1', lng: getTestCoordinate().lng, lat: getTestCoordinate().lat, value: 100 },
      { name: '测试点2', lng: getTestCoordinate().lng + 0.5, lat: getTestCoordinate().lat + 0.2, value: 120 }
    ])
  },
  attribute: {
    titleText: `${getMapName(currentMap.value)}区域颜色测试`,
    map: currentMap.value,
    roam: true,
    zoom: getOptimalZoom(currentMap.value),
    center: getOptimalCenter(currentMap.value),
    
    showMapLabel: true,
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,
    
    seriesName: '测试数据',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 8,
    scatterColor: '#ff6b6b',
    showScatterLabel: true,
    
    // 区域颜色配置
    regionColors: regionColors.value,
    regionValues: {}
  }
}));

const getMapName = (mapType: string) => {
  const names: Record<string, string> = {
    china: '中国',
    guizhou: '贵州省',
    bijie: '毕节市',
    changZhou: '常州市'
  };
  return names[mapType] || mapType;
};

const getOptimalZoom = (mapType: string) => {
  const zooms: Record<string, number> = {
    china: 1.2,
    guizhou: 2,
    bijie: 4,
    changZhou: 3
  };
  return zooms[mapType] || 1.2;
};

const getOptimalCenter = (mapType: string) => {
  const centers: Record<string, number[]> = {
    china: [104, 35],
    guizhou: [106.7, 26.6],
    bijie: [105.5, 27.0],
    changZhou: [119.95, 31.78]
  };
  return centers[mapType] || [104, 35];
};

const getTestCoordinate = () => {
  const coords: Record<string, {lng: number, lat: number}> = {
    china: { lng: 116.46, lat: 39.92 },
    guizhou: { lng: 106.7, lat: 26.6 },
    bijie: { lng: 105.5, lat: 27.0 },
    changZhou: { lng: 119.95, lat: 31.78 }
  };
  return coords[currentMap.value] || { lng: 104, lat: 35 };
};

const onMapChange = () => {
  regionColors.value = {};
  chartKey.value++;
};

const enableRegionColors = () => {
  // 为每个区域设置默认颜色
  const defaultColors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];
  
  availableRegions.value.forEach((region: string, index: number) => {
    if (!regionColors.value[region]) {
      regionColors.value[region] = defaultColors[index % defaultColors.length];
    }
  });
  
  updateChart();
};

const randomizeColors = () => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    '#F39C12', '#E74C3C', '#9B59B6', '#3498DB', '#1ABC9C'
  ];
  
  availableRegions.value.forEach((region: string) => {
    regionColors.value[region] = colors[Math.floor(Math.random() * colors.length)];
  });
  
  updateChart();
};

const resetColors = () => {
  regionColors.value = {};
  updateChart();
};

const clearRegionColor = (region: string) => {
  delete regionColors.value[region];
  updateChart();
};

const updateChart = () => {
  chartKey.value++;
};

onMounted(() => {
  console.log('区域颜色测试页面加载完成');
  console.log('可用区域:', availableRegions.value);
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
  align-items: center;
}

.layout {
  display: flex;
  gap: 20px;
  margin: 20px 0;
}

.chart-section {
  flex: 2;
}

.chart-wrapper {
  width: 600px;
  height: 500px;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: #fff;
}

.config-section {
  flex: 1;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
}

.region-list {
  max-height: 400px;
  overflow-y: auto;
}

.region-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.region-item:last-child {
  border-bottom: none;
}

.region-name {
  flex: 1;
  font-weight: 500;
  color: #333;
  margin-right: 10px;
}

.info-section {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.info-section pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

h2, h3 {
  color: #333;
}
</style>
