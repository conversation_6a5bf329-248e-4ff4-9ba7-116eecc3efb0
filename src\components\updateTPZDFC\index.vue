<!-- 选择文件夹更新图片 -->
<template>
  <div class="updateExcel-main" v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      title="批量更新图片"
      v-model="updateTPZDFCDialogCopy"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="875px"
      :before-close="handleClose"
      v-dialogDrag
      destroy-on-close
    >
      <div class="title-label">1、操作方式</div>
      <el-radio-group v-model="operaType">
        <el-tooltip class="item" effect="dark" content="针对更新普通属性组" placement="top">
          <el-radio :label="1">一对一</el-radio>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="针对更新特殊（公示照片，一张照片多个宗地更新）" placement="top">
          <el-radio :label="2">一对多</el-radio>
        </el-tooltip>
      </el-radio-group>
      <div class="title-label">2、选择文件夹</div>
      <el-upload
        ref="uploadFolder"
        :show-file-list="false"
        :headers="headers"
        :accept="accept"
        :auto-upload="false"
        name="files"
        :action="`${base}/qjt/file/multi/upload`"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        :on-error="uploadError"
        :on-exceed="exceed"
        multiple
        :on-change="handleChangeFCZD"
      />
      <div style="margin-top: -15px">
        <el-button type="primary" @click="uploadBtn?.click()" size="small" v-if="!isUpload" :disabled="isUpload">点击选取文件夹</el-button>
        <el-button type="warning" @click="handleReselectFolder" size="small" v-else :disabled="!isUpload">重新选择</el-button>
      </div>
      <div style="margin-top: 10px" v-show="folderName">已选文件夹：{{ folderName }}</div>
      <div class="title-label">3、选择节点</div>
      <el-cascader
        clearable
        style="width: 100%"
        v-model="nodeId"
        :options="ruleTree"
        placeholder="请选择节点"
        :props="{ checkStrictly: true, children: 'list', value: 'id', label: 'typeName' }"
        @change="handleChange"
      />
      <div class="title-label">4、选择属性组及映射字段</div>
      <div class="flex-row">
        <el-select v-model="groupId" placeholder="请选择属性组" @change="changeGroup" filterable style="width: 100%">
          <el-option v-for="item in fieldGroupModelList" :key="item.id" :label="item.typeName" :value="item.id" />
        </el-select>
        <el-select v-model="ysFieldName" placeholder="请选择映射字段" clearable filterable style="margin-left: 10px">
          <el-option v-for="item in localfields" :key="item.fieldName" :label="item.fieldCn" :value="item.fieldName" />
        </el-select>
      </div>
      <div class="title-label">5、关联字段</div>
      <div class="flex-row">
        <el-select v-model="ysChooseGroupId" placeholder="请选择关联属性组" filterable style="width: 100%" @change="chooseYSGroup">
          <el-option v-for="item in fieldGroupModelList" :key="item.id" :label="item.typeName" :value="item.id" />
        </el-select>
        <el-select v-model="ysChooseField" placeholder="请选择关联字段" clearable filterable style="margin-left: 10px">
          <el-option v-for="item in ysChooseFieldList" :key="item.fieldName" :label="item.fieldCn" :value="item.fieldName" />
        </el-select>
      </div>
      <div class="title-label">6、更新方式</div>
      <el-radio-group v-model="type">
        <el-radio :label="item.value" v-for="(item, index) in typeList" :key="index" :disabled="item.disable">{{ item.label }}</el-radio>
      </el-radio-group>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">提 交</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      title="文件上传进度"
      v-model="uploadFileProDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="300px"
      destroy-on-close
    >
      <div class="down-dialog">
        <el-progress :stroke-width="16" type="circle" :percentage="uploadFileProgress" />
        <div style="margin-top: 10px">已上传：{{ allFileCount }}/总条数：{{ uploadCount }}</div>
      </div>
    </el-dialog>

    <el-dialog
      title="数据导入中"
      v-model="uploadLodingDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="300px"
      destroy-on-close
    >
      <div class="down-dialog">
        <el-progress :stroke-width="16" type="circle" :percentage="uploadProgress" />
        <div style="margin-top: 10px">{{ uploadMsg }}</div>
      </div>
    </el-dialog>

    <el-dialog
      title="上传前校验"
      :append-to-body="true"
      :modal-append-to-body="false"
      v-model="verificationDialog"
      :close-on-click-modal="false"
      @closed="closeVerification"
      v-dialogDrag
      width="650px"
      destroy-on-close
    >
      <div class="dialog-row">校验进度：</div>
      <el-progress :text-inside="true" :stroke-width="26" :percentage="verificationPlan" />
      <template v-if="verificationPlan >= 100">
        <div class="dialog-row" style="margin-top: 10px">总数据量：{{ uploadCount }} 条</div>
        <div class="dialog-row">校验成功：{{ verSuccNum }} 条</div>
        <div class="dialog-row">校验失败：{{ verErrorNum }} 条</div>
        <div class="dialog-row" v-show="uploadShpError.length != 0">
          <el-link type="primary" @click="downLoadErrorLog">下载错误日志</el-link>
        </div>
        <div class="error-div" v-show="uploadShpError.length != 0">
          <div class="item" v-for="(item, index) in uploadShpError" :key="index">
            {{ item }}
          </div>
        </div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeVerification">取 消</el-button>
          <el-button type="primary" @click="nextSubmit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, computed } from 'vue';
import { ElMessage, ElMessageBox, valueEquals } from 'element-plus';
import { getToken } from '@/utils/auth';
import { selectRules, addExamine, examineList } from '@/api/modal';
import { updateInstance, updateInstanceCheck } from '@/api/project';
import { excelDateToJsDate } from '@/utils/validate';
import type { UploadInstance } from 'element-plus';

// Props 定义
const props = defineProps<{
  updateZDFCDialog: boolean;
  moduleId: string | number;
}>();
const updateTPZDFCDialogCopy = computed(() => props.updateZDFCDialog);
// Emits 定义
const emit = defineEmits<{
  (e: 'closeUpdateZDFC'): void;
}>();

// 响应式状态
const headers = { Authorization: 'Bearer ' + getToken() };
const accept = `.${['png', 'jpg', 'jpeg'].join(',.')}`;
const base = import.meta.env.VITE_APP_BASE_API;
const fileList = ref([]);
const sortUrl = '/project/fund/excel/upload';
const isUpload = ref(false);
const fileMsg = ref('');
const fullscreenLoading = ref(false);
const ruleTree = ref([]);
const nodeId = ref('');
const chooseNode = ref({});
const fieldGroupModelList = ref<GroupItem[]>([]);
const chooseGroup = ref({});
const groupId = ref('');
const operaType = ref(1);
const localfields = ref<FieldItem[]>([]);
const shpFields = ref([]);
const ysChooseGroupId = ref('');
const ysChooseGroup = ref({});
const ysChooseFieldList = ref([]);
const ysChooseField = ref('');
const uploadLodingDialog = ref(false);
const uploadProgress = ref(0);
const uploadMsg = ref('');
const uploadShpError = ref([]);
const folderName = ref('');
const uploadBtn = ref<HTMLInputElement | null>(null);
const ysFieldName = ref('');
const uploadList = ref<UploadItem[]>([]);
const uploadCount = ref(0);
const uploadFileProDialog = ref(false);
const uploadFileProgress = ref(0);
const nowCount = ref(0);
// 当前上传的文件夹中的所有文件的总数
const allFileCount = ref(0);
const verificationDialog = ref(false);
const verSuccNum = ref(0);
const verErrorNum = ref(0);
const nextEndList = ref([]);
const verificationPlan = ref(0);
const successNum = ref(0);
const type = ref(20);
const typeList = ref([
  { label: '增加', value: 21, disable: false },
  { label: '覆盖', value: 20, disable: false }
]);
const uploadShpNum = ref(0); //上传的条数
// 组件引用
const uploadFolder = ref<UploadInstance | null>(null);

// 定义类型接口
interface UploadItem {
  zdId: string;
  list: { url: string }[];
}

interface FieldItem {
  fieldName: string;
  fieldCn: string;
  valueMethod: string;
  disable?: boolean;
  attribution?: {
    expendList?: Array<{
      cnName: string;
      enName: string;
      valueMethod: string;
    }>;
  };
}

interface GroupItem {
  id: string;
  linkId: string;
  ruleAttribution: any;
  fieldModelList: FieldItem[];
}

// 监听器
watch(
  () => props.updateZDFCDialog,
  (val) => {
    if (val) {
      nextTick(() => {
        uploadBtn.value = uploadFolder.value?.$el.querySelector('input');
        if (uploadBtn.value) {
          uploadBtn.value.webkitdirectory = true;
        }
      });
      getTree();
      // 初始化
      nextEndList.value = [];
      ruleTree.value = [];
      nodeId.value = '';
      chooseNode.value = {};
      fieldGroupModelList.value = [];
      chooseGroup.value = {};
      groupId.value = '';
      localfields.value = [];
      shpFields.value = [];
      uploadList.value = [];
      nextEndList.value = [];
      uploadFolder.value?.clearFiles();
      folderName.value = '';
    } else {
      // 关闭弹框的时候也需要重新设置可以选择文件夹
      nextTick(() => {
        uploadBtn.value = uploadFolder.value?.$el.querySelector('input');
        if (uploadBtn.value) {
          uploadBtn.value.webkitdirectory = true;
        }
      });
    }
  },
  { deep: true }
);

// 方法定义
const handleClose = () => {
  ruleTree.value = [];
  nodeId.value = '';
  chooseNode.value = {};
  fieldGroupModelList.value = [];
  chooseGroup.value = {};
  groupId.value = '';
  localfields.value = [];
  shpFields.value = [];
  uploadList.value = [];
  nextEndList.value = [];
  uploadFolder.value?.clearFiles();
  folderName.value = '';
  allFileCount.value = 0;
  uploadCount.value = 0;
  type.value = 20;
  isUpload.value = false;
  verificationPlan.value = 0;
  verErrorNum.value = 0;
  verSuccNum.value = 0;
  uploadShpError.value = [];
  uploadProgress.value = 0;
  ysChooseField.value = '';
  ysFieldName.value = '';
  ysChooseGroupId.value = '';
  nowCount.value = 0;
  emit('closeUpdateZDFC');
};

const uploadSuccess = (response: any, file: any, fileList: any[]) => {
  allFileCount.value++;
  uploadFileProgress.value = Number(
    ((allFileCount.value / uploadCount.value) * 100).toFixed(2) > 100 ? 100 : ((allFileCount.value / uploadCount.value) * 100).toFixed(2)
  );

  const zdId = file.raw.webkitRelativePath.split('/')[1];
  const foundObject = uploadList.value.find((obj) => obj.zdId == zdId);
  if (foundObject) {
    foundObject.list.push({
      url: response.data[0].path
    });
  } else {
    nowCount.value++;
    uploadList.value.push({
      zdId: zdId,
      list: [
        {
          url: response.data[0].path
        }
      ]
    });
  }
  //  获取最终要上传的文件的list 的长度
  const leng = uploadList.value.reduce((sum, item) => {
    return sum + item.list.length;
  }, 0);
  if (nowCount.value == uploadList.value.length && leng === fileList.length) {
    uploadFileProDialog.value = false;
    endSubmit();
  }
};

const getTree = async () => {
  try {
    const res = await selectRules({ moduleId: props.moduleId });
    if (res.code == 200) {
      ruleTree.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

const handleChangeYS = (val: any[]) => {};

const handleChange = (val: any[]) => {
  getChooseNode(ruleTree.value, val[val.length - 1]);
};

const getChooseNode = (list: any[], id: string) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].id == id) {
      chooseNode.value = list[i];
      fieldGroupModelList.value = list[i].fieldGroupModelList;
      break;
    }
    if (list[i].list.length != 0) {
      getChooseNode(list[i].list, id);
    }
  }
};

const changeGroup = async (val: string) => {
  for (let i = 0; i < fieldGroupModelList.value.length; i++) {
    if (fieldGroupModelList.value[i].id == val) {
      chooseGroup.value = fieldGroupModelList.value[i];
      localfields.value = [];
      fieldGroupModelList.value[i].fieldModelList.forEach((v: any) => {
        if (v.valueMethod == 'idCardScan') {
          if (v.attribution.expendList) {
            v.attribution.expendList.forEach((k: any) => {
              const ite = {
                fieldCn: k.cnName,
                fieldName: k.enName,
                valueMethod: k.valueMethod
              };
              if (!['upload', 'idCardBitmap', 'xtqm', 'xtzw'].includes(k.valueMethod)) {
                ite.disable = true;
              }
              localfields.value.push(ite);
            });
          }
        } else {
          if (!['upload', 'idCardBitmap', 'xtqm', 'xtzw'].includes(v.valueMethod)) {
            v.disable = true;
          }
          localfields.value.push(v);
        }
      });
      await getLocalFild();
      break;
    }
  }
};

const getLocalFild = async () => {
  localfields.value.forEach((f: any) => {
    for (let index = 0; index < shpFields.value.length; index++) {
      if (f.fieldName.toUpperCase() == shpFields.value[index]) {
        f.yz = shpFields.value[index];
        break;
      }
    }
  });
};

const changeShpField = (val: any) => {};

const chooseYSGroup = (val: string) => {
  for (let i = 0; i < fieldGroupModelList.value.length; i++) {
    if (fieldGroupModelList.value[i].id == val) {
      ysChooseGroup.value = fieldGroupModelList.value[i];
      ysChooseFieldList.value = fieldGroupModelList.value[i].fieldModelList;
      break;
    }
  }
};

const endSubmit = async () => {
  const params = [];
  let num = 1;
  uploadList.value.forEach((v: any) => {
    const item = {
      appId: 0,
      attribution: {},
      groupId: chooseGroup.value.id,
      linkId: chooseGroup.value.linkId,
      ruleAttribution: chooseGroup.value.ruleAttribution,
      groupModel: {
        id: ysChooseGroup.value.id,
        linkId: ysChooseGroup.value.linkId,
        fieldModelList: [
          {
            fieldName: ysChooseField.value
          }
        ],
        ruleId: chooseNode.value.id
      },
      shpId: num
    };
    let nowField = null;
    for (let index = 0; index < localfields.value.length; index++) {
      if (localfields.value[index].fieldName == ysFieldName.value) {
        nowField = localfields.value[index];
        break;
      }
    }
    if (nowField.valueMethod == 'upload') {
      item.attribution[ysFieldName.value] = v.list;
    } else {
      item.attribution[ysFieldName.value] = v.list[0].url;
    }
    item.attribution[ysChooseField.value] = v.zdId;
    num++;
    params.push(item);
  });

  uploadShpNum.value = params.length;

  nextEndList.value = [];
  uploadShpError.value = [];
  verErrorNum.value = 0;
  verSuccNum.value = 0;

  const chunksData = await handleChunkData(params, 10);

  verificationDialog.value = true;
  uploadFileProDialog.value = false;
  verificationPlan.value = 0;
  for (let index = 0; index < chunksData.chunks.length; index++) {
    try {
      const resultNum = await verificationOnece(chunksData.chunks[index], index + 1, chunksData.count, chunksData.chunkSize);
      if (resultNum == 100) {
      }
    } catch (error) {
      continue;
    }
  }
};

const verificationOnece = async (list: any[], num: number, count: number, chunkSize: number) => {
  return new Promise((resolve, reject) => {
    let moreFlag = 0;
    if (operaType.value == 2) {
      moreFlag = 1;
    }
    updateInstanceCheck(list, moreFlag).then((res) => {
      if (res.code == 200) {
        setTimeout(() => {
          if (res.data.length == 0) {
            verSuccNum.value = verSuccNum.value + list.length;
            nextEndList.value.push(...list);
          } else {
            verErrorNum.value = verErrorNum.value + res.data.length;
            if (res.data.length < list.length) {
              const errNums = [];
              res.data.forEach((v: any) => {
                errNums.push(parseInt(v.substring(1, v.indexOf('行'))));
              });

              list.forEach((v: any) => {
                if (!errNums.some((obj: any) => obj == v.shpId)) {
                  nextEndList.value.push(v);
                  verSuccNum.value++;
                }
              });
            }
            uploadShpError.value.push(...res.data);
          }
          verificationPlan.value = Number(
            (((num * chunkSize) / count) * 100).toFixed(2) > 100 ? 100 : (((num * chunkSize) / count) * 100).toFixed(2)
          );
          resolve(verificationPlan.value);
        }, 500);
      } else {
        reject(res.msg);
      }
    });
  });
};

const handleChunkData = async (list: any[], num: number) => {
  const count = list.length;
  const chunkSize = num || 10;
  const chunks = [];
  for (let i = 0; i < list.length; i += chunkSize) {
    chunks.push(list.slice(i, i + chunkSize));
  }
  const resultItem = {
    count: count,
    chunkSize: chunkSize,
    chunks: chunks
  };
  return new Promise((resolve) => {
    resolve(resultItem);
  });
};

const submit = () => {
  if (!isUpload.value) {
    ElMessage.error('请选择上传的文件！！！');
    return;
  }
  if (!nodeId.value) {
    ElMessage.error('请选择节点！！！');
    return;
  }
  if (!ysFieldName.value) {
    ElMessage.error('请选择映射字段');
    return;
  }
  if (!groupId.value) {
    ElMessage.error('请选择属性组！！！');
    return;
  }
  uploadFolder.value.submit();
  successNum.value = 0;
  uploadFileProDialog.value = true;
};

const subsectionSubmit = async (list: any[], num: number) => {
  return new Promise((resolve, reject) => {
    let moreFlag = 0;
    if (operaType.value == 2) {
      moreFlag = 1;
    }
    updateInstance(list, moreFlag, type.value).then((res) => {
      if (res.code == 200) {
        uploadProgress.value = Number(
          (((num * 10) / uploadShpNum.value) * 100).toFixed(2) > 100 ? 100 : (((num * 10) / uploadShpNum.value) * 100).toFixed(2)
        );
        uploadMsg.value = `已成功导入${num * 10 + list.length}条`;
        successNum.value = successNum.value + res.data.length;

        resolve();
      } else {
        uploadShpError.value.push(...list);
        reject(res.msg);
      }
    });
  });
};

const beforeUpload = (file: any) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  return isJPG;
};

const uploadError = (err: any, file: any, fileList: any[]) => {
  ElMessage.error('上传失败');
};

const exceed = (file: any, fileList: any[]) => {
  ElMessage.error('上传文件数量太大，分散上传吧！');
};

const handleChangeFCZD = (file: any, fileList: any[]) => {
  isUpload.value = true;
  // 文件总数量
  uploadCount.value = fileList.length;
  // 获取文件夹名称
  if (file.raw.webkitRelativePath) {
    folderName.value = file.raw.webkitRelativePath.split('/')[0];
  }
};

const handleReselectFolder = () => {
  uploadFolder.value.clearFiles();
  isUpload.value = false;
  uploadCount.value = 0;
  folderName.value = '';
  uploadBtn.value?.click();
};

const closeVerification = () => {
  // 调用关闭外层弹框，同时也清除之前选择的值
  handleClose();
  verificationDialog.value = false;
  emit('closeUpdateZDFC');
};

const downLoadErrorLog = () => {
  const element = document.createElement('a');
  const endContent = uploadShpError.value.join('\n');
  element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(endContent));
  element.setAttribute('download', 'shp上传错误日志');
  element.style.display = 'none';
  element.click();
  document.body.removeChild(element);
};

const nextSubmit = async () => {
  if (nextEndList.value.length == 0) {
    ElMessage.error('校验成功的数据为0，请修改后重新提交！！！');
    return;
  }
  verificationDialog.value = false;
  const chunkSize = 10;
  const chunks = [];

  for (let i = 0; i < nextEndList.value.length; i += chunkSize) {
    chunks.push(nextEndList.value.slice(i, i + chunkSize));
  }

  uploadMsg.value = '已成功导入0条';
  uploadLodingDialog.value = true;
  uploadShpError.value = [];
  for (let index = 0; index < chunks.length; index++) {
    try {
      await subsectionSubmit(chunks[index], index + 1);
    } catch (error) {
      continue;
    }
  }
  uploadLodingDialog.value = false;
  handleClose();

  const str = `成功${operaType.value == 1 ? '新增' : '更新'}${successNum.value}条数据`;
  operaType.value = null;
  ElMessageBox.alert(str, `${operaType.value == 1 ? '新增' : '更新'}成功`, {
    confirmButtonText: '确定',
    callback: (action) => {
      sessionStorage.setItem('qiehuan_company', false);
      location.reload();
    }
  });
};

const handleFolderChange = (event: Event) => {
  const files = (event.target as HTMLInputElement).files;
  if (!files || files.length === 0) return;

  const folderStructure: any = {};
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const path = file.webkitRelativePath;

    const parts = path.split('/');
    folderName.value = parts[0];
    isUpload.value = true;
    let currentLevel = folderStructure;
    for (let j = 0; j < parts.length - 1; j++) {
      const part = parts[j];
      if (!currentLevel[part]) {
        currentLevel[part] = {};
      }
      currentLevel = currentLevel[part];
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-row {
  margin-bottom: 10px;
}
.error-div {
  height: 400px;
  overflow: auto;
  .item {
    margin-bottom: 5px;
  }
}
.error-content {
  .flex-row {
    margin-bottom: 10px;
  }
  .error-div {
    height: 400px;
    overflow: auto;
    .item {
      margin-bottom: 5px;
    }
  }
}
.updateExcel-main {
}
.down-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.title-label {
  margin-bottom: 10px;
  margin-top: 10px;
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
