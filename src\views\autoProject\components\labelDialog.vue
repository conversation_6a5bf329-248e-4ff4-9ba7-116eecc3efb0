<!-- 选择标注内容 -->
<template>
  <el-dialog title="标注" v-model="labelDialogCopy" width="720px" append-to-body :close-on-click-modal="false" :before-close="handleClose">
    <div class="dialog-box">
      <div class="left">
        <div class="title-div"><span class="normal-sapn">结构树</span></div>
        <div class="content">
          <el-tree
            ref="treeRef"
            :data="treeList"
            :props="defaultProps"
            highlight-current
            default-expand-all
            node-key="id"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            class="tree-div"
          >
            <template #default="{ data }">
              <div class="tree-row">
                <div class="tree-row-left">
                  <div v-if="data.iconUrl && data.iconUrl.substring(data.iconUrl.lastIndexOf('_') + 1) == 'blob'">
                    <authImg :authSrc="`${baseUrl}${data.iconUrl}?att=1`" :width="'20px'" :height="'20px'" />
                  </div>
                  <div v-else>
                    <svg-icon class-name="svg-item" :icon-class="data.iconUrl" />
                  </div>
                  <span style="margin-left: 4px">{{ data.typeName }}</span>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="center">
        <div class="title-div"><span class="normal-sapn">属性组</span></div>
        <div class="content">
          <div v-if="attrbutionGroup.length == 0" class="empty-span">暂无属性组数据</div>
          <template v-else>
            <div
              class="flex-row"
              v-for="(item, index) in attrbutionGroup"
              :class="{ 'flex-active': item.checked }"
              :key="index"
              @click="changeAtt(item)"
            >
              <div class="label">
                <span>{{ item.typeName }}</span>
                <span style="padding-left: 4px" v-if="item.groupScope == 1 && item.ruleAttribution && item.ruleAttribution.type == 'graphicalPoint'"
                  >(点)</span
                >
                <span style="padding-left: 4px" v-if="item.groupScope == 1 && item.ruleAttribution && item.ruleAttribution.type == 'graphicalLine'"
                  >(线)</span
                >
                <span style="padding-left: 4px" v-if="item.groupScope == 1 && item.ruleAttribution && item.ruleAttribution.type == 'graphicalArea'"
                  >(面)</span
                >
              </div>
              <div class="ico">
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="right">
        <div class="title-div"><span class="normal-sapn">字段</span></div>
        <div class="content">
          <div v-if="fieldList.length == 0" class="empty-span">暂无字段数据</div>
          <template v-else>
            <div v-for="(item, index) in fieldList" :key="index">
              <div class="flex-row-spe" v-if="item.valueMethod == 'idCardScan'">
                <div class="spe-title">
                  <!-- 身份证识别大标题 -->
                  {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                </div>
                <div
                  class="spe-item"
                  :class="{ 'spe-item-active': ite.checked }"
                  v-for="(ite, idx) in idCardScanList"
                  :key="idx"
                  @click="chooseField(item, ite)"
                >
                  <!-- 身份证识别中的某个字段 -->
                  <div>
                    <span>{{ ite.enName }}</span
                    ><span style="padding-left: 4px">({{ ite.label }})</span>
                  </div>
                </div>
              </div>
              <!-- 银行卡识别 -->
              <div class="flex-row-spe" v-else-if="item.valueMethod == 'xtBankCard'">
                <div class="spe-title">
                  <!-- 身份证识别大标题 -->
                  {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                </div>
                <div
                  class="spe-item"
                  :class="{ 'spe-item-active': ite.checked }"
                  v-for="(ite, idx) in yhkTypeList"
                  :key="idx"
                  @click="chooseField(item, ite)"
                >
                  <!-- 身份证识别中的某个字段 -->
                  <div>
                    <span>{{ ite.enName }}</span
                    ><span style="padding-left: 4px">({{ ite.label }})</span>
                  </div>
                </div>
              </div>
              <!-- 系统动物识别的内容 -->
              <div class="flex-row-spe" v-else-if="item.valueMethod == 'xtdwsb'">
                <div class="spe-title">
                  <!-- 大标题 -->
                  {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                </div>
                <div
                  class="spe-item"
                  :class="{ 'spe-item-active': ite.checked }"
                  v-for="(ite, idx) in DWSBOptions"
                  :key="idx"
                  @click="chooseField(item, ite)"
                >
                  <!-- 身份证识别中的某个字段 -->
                  <div>
                    <span>{{ ite.enName }}</span
                    ><span style="padding-left: 4px">({{ ite.label }})</span>
                  </div>
                </div>
              </div>
              <!-- 植物识别 -->
              <div class="flex-row-spe" v-else-if="item.valueMethod == 'xtzwsb'">
                <div class="spe-title">
                  <!-- 大标题 -->
                  {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                </div>
                <div
                  class="spe-item"
                  :class="{ 'spe-item-active': ite.checked }"
                  v-for="(ite, idx) in ZWSBOptions"
                  :key="idx"
                  @click="chooseField(item, ite)"
                >
                  <!-- 身份证识别中的某个字段 -->
                  <div>
                    <span>{{ ite.enName }}</span
                    ><span style="padding-left: 4px">({{ ite.label }})</span>
                  </div>
                </div>
              </div>
              <!-- 表格字段 -->
              <div class="flex-row-spe" v-else-if="item.valueMethod == 'xttable'">
                <div class="spe-title">
                  <!-- 大标题 -->
                  {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                </div>

                <div
                  class="spe-item"
                  :class="{
                    'spe-item-active': ite.checked,
                    'no-span': ['upload', 'xtaudio', 'xtfj', 'xtvideo', 'xtsjjt'].includes(ite.valueMethod)
                  }"
                  v-for="(ite, idx) in item.attribution.children"
                  :key="idx"
                  @click="chooseField(item, ite)"
                >
                  <!-- 身份证识别中的某个字段 -->
                  <div>
                    <span>{{ ite.fieldName }}</span
                    ><span style="padding-left: 4px">({{ ite.fieldCn }})</span>
                  </div>
                </div>
              </div>
              <!-- 单个字段的情况 -->
              <div
                v-else
                class="flex-row"
                :class="{
                  'flex-active': item.id == checkedField.id,
                  'no-span': ['upload', 'xtaudio', 'xtfj', 'xtvideo', 'xtsjjt'].includes(item.valueMethod)
                }"
                @click="chooseField(item)"
              >
                <div class="label-field">
                  <!-- 属性组字段 -->
                  <div>{{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})</div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <template v-slot:footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitField">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import SvgIcon from '@/components/SvgIcon/index.vue';
import { selectRules } from '@/api/modal';
import authImg from '@/components/authImg/index.vue';

//--- 定义Props ---
interface Props {
  labelDialog: boolean; // 打开弹框
  isFileName?: boolean; // 是不是设置导出报告和文件名的表达式
  isYsName?: boolean; // 判断是不是字段映射的别名
  moduleId?: string | number; // 模块id
}

const props = withDefaults(defineProps<Props>(), {
  labelDialog: false,
  isFileName: false,
  isYsName: false,
  moduleId: ''
});
// 定义emit
const emit = defineEmits<{
  (e: 'submitField', fieldName: string, linkId: number): void;
  (e: 'handleCloseLabel'): void;
}>();

// 定义计算属性
const labelDialogCopy = computed(() => props.labelDialog);

// ---watch---
watch(
  labelDialogCopy,
  (newVal) => {
    if (newVal) {
      init();
    }
  },
  { deep: true }
);

// --- 定义变量 ---
const baseUrl = import.meta.env.VITE_BASE_URL;
const treeList = ref([]); //要素树
const defaultProps = reactive({
  children: 'list',
  label: 'typeName'
});
const attrbutionGroup = ref([]); //属性组
const fieldList = ref([]); //字段组
let checkedYS: any = reactive({}); //字段对应的要素编码
let checkedAtt: any = reactive({}); //字段对应的属性组编码
let checkedField: any = reactive({}); //图片类型 顺带赋值映射字段中文名
//身份证识别
const idCardScanList = reactive([
  { label: '姓名', value: 0, enName: 'xm', checked: false },
  { label: '性别', value: 1, enName: 'xb', checked: false },
  { label: '民族', value: 2, enName: 'mz', checked: false },
  { label: '出生日期', value: 3, enName: 'csrq', checked: false },
  { label: '住址', value: 4, enName: 'zz', checked: false },
  { label: '身份证号', value: 5, enName: 'sfzh', checked: false },
  { label: '签发机关', value: 6, enName: 'qfjg', checked: false },
  { label: '有效期限', value: 7, enName: 'yxqx', checked: false }
]);
const yhkTypeList = reactive([
  { value: 0, label: '银行卡名称', enName: 'yhkName', checked: false },
  { value: 1, label: '银行卡卡号', enName: 'yhkNum', checked: false },
  { value: 2, label: '银行卡类型', enName: 'yhkType', checked: false }
]);
const oldFieldName = ref(''); //当前选中的原始身份证识别英文字段名
const DWSBOptions = reactive([
  { value: 0, label: '名称', enName: 'dwmc', checked: false },
  { value: 1, label: '图片', enName: 'dwtp', checked: false },
  { value: 2, label: '类型', enName: 'dwlx', checked: false },
  { value: 3, label: '描述', enName: 'dwms', checked: false }
]);
const ZWSBOptions = reactive([
  { value: 0, label: '名称', enName: 'dwmc', checked: false },
  { value: 1, label: '图片', enName: 'dwtp', checked: false },
  { value: 2, label: '类型', enName: 'dwlx', checked: false },
  { value: 3, label: '描述', enName: 'dwms', checked: false }
]);

// --- 定义ref ---
const treeRef = ref(null);

// --- 定义方法 ---
const init = () => {
  selectRules({ moduleId: props.moduleId }).then((res) => {
    if (res.code == 200) {
      treeList.value = res.data;
      attrbutionGroup.value = res.data[0].fieldGroupModelList;
      checkedYS = res.data[0];
      fieldList.value = [];
      nextTick(() => {
        // selectId：绑定的 node-key
        treeRef.value.setCurrentKey(res.data[0].id);
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 改变选中属性组
 * @param item 属性组
 */
const changeAtt = (item) => {
  attrbutionGroup.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  checkedAtt = item;
  // 当选中属性组的时候，给属性组中的字段添加checked 属性，方便来判断是否选中该字段
  if (item.fieldModelList && item.fieldModelList.length > 0) {
    const fieldModelList = item.fieldModelList.map((field) => {
      if (field.valueMethod == 'idCardScan') {
        idCardScanList.forEach((v) => {
          v.checked = false;
        });
      } else if (field.valueMethod == 'xtBankCard') {
        yhkTypeList.forEach((v) => {
          v.checked = false;
        });
      } else if (field.valueMethod == 'xtdwsb') {
        DWSBOptions.forEach((v) => {
          v.checked = false;
        });
      } else if (field.valueMethod == 'xtzwsb') {
        ZWSBOptions.forEach((v) => {
          v.checked = false;
        });
      } else if (field.valueMethod == 'xttable') {
        if (field.attribution && field.attribution.children.length > 0) {
          field.attribution.children.forEach((v) => {
            v.checked = false;
          });
        }
      } else {
        field.checked = false;
      }
      return field;
    });
    fieldList.value = fieldModelList;
  }
};

/**
 * 提交选中的快捷表达式
 */
const submitField = () => {
  if (
    checkedField.valueMethod == 'idCardScan' ||
    checkedField.valueMethod == 'xtBankCard' ||
    checkedField.valueMethod == 'xtzwsb' ||
    checkedField.valueMethod == 'xtdwsb'
  ) {
    emit('submitField', `${checkedField.fieldName}_${checkedField.checkedIndex}`, checkedAtt.linkId);
    console.log('submitField', `${checkedField.fieldName}_${checkedField.checkedIndex}`, checkedAtt.linkId);
  } else {
    emit('submitField', `${checkedField.fieldName}`, checkedAtt.linkId);
    console.log('submitField', `${checkedField.fieldName}`, checkedAtt.linkId);
  }
};

/**
 * 处理共享组的表达式
 * @param tree 要素树
 * @param group 属性组
 * @param field 字段
 * @param str 字符串
 */
const handleShareExpressionText = (tree, group, field, str) => {
  let expression = '';
  let type = '面';
  if (group.groupScope == 1 && group.ruleAttribution && group.ruleAttribution.type == 'graphicalPoint') {
    type = '点';
  } else if (group.groupScope == 1 && group.ruleAttribution && group.ruleAttribution.type == 'graphicalLine') {
    type = '线';
  } else {
    type = '面';
  }
  if (field.valueMethod == 'idCardScan' || field.valueMethod == 'xtBankCard' || field.valueMethod == 'xtzwsb' || field.valueMethod == 'xtdwsb') {
    if (props.isFileName) {
      //身份证识别特殊处理
      expression = `#{getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}_${field.checkedIndex}")${str}}`;
    } else if (props.isYsName) {
      expression = `getSpeFieldColStr("${tree.typeName}",0,"${group.typeName}","${field.fieldName}_${field.checkedIndex}")`;
    } else {
      //身份证识别特殊处理
      expression = `getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}_${field.checkedIndex}")${str}`;
    }
  } else if (field.valueMethod == 'xttable') {
    if (props.isFileName) {
      //表格特殊处理
      expression = `#{getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}").getTableData(0,"${field.checkedIndex}")}`;
    } else if (props.isYsName) {
      expression = `getFactorField("${tree.typeName}",0,"${group.typeName}","${field.fieldName}").getColFieldStr(${field.checkedIndex}")`;
    } else {
      //表格特殊处理
      expression = `getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}").getTableData(0,"${field.checkedIndex}")`;
    }
  } else {
    if (props.isFileName) {
      //文件名表达式要加#{}
      expression = `#{getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}")${str}}`;
    } else if (props.isYsName) {
      expression = `getSpeFieldColStr("${tree.typeName}",0,"${group.typeName}","${field.fieldName}")`;
    } else {
      expression = `getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}")${str}`;
    }
  }
  return expression;
};

/**
 * 处理跟节点的表达式
 * @param tree 要素树
 * @param group 属性组
 * @param field 字段
 * @param str 字符串
 */
const handleRootExpressionText = (tree, group, field, str) => {
  let expression = '';
  if (field.valueMethod == 'idCardScan' || field.valueMethod == 'xtBankCard' || field.valueMethod == 'xtzwsb' || field.valueMethod == 'xtdwsb') {
    if (props.isFileName) {
      //身份证识别特殊处理
      expression = `#{getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}_${field.checkedIndex}")${str}}`;
    } else {
      //身份证识别特殊处理
      expression = `getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}_${field.checkedIndex}")${str}`;
    }
  } else if (field.valueMethod == 'xttable') {
    if (props.isFileName) {
      //表格特殊处理
      expression = `#{getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}").getTableData(0,"${field.checkedIndex}")}`;
    } else {
      //表格特殊处理
      expression = `getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}").getTableData(0,"${field.checkedIndex}")`;
    }
  } else {
    if (props.isFileName) {
      //文件名表达式要加#{}
      expression = `#{getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}")${str}}`;
    } else {
      expression = `getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}")${str}`;
    }
  }
  return expression;
};

/**
 * 处理非根节点的表达式
 * @param tree 要素树
 * @param group 属性组
 * @param field 字段
 * @param str 字符串
 */
const handleNomalExpressionText = (tree, group, field, str) => {
  let expression = '';
  // getFlatField("宗地",0,"宗地信息",0,"zdmc").toStr()
  if (field.valueMethod == 'idCardScan' || field.valueMethod == 'xtBankCard' || field.valueMethod == 'xtzwsb' || field.valueMethod == 'xtdwsb') {
    if (props.isFileName) {
      //身份证识别特殊处理
      expression = `#{getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}_${field.checkedIndex}")${str}}`;
    } else {
      //身份证识别特殊处理
      expression = `getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}_${field.checkedIndex}")${str}`;
    }
  } else if (field.valueMethod == 'xttable') {
    if (props.isFileName) {
      //表格特殊处理
      expression = `#{getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}").getTableData(0,"${field.checkedIndex}")}`;
    } else {
      //表格特殊处理
      expression = `getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}").getTableData(0,"${field.checkedIndex}")`;
    }
  } else {
    if (props.isFileName) {
      //文件名表达式要加#{}
      expression = `#{getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}")${str}}`;
    } else {
      expression = `getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}")${str}`;
    }
  }
  return expression;
};

/**
 * 选中某个字段
 * @param item 字段
 * @param ite 字段
 */
const chooseField = (item, ite?: any) => {
  item.checkedIndex = '';
  // 当一个族中同时存在身份证，动植物识别 选中第一个之后 在选第二个不会把值清空 故 重新勾选的时候全部置为false
  idCardScanList.forEach((v) => {
    v.checked = false;
  });
  yhkTypeList.forEach((v) => {
    v.checked = false;
  });
  DWSBOptions.forEach((v) => {
    v.checked = false;
  });
  ZWSBOptions.forEach((v) => {
    v.checked = false;
  });
  // 特殊情况  有表格和有数据的时候怎么办。
  if (item.valueMethod == 'xttable' && item.attribution && item.attribution.children.length > 0) {
    item.attribution.children.forEach((v) => {
      v.checked = false;
    });
  }

  if (item.valueMethod == 'idCardScan') {
    idCardScanList.forEach((v) => {
      v.checked = false;
    });
    ite.checked = true;
    item.checkedIndex = ite.value;
    checkedField = Object.assign(checkedField, item);
  } else if (item.valueMethod == 'xtBankCard') {
    yhkTypeList.forEach((v) => {
      v.checked = false;
    });
    ite.checked = true;
    item.checkedIndex = ite.value;
    checkedField = Object.assign(checkedField, item);
  } else if (item.valueMethod == 'xtdwsb') {
    DWSBOptions.forEach((v) => {
      v.checked = false;
    });
    ite.checked = true;
    item.checkedIndex = ite.value;
    checkedField = Object.assign(checkedField, item);
  } else if (item.valueMethod == 'xtzwsb') {
    ZWSBOptions.forEach((v) => {
      v.checked = false;
    });
    ite.checked = true;
    item.checkedIndex = ite.value;
    checkedField = Object.assign(checkedField, item);
  } else if (item.valueMethod == 'xttable') {
    if (item.attribution && item.attribution.children.length > 0) {
      item.attribution.children.forEach((v) => {
        v.checked = false;
      });
    }
    ite.checked = true;
    item.checkedIndex = ite.fieldName;
    checkedField = Object.assign(checkedField, item);
  } else {
    // 当身份证识别中有签名和指纹的情况，选择身份证识别中的1/10的字段，签名和指纹也还可以选中
    // 身份证识别和字段只能选一个字段 所以循环置为false
    idCardScanList.forEach((v) => {
      v.checked = false;
    });
    yhkTypeList.forEach((v) => {
      v.checked = false;
    });
    // 去过是其他属性组的字段
    fieldList.value.forEach((v) => {
      v.checked = false;
    });
    item.checked = true;
    checkedField = Object.assign(checkedField, item);
  }
};

const handleNodeClick = (data) => {
  attrbutionGroup.value = data.fieldGroupModelList;
  checkedYS.value = data;
  fieldList.value = [];
};
const handleClose = () => {
  emit('handleCloseLabel');
};
</script>
<style lang="scss" scoped>
.el-color-dropdown__link-btn {
  display: none;
}
</style>
<style lang="scss" scoped>
.dialog-box {
  height: 300px;
  border: 1px solid rgba(219, 231, 238, 1);
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  .left {
    flex: 2;
  }
  .center {
    flex: 2;
    border-left: 1px solid rgba(219, 231, 238, 1);
  }
  .right {
    flex: 3;
    border-left: 1px solid rgba(219, 231, 238, 1);
  }
  .title-div {
    width: 100%;
    height: 37px;
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
    .normal-sapn {
      margin-left: 20px;
    }
  }
  .content {
    height: calc(100% - 37px);
    padding: 0px 8px;
    width: calc(100% - 16px);
    margin-left: 8px;
    overflow: auto;
    & :deep(.el-tree-node__content) {
      height: 32px;
      font-size: 12px;
    }
    .empty-span {
      color: #909399;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
    .flex-row {
      height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      .label {
        font-size: 12px;
        padding-left: 12px;
        width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .label-field {
        font-size: 12px;
        padding-left: 12px;
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
      }
      .ico {
        padding-right: 8px;
      }
    }
    .flex-row:hover {
      background-color: #f5f7fa;
    }
    .flex-active {
      background: #edf4fb;
    }
    .no-span {
      color: #d3d3d3 !important;
      cursor: not-allowed;
    }
    .flex-row-spe {
      height: auto;
      .spe-title {
        color: #d3d3d3;
        padding-left: 12px;
        cursor: not-allowed;
      }
      .spe-item {
        height: 32px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        font-size: 12px;
        padding-left: 24px;
      }
      .spe-item:hover {
        background: #edf4fb;
      }
      .spe-item-active {
        background: #edf4fb;
      }
    }
    .tree-div {
      height: calc(100% - 49px);
      overflow: auto;
      width: 100%;
      color: #333333;
      .tree-row {
        height: 44px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        width: 100%;
        // padding: 0px 16px;
        .tree-row-left {
          display: flex;
          flex-direction: row;
          align-items: center;
          .tree-img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            background: #fff;
          }
          .svg-item {
            width: 16px;
            height: 16px;
            // margin-right: 8px;
            // color: #333;
            vertical-align: middle;
          }
        }
        .tree-row-handle {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 2px;
        }
        .tree-row-handle:hover {
          background: var(--current-color);
          color: #fff;
        }
      }
      .active-tree {
        background: var(--current-color);
        color: #fff;
      }
    }
  }
  /*滚动条样式*/
  .content::-webkit-scrollbar {
    width: 4px;
  }
  .content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  .content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}
</style>
