<!-- 组件注册测试页面 -->
<template>
  <div class="test-container">
    <h2>组件注册测试</h2>
    
    <div class="test-section">
      <h3>组件库配置测试</h3>
      <p>检查散点地图组件是否在组件库中正确注册</p>
      
      <div class="component-info">
        <h4>散点地图组件信息</h4>
        <ul>
          <li><strong>组件名称</strong>: {{ componentInfo.name }}</li>
          <li><strong>组件Key</strong>: {{ componentInfo.cptKey }}</li>
          <li><strong>配置Key</strong>: {{ componentInfo.cptOptionKey }}</li>
          <li><strong>默认宽度</strong>: {{ componentInfo.width }}px</li>
          <li><strong>默认高度</strong>: {{ componentInfo.height }}px</li>
          <li><strong>图标</strong>: {{ componentInfo.icon }}</li>
        </ul>
      </div>
      
      <div class="registration-status">
        <h4>注册状态检查</h4>
        <div class="status-item">
          <span class="label">主组件注册:</span>
          <span :class="['status', mainComponentRegistered ? 'success' : 'error']">
            {{ mainComponentRegistered ? '✅ 已注册' : '❌ 未注册' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">配置组件注册:</span>
          <span :class="['status', optionComponentRegistered ? 'success' : 'error']">
            {{ optionComponentRegistered ? '✅ 已注册' : '❌ 未注册' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">组件库配置:</span>
          <span :class="['status', inComponentLibrary ? 'success' : 'error']">
            {{ inComponentLibrary ? '✅ 已配置' : '❌ 未配置' }}
          </span>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h3>组件渲染测试</h3>
      <p>测试组件是否能正常渲染</p>
      
      <div class="component-test">
        <h4>散点地图组件</h4>
        <div class="chart-container">
          <component 
            :is="componentInfo.cptKey" 
            :width="400" 
            :height="300" 
            :option="testOption"
            v-if="mainComponentRegistered"
          />
          <div v-else class="error-message">
            组件未注册，无法渲染
          </div>
        </div>
      </div>
      
      <div class="option-test">
        <h4>配置组件</h4>
        <div class="option-container">
          <component 
            :is="componentInfo.cptOptionKey" 
            :attribute="testOption.attribute"
            @update-attribute="updateAttribute"
            v-if="optionComponentRegistered"
          />
          <div v-else class="error-message">
            配置组件未注册，无法渲染
          </div>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h3>组件库数据</h3>
      <p>显示组件在组件库中的完整配置</p>
      <pre>{{ JSON.stringify(componentLibraryData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance } from 'vue';
import cptOptions from '../../../utils/options';

defineOptions({
  name: 'component-registration-test'
});

const instance = getCurrentInstance();

// 组件信息
const componentInfo = {
  name: '散点地图',
  cptKey: 'cpt-chart-geo-scatter',
  cptOptionKey: 'cpt-chart-geo-scatter-option',
  width: 800,
  height: 600,
  icon: 'scatter'
};

// 测试选项
const testOption = ref({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify([
      { name: '北京', lng: 116.46, lat: 39.92, value: 150 },
      { name: '上海', lng: 121.48, lat: 31.22, value: 200 },
      { name: '广州', lng: 113.23, lat: 23.16, value: 120 }
    ])
  },
  attribute: {
    titleText: '测试散点地图',
    map: 'china',
    scatterType: 'scatter',
    showScatterLabel: true,
    scatterColor: '#ff6b6b'
  }
});

// 检查组件注册状态
const mainComponentRegistered = computed(() => {
  return !!instance?.appContext.app.component(componentInfo.cptKey);
});

const optionComponentRegistered = computed(() => {
  return !!instance?.appContext.app.component(componentInfo.cptOptionKey);
});

// 检查组件库配置
const componentLibraryData = computed(() => {
  const mapCategory = cptOptions.find(category => category.name === '地图');
  return mapCategory?.children.find(component => component.cptKey === componentInfo.cptKey);
});

const inComponentLibrary = computed(() => {
  return !!componentLibraryData.value;
});

// 更新属性
const updateAttribute = (newAttribute: any) => {
  testOption.value.attribute = newAttribute;
};

// 页面加载时输出调试信息
onMounted(() => {
  console.log('=== 组件注册测试 ===');
  console.log('主组件注册状态:', mainComponentRegistered.value);
  console.log('配置组件注册状态:', optionComponentRegistered.value);
  console.log('组件库配置状态:', inComponentLibrary.value);
  console.log('组件库数据:', componentLibraryData.value);
  console.log('所有已注册组件:', Object.keys(instance?.appContext.app._component || {}));
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #409eff;
}

.component-info ul {
  list-style: none;
  padding: 0;
}

.component-info li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.registration-status {
  margin: 20px 0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.label {
  font-weight: bold;
}

.status.success {
  color: #67c23a;
}

.status.error {
  color: #f56c6c;
}

.chart-container {
  width: 400px;
  height: 300px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 10px 0;
  background: #fff;
}

.option-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fff;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #f56c6c;
  font-weight: bold;
}

pre {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  border: 1px solid #ddd;
  max-height: 300px;
  overflow-y: auto;
}

h2, h3, h4 {
  color: #333;
}

h3 {
  margin-top: 0;
}
</style>
