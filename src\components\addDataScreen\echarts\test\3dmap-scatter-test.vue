<!-- 3D地图散点图测试页面 -->
<template>
  <div class="test-container">
    <h2>3D地图散点图测试</h2>
    <div class="chart-wrapper">
      <cpt-chart-3dmap 
        :width="800" 
        :height="600" 
        :option="testOption" 
      />
    </div>
    <div class="test-info">
      <h3>测试说明</h3>
      <p>此页面用于测试3D地图散点功能是否正常工作</p>
      <ul>
        <li>地图应该显示贵州省3D地图</li>
        <li>地图上应该显示3个红色散点（渲染在地图表面）</li>
        <li>散点会随着地图的旋转、缩放而变化</li>
        <li>鼠标悬停散点应该显示提示信息</li>
        <li>散点应该有标签显示名称</li>
        <li>散点是叠加在3D地图上的，不是独立的坐标系</li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChart3dmap from '../cpt-chart-3dmap.vue';

defineOptions({
  name: '3dmap-scatter-test'
});

// 测试数据
const testOption = {
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify({
      mapData: [
        { 'name': '毕节市', 'value': 105 },
        { 'name': '遵义市', 'value': 41 },
        { 'name': '铜仁市', 'value': 30 },
        { 'name': '贵阳市', 'value': 52 },
        { 'name': '黔东南苗族侗族自治州', 'value': 66 },
        { 'name': '黔南布依族苗族自治州', 'value': 12 },
        { 'name': '安顺市', 'value': 99 },
        { 'name': '六盘水市', 'value': 72 },
        { 'name': '黔西南布依族苗族自治州', 'value': 82 }
      ],
      scatterData: [
        { 'name': '贵阳数据中心', 'lng': 106.713478, 'lat': 26.578343, 'value': 150 },
        { 'name': '遵义分站', 'lng': 107.297104, 'lat': 27.725654, 'value': 89 },
        { 'name': '毕节监测点', 'lng': 105.300492, 'lat': 27.302612, 'value': 76 }
      ]
    })
  },
  attribute: {
    map: 'areaGuizhou',
    titleText: '贵州省3D地图散点测试',
    mapHeight: 4,
    distance: 120,
    alpha: 45,
    beta: -20,
    isShowLabel: true,
    labelColor: '#fff',
    fontSize: 12,
    mapBackgroundColor: '#4389ED',
    borderWidth: 2,
    borderColor: '#61CFF8',
    isShowLed: true,
    ledColor: '#fff',
    mapLedColor: '#007EE8',
    mapLedBorderWidth: 3,
    mapLedBorderColor: '#6BECF5',
    
    // 散点配置
    showScatter: true,
    scatterName: '测试散点',
    scatterSymbol: 'pin',
    scatterSize: [8, 25],
    scatterColor: '#FF6B6B',
    scatterOpacity: 0.9,
    scatterEmphasisColor: '#FF4757',
    showScatterLabel: true,
    scatterLabelColor: '#fff',
    scatterLabelSize: 12
  }
};
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #f5f5f5;
}

.test-info {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #4389ED;
}

.test-info h3 {
  margin-top: 0;
  color: #333;
}

.test-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-info li {
  margin: 5px 0;
  color: #666;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}
</style>
