<!-- 地图渲染调试页面 -->
<template>
  <div class="debug-container">
    <h2>地图底图渲染调试</h2>
    
    <div class="test-grid">
      <!-- China地图测试 -->
      <div class="test-item">
        <h3>China地图 (压缩格式)</h3>
        <div class="chart-container">
          <div id="china-chart" style="width: 100%; height: 100%;"></div>
        </div>
        <div class="status">
          状态: <span :class="chinaStatus.class">{{ chinaStatus.text }}</span>
        </div>
      </div>
      
      <!-- Bijie地图测试 -->
      <div class="test-item">
        <h3>Bijie地图 (GeoJSON格式)</h3>
        <div class="chart-container">
          <div id="bijie-chart" style="width: 100%; height: 100%;"></div>
        </div>
        <div class="status">
          状态: <span :class="bijieStatus.class">{{ bijieStatus.text }}</span>
        </div>
      </div>
      
      <!-- Guizhou地图测试 -->
      <div class="test-item">
        <h3>Guizhou地图 (对比)</h3>
        <div class="chart-container">
          <div id="guizhou-chart" style="width: 100%; height: 100%;"></div>
        </div>
        <div class="status">
          状态: <span :class="guizhouStatus.class">{{ guizhouStatus.text }}</span>
        </div>
      </div>
      
      <!-- 纯毕节地图测试 -->
      <div class="test-item">
        <h3>纯毕节地图 (无散点)</h3>
        <div class="chart-container">
          <div id="pure-bijie-chart" style="width: 100%; height: 100%;"></div>
        </div>
        <div class="status">
          状态: <span :class="pureBijieStatus.class">{{ pureBijieStatus.text }}</span>
        </div>
      </div>
    </div>
    
    <div class="controls">
      <el-button @click="refreshAllMaps">刷新所有地图</el-button>
      <el-button @click="testMapData">测试地图数据</el-button>
      <el-button @click="showBounds">显示边界信息</el-button>
    </div>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <h4>China地图信息</h4>
          <pre>{{ chinaInfo }}</pre>
        </div>
        <div class="info-item">
          <h4>Bijie地图信息</h4>
          <pre>{{ bijieInfo }}</pre>
        </div>
      </div>
    </div>
    
    <div class="console-output">
      <h3>控制台输出</h3>
      <div class="logs">
        <div v-for="(log, index) in logs" :key="index" :class="['log', log.type]">
          <span class="time">{{ log.time }}</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';

defineOptions({
  name: 'map-render-debug'
});

const logs = ref<Array<{type: string, message: string, time: string}>>([]);
const chinaStatus = ref({ class: 'pending', text: '等待测试' });
const bijieStatus = ref({ class: 'pending', text: '等待测试' });
const guizhouStatus = ref({ class: 'pending', text: '等待测试' });
const pureBijieStatus = ref({ class: 'pending', text: '等待测试' });

const chinaInfo = ref('');
const bijieInfo = ref('');

let chinaChart: any = null;
let bijieChart: any = null;
let guizhouChart: any = null;
let pureBijieChart: any = null;

const addLog = (type: string, message: string) => {
  logs.value.push({
    type,
    message,
    time: new Date().toLocaleTimeString()
  });
  console.log(`[${type.toUpperCase()}] ${message}`);
};

const createChinaMap = () => {
  if (!chinaChart) return;
  
  addLog('info', '创建China地图...');
  
  try {
    const option = {
      title: { text: 'China地图', left: 'center' },
      geo: {
        map: 'china',
        roam: true,
        zoom: 1,
        center: [104, 35],
        itemStyle: {
          areaColor: '#e0e0e0',
          borderColor: '#333',
          borderWidth: 1
        },
        emphasis: {
          itemStyle: {
            areaColor: '#c0c0c0'
          }
        }
      }
    };
    
    chinaChart.setOption(option);
    chinaStatus.value = { class: 'success', text: '渲染成功' };
    addLog('success', 'China地图渲染成功');
  } catch (error) {
    chinaStatus.value = { class: 'error', text: '渲染失败' };
    addLog('error', `China地图渲染失败: ${error}`);
  }
};

const createBijieMap = () => {
  if (!bijieChart) return;
  
  addLog('info', '创建Bijie地图...');
  
  try {
    const option = {
      title: { text: 'Bijie地图', left: 'center' },
      geo: {
        map: 'bijie',
        roam: true,
        zoom: 1,
        center: [105.5, 27.0],
        itemStyle: {
          areaColor: '#e0e0e0',
          borderColor: '#333',
          borderWidth: 2
        },
        emphasis: {
          itemStyle: {
            areaColor: '#c0c0c0'
          }
        },
        label: {
          show: true,
          fontSize: 10,
          color: '#333'
        }
      }
    };
    
    bijieChart.setOption(option);
    bijieStatus.value = { class: 'success', text: '渲染成功' };
    addLog('success', 'Bijie地图渲染成功');
  } catch (error) {
    bijieStatus.value = { class: 'error', text: '渲染失败' };
    addLog('error', `Bijie地图渲染失败: ${error}`);
  }
};

const createGuizhouMap = () => {
  if (!guizhouChart) return;
  
  addLog('info', '创建Guizhou地图...');
  
  try {
    const option = {
      title: { text: 'Guizhou地图', left: 'center' },
      geo: {
        map: 'guizhou',
        roam: true,
        zoom: 1,
        center: [106.7, 26.6],
        itemStyle: {
          areaColor: '#e0e0e0',
          borderColor: '#333',
          borderWidth: 1
        }
      }
    };
    
    guizhouChart.setOption(option);
    guizhouStatus.value = { class: 'success', text: '渲染成功' };
    addLog('success', 'Guizhou地图渲染成功');
  } catch (error) {
    guizhouStatus.value = { class: 'error', text: '渲染失败' };
    addLog('error', `Guizhou地图渲染失败: ${error}`);
  }
};

const createPureBijieMap = () => {
  if (!pureBijieChart) return;
  
  addLog('info', '创建纯Bijie地图（无散点）...');
  
  try {
    const option = {
      title: { text: '纯Bijie地图', left: 'center' },
      geo: {
        map: 'bijie',
        roam: true,
        zoom: 2,
        center: [105.5, 27.0],
        itemStyle: {
          areaColor: '#f0f0f0',
          borderColor: '#666',
          borderWidth: 2
        },
        emphasis: {
          itemStyle: {
            areaColor: '#d0d0d0'
          }
        },
        label: {
          show: true,
          fontSize: 12,
          color: '#000',
          fontWeight: 'bold'
        }
      }
    };
    
    pureBijieChart.setOption(option);
    pureBijieStatus.value = { class: 'success', text: '渲染成功' };
    addLog('success', '纯Bijie地图渲染成功');
  } catch (error) {
    pureBijieStatus.value = { class: 'error', text: '渲染失败' };
    addLog('error', `纯Bijie地图渲染失败: ${error}`);
  }
};

const refreshAllMaps = () => {
  addLog('info', '刷新所有地图...');
  createChinaMap();
  createBijieMap();
  createGuizhouMap();
  createPureBijieMap();
};

const testMapData = () => {
  addLog('info', '测试地图数据...');
  
  const chinaMap = echarts.getMap('china');
  const bijieMap = echarts.getMap('bijie');
  
  if (chinaMap) {
    chinaInfo.value = JSON.stringify({
      type: 'china',
      features: chinaMap.geoJson?.features?.length,
      hasEncodeOffsets: !!chinaMap.geoJson?.features?.[0]?.geometry?.encodeOffsets,
      firstFeature: chinaMap.geoJson?.features?.[0]?.properties
    }, null, 2);
  }
  
  if (bijieMap) {
    bijieInfo.value = JSON.stringify({
      type: 'bijie',
      features: bijieMap.geoJson?.features?.length,
      hasCoordinates: !!bijieMap.geoJson?.features?.[0]?.geometry?.coordinates,
      firstFeature: bijieMap.geoJson?.features?.[0]?.properties,
      bounds: calculateBounds(bijieMap.geoJson)
    }, null, 2);
  }
};

const calculateBounds = (geoJson: any) => {
  if (!geoJson?.features) return null;
  
  let minLng = Infinity, maxLng = -Infinity;
  let minLat = Infinity, maxLat = -Infinity;
  
  geoJson.features.forEach((feature: any) => {
    const center = feature.properties?.center;
    if (center && Array.isArray(center)) {
      minLng = Math.min(minLng, center[0]);
      maxLng = Math.max(maxLng, center[0]);
      minLat = Math.min(minLat, center[1]);
      maxLat = Math.max(maxLat, center[1]);
    }
  });
  
  return {
    lng: [minLng, maxLng],
    lat: [minLat, maxLat],
    center: [(minLng + maxLng) / 2, (minLat + maxLat) / 2]
  };
};

const showBounds = () => {
  const bijieMap = echarts.getMap('bijie');
  if (bijieMap) {
    const bounds = calculateBounds(bijieMap.geoJson);
    addLog('info', `毕节地图边界: 经度 ${bounds?.lng[0]?.toFixed(3)} ~ ${bounds?.lng[1]?.toFixed(3)}, 纬度 ${bounds?.lat[0]?.toFixed(3)} ~ ${bounds?.lat[1]?.toFixed(3)}`);
    addLog('info', `建议中心点: [${bounds?.center[0]?.toFixed(3)}, ${bounds?.center[1]?.toFixed(3)}]`);
  }
};

onMounted(() => {
  chinaChart = echarts.init(document.getElementById('china-chart'));
  bijieChart = echarts.init(document.getElementById('bijie-chart'));
  guizhouChart = echarts.init(document.getElementById('guizhou-chart'));
  pureBijieChart = echarts.init(document.getElementById('pure-bijie-chart'));
  
  addLog('info', '页面初始化完成');
  testMapData();
  refreshAllMaps();
});

onUnmounted(() => {
  [chinaChart, bijieChart, guizhouChart, pureBijieChart].forEach(chart => {
    if (chart) chart.dispose();
  });
});
</script>

<style scoped>
.debug-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.test-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 20px 0;
}

.test-item {
  border: 2px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  background: #f9f9f9;
}

.chart-container {
  width: 100%;
  height: 300px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: #fff;
  margin: 10px 0;
}

.status {
  text-align: center;
  font-weight: bold;
}

.status .success {
  color: #52c41a;
}

.status .error {
  color: #ff4d4f;
}

.status .pending {
  color: #1890ff;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 20px 0;
}

.info-item pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 11px;
  max-height: 200px;
  overflow-y: auto;
}

.logs {
  background: #000;
  color: #fff;
  padding: 15px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log {
  margin: 2px 0;
  display: flex;
  gap: 10px;
}

.log.error { color: #ff6b6b; }
.log.success { color: #51cf66; }
.log.info { color: #74c0fc; }

.time {
  color: #adb5bd;
  min-width: 80px;
}

h2, h3, h4 {
  color: #333;
  margin-top: 0;
}
</style>
