<!-- 点要素/线要素的设置内容 -->
<template>
  <div>
    <el-dialog :title="settingTitle" v-model="dialogVisible" @open="getOpen" @closed="handleClose" :close-on-click-modal="false">
      <!-- <el-button
        plain
        icon="el-icon-star-off"
        size="mini"
      >要素样式</el-button> -->
      <div style="display: flex; justify-content: flex-start; align-items: center">
        <!-- v-if="isShowFY" -->
        <div class="isfy">
          <div style="margin-right: 8px">是否放样</div>
          <el-checkbox :label="1" :false-label="0" v-model="checkedTreeMsg.lofting" @change="handleLofting"></el-checkbox>
        </div>
      </div>
      <div class="attribute-group-main">
        <div class="item-title">
          <div class="handle-title">
            <span class="text">属性设置</span>
          </div>
        </div>
        <el-row style="margin: 24px 16px 4px">
          <el-col>
            <el-button type="primary" plain icon="el-icon-plus" @click="handleOpenGroup('新增属性')">新增属性</el-button>
          </el-col>
        </el-row>

        <div class="attribute-main">
          <div class="attribite-list">
            <el-row class="attribite-item" v-show="isShowGroupList(item)" v-for="(item, index) in checkedTreeMsg.fieldGroupModelList" :key="index">
              <el-col :span="7" style="display: flex; align-items: center">
                <div
                  v-if="item.iconUrl && item.iconUrl.substring(item.iconUrl.lastIndexOf('_') + 1) == 'blob'"
                  style="display: flex; align-items: center"
                >
                  <authImg :authSrc="`${baseUrl}${item.iconUrl}?att=1`" :width="'20px'" :height="'20px'" style="margin-right: 8px" />
                </div>
                <div v-else style="margin-top: 10px">
                  <svg-icon class-name="svg-item" :icon-class="item.iconUrl" />
                </div>
                <div class="item-type-name">{{ item.typeName }}</div>
              </el-col>
              <el-col :span="8">
                <div class="item-remark">{{ item.remark }}</div>
              </el-col>
              <el-col :span="9" style="display: flex; flex-wrap: nowrap; justify-content: flex-end">
                <el-tooltip class="item" effect="dark" content="字段" placement="top">
                  <div class="text-btn" @click="handleOpenInfoField(item)">
                    <svg-icon class-name="svg-item" icon-class="more_field" />
                  </div>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="修改" placement="top">
                  <div class="text-btn" @click="handleOpenGroup('修改属性', item)"><svg-icon class-name="svg-item" icon-class="more_edit" /></div>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="删除" placement="top">
                  <div class="text-btn" @click="handleDeleteGroup(item)"><svg-icon class-name="svg-item" icon-class="more_delete" /></div>
                </el-tooltip>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <div class="acquisition-owner-main">
        <div class="item-title">
          <div class="handle-title">
            <span class="text">特殊属性选择</span>
          </div>
        </div>
        <div class="attribute-main" v-if="ownerTypeList.length > 0">
          <div class="attribite-list">
            <el-row class="attribite-item" v-for="(item, index) in ownerTypeList" :key="index">
              <el-col :span="7" style="display: flex; align-items: center">
                <div
                  v-if="item.iconUrl && item.iconUrl.substring(item.iconUrl.lastIndexOf('_') + 1) == 'blob'"
                  style="display: flex; align-items: center"
                >
                  <authImg :authSrc="`${baseUrl}${item.iconUrl}?att=1`" :width="'20px'" :height="'20px'" style="margin-right: 8px" />
                </div>
                <div v-else style="display: flex; justify-content: center; align-items: center">
                  <svg-icon class-name="svg-item" :icon-class="item.iconUrl" :style="{ color: idList.includes(item.id) ? '#161d26' : '#8291a9' }" />
                </div>
                <div class="item-type-name" :style="{ color: idList.includes(item.id) ? '#161d26' : '#8291a9' }">{{ item.typeName }}</div>
              </el-col>
              <el-col :span="8">
                <div class="item-remark" :style="{ color: idList.includes(item.id) ? '#161d26' : '#8291a9' }">{{ item.remark }}</div>
              </el-col>
              <el-col :span="9" style="display: flex; flex-wrap: nowrap; justify-content: flex-end">
                <el-checkbox-group v-model="idList">
                  <el-checkbox :value="item.id" @change="handleCheckedOwnerType"></el-checkbox>
                </el-checkbox-group>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="attribute-main" v-else>
          <div class="attribite-list">
            <el-row class="attribite-item-no">
              <div class="no-text">暂无特殊属性</div>
            </el-row>
          </div>
        </div>
        <div v-if="isShowRepeat" style="display: flex; margin-bottom: 8px">
          <div class="isrepeat">
            <div style="margin-right: 8px">是否检测重复</div>
            <el-checkbox :label="1" :false-label="0" v-model="repeat" @change="handleRepeat"></el-checkbox>
          </div>
          <div class="isallAccquire">
            <div style="margin-right: 8px">是否全要素采集</div>
            <el-checkbox :label="1" :false-label="0" v-model="allAccquire" @change="handleAllAccquire"></el-checkbox>
          </div>
        </div>
      </div>

      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitElementSetting">确 定</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 新增属性的弹框 -->
    <add-group
      :addGroupVisible="addGroupVisible"
      :addGroupTitle="addGroupTitle"
      :groupFilterList="groupFilterList"
      :addGroupItem="addGroupItem"
      @closeAddGroup="handleCloseAddGroup"
      @submitGroup="handleSubmitGroup"
    ></add-group>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import authImg from '@/components/authImg/index.vue';
import addGroup from '../addGroup/index.vue';
import { saveFieldGroup, getOwnerListByModuleId, saveFieldInOwner } from '@/api/modal/index';
import svgIcon from '../../svgIcon/index.vue';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
const modalStore = useModalStore();
const userStore = useUserStore();

// 定义 props
const props = defineProps<{
  settingTitle: string;
  settingVisible: boolean;
  checkedTreeMsg: {
    fieldGroupModelList: Array<{
      id?: number;
      iconUrl?: string;
      typeName: string;
      remark: string;
      ruleAttribution?: {
        type: string;
        pointRepeat?: boolean;
        pointAccquire?: boolean;
        lineRepeat?: boolean;
        lineAccquire?: boolean;
      };
      groupScope: number;
      lofting?: number;
      loftingPoint?: number;
      loftingLine?: number;
    }>;
  };
}>();

// 定义 emits
const emit = defineEmits(['closeElementSetting', 'saveRule', 'showField', 'openFieldForm']);
const dialogVisible = computed({
  get() {
    return props.settingVisible;
  },
  set(value) {
    // 触发关闭事件
    emit('closeElementSetting');
  }
});

// 定义响应式数据
const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;

const ownerTypeList = ref<
  Array<{
    id: number;
    iconUrl?: string;
    typeName: string;
    remark: string;
  }>
>([]);
const addGroupVisible = ref<boolean>(false);
const addGroupTitle = ref<string>('新建属性');
const idList = ref<Array<number>>([]);
const groupFilterList = ref<
  Array<{
    id?: number;
    iconUrl?: string;
    typeName: string;
    remark: string;
  }>
>([]);
const addGroupItem = ref<{
  id?: number;
  iconUrl?: string;
  typeName: string;
  remark: string;
  ruleAttribution?: {
    type: string;
  };
  groupScope: number;
}>({});
const repeat = ref<boolean>(false);
const allAccquire = ref<boolean>(false);

// 计算属性
const moduleId = computed(() => modalStore.moduleId);
const groupId = computed(() => modalStore.groupId);

const isShowFY = computed(() => {
  let list: Array<{
    ruleAttribution?: {
      type: string;
    };
  }> = [];
  if (props.settingTitle === '点要素设置') {
    list = props.checkedTreeMsg.fieldGroupModelList.filter((item) => {
      return (
        (item.ruleAttribution && item.ruleAttribution.type === 'graphicalPoint') ||
        (item.ruleAttribution && item.ruleAttribution.type === 'commonPoint')
      );
    });
  } else if (props.settingTitle === '线要素设置') {
    list = props.checkedTreeMsg.fieldGroupModelList.filter((item) => {
      return (
        (item.ruleAttribution && item.ruleAttribution.type === 'graphicalLine') ||
        (item.ruleAttribution && item.ruleAttribution.type === 'commonLine')
      );
    });
  }
  return list.length > 0;
});

const isShowRepeat = computed(() => {
  let list: Array<{
    groupScope: number;
    ruleAttribution?: {
      type: string;
    };
  }> = [];
  if (props.settingTitle === '点要素设置') {
    list = props.checkedTreeMsg.fieldGroupModelList.filter((item) => {
      return item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalPoint';
    });
  } else if (props.settingTitle === '线要素设置') {
    list = props.checkedTreeMsg.fieldGroupModelList.filter((item) => {
      return item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalLine';
    });
  }
  return list.length > 0;
});

const initSpeflg = (val: any) => {
  // 回显点放样或者线放样的数据
  if (props.settingTitle === '点要素设置') {
    props.checkedTreeMsg.lofting = val.loftingPoint;
    props.checkedTreeMsg.fieldGroupModelList.forEach((item) => {
      if (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalPoint') {
        if (item.ruleAttribution.pointRepeat === 1) {
          repeat.value = true;
        }
        if (item.ruleAttribution.pointAccquire === 1) {
          allAccquire.value = true;
        }
      }
    });
  } else if (props.settingTitle === '线要素设置') {
    props.checkedTreeMsg.lofting = val.loftingLine;
    props.checkedTreeMsg.fieldGroupModelList.forEach((item) => {
      if (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalLine') {
        if (item.ruleAttribution.lineRepeat === 1) {
          repeat.value = true;
        }
        if (item.ruleAttribution.lineAccquire === 1) {
          allAccquire.value = true;
        }
      }
    });
  }
};

// 方法
const isShowGroupList = (item: {
  groupScope: number;
  ruleAttribution?: {
    type: string;
  };
}) => {
  if (props.settingTitle === '点要素设置') {
    return (
      item.groupScope === 2 &&
      item.ruleAttribution !== null &&
      (item.ruleAttribution.type === 'commonPoint' || item.ruleAttribution.type === 'graphicalPoint')
    );
  } else if (props.settingTitle === '线要素设置') {
    return (
      item.groupScope === 2 &&
      item.ruleAttribution !== null &&
      (item.ruleAttribution.type === 'commonLine' || item.ruleAttribution.type === 'graphicalLine')
    );
  }
  return false;
};

const handleOpenInfoField = (item: {
  id: number;
  attribution?: {
    formData: any;
  };
  fieldModelList: Array<{
    fieldName: string;
    id: number;
  }>;
}) => {
  if (props.settingTitle === '点要素设置') {
    modalStore.setElementType('point');
  } else if (props.settingTitle === '线要素设置') {
    modalStore.setElementType('line');
  }
  modalStore.setGroupId(item.id);
  modalStore.setCurrentGroupItem(item);
  modalStore.setIsAllGroup(false);
  modalStore.setIsHasAcquition(true);
  modalStore.setCheckedNodeItem(props.checkedTreeMsg);

  if (item.attribution !== null) {
    const formData = item.attribution.formData;
    item.fieldModelList.forEach((i) => {
      formData.fields.filter((e: any) => {
        if (e.tagIcon === 'xtlxr' && e.tag === 'el-xtlxr') {
          e.childrenList.forEach((child: any) => {
            if (i.fieldName === child.vModel) {
              child.id = i.id;
            }
          });
        } else {
          if (i.fieldName === e.vModel) {
            e.id = i.id;
          }
        }
      });
    });
    const elementObj = {
      isShowGroup: true,
      title: props.settingTitle
    };
    modalStore.setIsGroupForm(elementObj);
    emit('showField', formData);
  } else {
    emit('openFieldForm');
  }
};

const handleRowShowField = (
  e: any,
  item: {
    fieldModelList: Array<{
      fieldName: string;
      id: number;
    }>;
  }
) => {
  e.children.forEach((it: any) => {
    if (it.layout === 'rowFormItem') {
      handleRowShowField(it, item);
    } else {
      item.fieldModelList.forEach((i) => {
        if (i.fieldName === it.vModel) {
          it.id = i.id;
        }
      });
    }
  });
  return e;
};

const handleOpenGroup = (
  str: string,
  item?: {
    id?: number;
    iconUrl?: string;
    typeName: string;
    remark: string;
    ruleAttribution?: {
      type: string;
    };
    groupScope: number;
  }
) => {
  groupFilterList.value = JSON.parse(JSON.stringify(ownerTypeList.value));
  const list = JSON.parse(JSON.stringify(props.checkedTreeMsg.fieldGroupModelList));
  if (list && list.length > 0) {
    list.forEach((i) => {
      groupFilterList.value.push(i);
    });
  }
  if (props.settingTitle === '点要素设置') {
    modalStore.setElementType('point');
  } else if (props.settingTitle === '线要素设置') {
    modalStore.setElementType('line');
  }
  if (str === '修改属性') {
    addGroupItem.value = item as any;
  }
  addGroupTitle.value = str;
  addGroupVisible.value = true;
};

const handleCloseAddGroup = () => {
  addGroupVisible.value = false;
  groupFilterList.value = [];
};

const handleSubmitGroup = (item: {
  id?: number;
  ruleAttribution?: {
    type: string;
  };
  groupScope: number;
}) => {
  if (props.settingTitle === '点要素设置') {
    item.ruleAttribution = {
      type: 'commonPoint'
    };
  } else if (props.settingTitle === '线要素设置') {
    item.ruleAttribution = {
      type: 'commonLine'
    };
  }
  item.groupScope = 2;
  if (!item.id) {
    // 新增
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      item.companyId = companyId;
    }
    saveFieldGroup(item).then((res) => {
      if (res.code === 200) {
        item.id = res.data.id;
        props.checkedTreeMsg.fieldGroupModelList.push(item as any);
        modalStore.setGroupId(res.data.id);
        emit('openFieldForm');
        modalStore.setCurrentGroupItem(res.data);
        addGroupVisible.value = false;
        const elementObj = {
          isShowGroup: true,
          title: props.settingTitle
        };
        modalStore.setIsGroupForm(elementObj);
        modalStore.setIsAllGroup(false);
        modalStore.setIsHasAcquition(true);
        modalStore.setCheckedNodeItem(props.checkedTreeMsg);
        emit('closeElementSetting');
      } else {
        alert(res.msg);
      }
    });
  } else {
    // 修改
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      item.companyId = companyId;
    }
    saveFieldGroup(item).then((res) => {
      const index = props.checkedTreeMsg.fieldGroupModelList.findIndex((f) => f.id === item.id);
      props.checkedTreeMsg.fieldGroupModelList.splice(index, 1);
      if (res.code === 200) {
        item.id = res.data.id;
        props.checkedTreeMsg.fieldGroupModelList.push(item as any);
        modalStore.setGroupId(res.data.id);
        modalStore.setCurrentGroupItem(res.data);
        addGroupVisible.value = false;
        modalStore.setIsHasAcquition(true);
        modalStore.setIsHasAcquition(true);
        modalStore.setCheckedNodeItem(props.checkedTreeMsg);
      } else {
        alert(res.msg);
      }
    });
  }
};

const handleDeleteGroup = (item: { id: number; typeName: string; delFlag: number }) => {
  alert(`你确定删除【${item.typeName}】组吗？`)
    .then(() => {
      item.delFlag = 1;
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = route.query.companyId;
      if (companyId && companyId !== undefined && companyId !== null) {
        item.companyId = companyId;
      }
      saveFieldGroup(item).then((res) => {
        if (res.code === 200) {
          const index = props.checkedTreeMsg.fieldGroupModelList.findIndex((f) => f.id === item.id);
          props.checkedTreeMsg.fieldGroupModelList.splice(index, 1);
          alert('删除成功!');
        } else {
          alert(res.msg);
        }
      });
    })
    .catch(() => {
      alert('已取消');
    });
};

const handleCheckedOwnerType = (val: number) => {
  let type = 'graphicalPoint';
  if (props.settingTitle === '点要素设置') {
    type = 'graphicalPoint';
    props.checkedTreeMsg.fieldGroupModelList = props.checkedTreeMsg.fieldGroupModelList.filter((item) => {
      return (
        item.groupScope === 2 ||
        (item.groupScope === 1 &&
          ((item.ruleAttribution && item.ruleAttribution.type === 'graphicalLine') ||
            (item.ruleAttribution && item.ruleAttribution.type === 'graphicalArea')))
      );
    });
  } else if (props.settingTitle === '线要素设置') {
    type = 'graphicalLine';
    props.checkedTreeMsg.fieldGroupModelList = props.checkedTreeMsg.fieldGroupModelList.filter((item) => {
      return (
        item.groupScope === 2 ||
        (item.groupScope === 1 &&
          ((item.ruleAttribution && item.ruleAttribution.type === 'graphicalPoint') ||
            (item.ruleAttribution && item.ruleAttribution.type === 'graphicalArea')))
      );
    });
  }
  ownerTypeList.value.forEach((item) => {
    idList.value.forEach((id) => {
      if (item.id === id) {
        item.ruleAttribution = {
          type: type
        };
        props.checkedTreeMsg.fieldGroupModelList.push(item as any);
      }
    });
  });
};

const handleClose = () => {
  const pointGroupList: Array<{
    ruleAttribution?: {
      type: string;
    };
  }> = [];
  const list = JSON.parse(JSON.stringify(props.checkedTreeMsg.fieldGroupModelList));
  if (list.length > 0) {
    list.forEach((item) => {
      if (item.ruleAttribution !== null && (item.ruleAttribution.type === 'graphicalPoint' || item.ruleAttribution.type === 'commonPoint')) {
        pointGroupList.push(item);
      }
    });
  }
  modalStore.setPointGroupList(pointGroupList);
  emit('closeElementSetting');
  emit('saveRule');
};

const handleLofting = (val: boolean) => {
  let flgNum = 0;
  if (val === true) {
    flgNum = 1;
  }
  if (props.settingTitle === '点要素设置') {
    props.checkedTreeMsg.loftingPoint = flgNum;
  } else if (props.settingTitle === '线要素设置') {
    props.checkedTreeMsg.loftingLine = flgNum;
  }
};

const handleCircum = () => {
  // 查看周边信息
};

const handleRepeat = (val: number) => {
  if (props.settingTitle === '点要素设置') {
    props.checkedTreeMsg.fieldGroupModelList.forEach((item) => {
      if (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalPoint') {
        item.ruleAttribution.pointRepeat = val === 1;
      }
    });
    repeat.value = val;
  } else if (props.settingTitle === '线要素设置') {
    props.checkedTreeMsg.fieldGroupModelList.forEach((item) => {
      if (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalLine') {
        item.ruleAttribution.lineRepeat = val === 1;
      }
    });
    repeat.value = val;
  }
};

const handleAllAccquire = (val: number) => {
  if (props.settingTitle === '点要素设置') {
    props.checkedTreeMsg.fieldGroupModelList.forEach((item) => {
      if (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalPoint') {
        item.ruleAttribution.pointAccquire = val === 1;
      }
    });
    allAccquire.value = val;
  } else if (props.settingTitle === '线要素设置') {
    props.checkedTreeMsg.fieldGroupModelList.forEach((item) => {
      if (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalLine') {
        item.ruleAttribution.lineAccquire = val === 1;
      }
    });
    allAccquire.value = val;
  }
};

const submitElementSetting = () => {
  const pointGroupList: Array<{
    ruleAttribution?: {
      type: string;
    };
  }> = [];
  const list = JSON.parse(JSON.stringify(props.checkedTreeMsg.fieldGroupModelList));
  if (list.length > 0) {
    list.forEach((item) => {
      if (item.ruleAttribution !== null && (item.ruleAttribution.type === 'graphicalPoint' || item.ruleAttribution.type === 'commonPoint')) {
        pointGroupList.push(item);
      }
    });
  }
  modalStore.setPointGroupList(pointGroupList);
  props.checkedTreeMsg.fieldGroupModelList.forEach((item) => {
    if (item.ruleAttribution && item.ruleAttribution.pointRepeat === false) {
      item.ruleAttribution.pointRepeat = 0;
    } else if (item.ruleAttribution && item.ruleAttribution.pointRepeat === true) {
      item.ruleAttribution.pointRepeat = 1;
    }
    if (item.ruleAttribution && item.ruleAttribution.pointAccquire === false) {
      item.ruleAttribution.pointAccquire = 0;
    } else if (item.ruleAttribution && item.ruleAttribution.pointAccquire === true) {
      item.ruleAttribution.pointAccquire = 1;
    }
  });
  emit('saveRule');
  emit('closeElementSetting');
};

const getOpen = async () => {
  initSpeflg(props.checkedTreeMsg);
  await getOwnerList();
  const list = props.checkedTreeMsg.fieldGroupModelList;
  if (!list) {
    return;
  }
  idList.value = [];
  if (list.length > 0 && props.settingTitle === '点要素设置') {
    list.forEach((item) => {
      if (item.ruleAttribution !== null && item.ruleAttribution.type === 'graphicalPoint') {
        if (item.groupScope === 1) {
          idList.value.push(item.id as number);
        }
      }
    });
  } else if (list.length > 0 && props.settingTitle === '线要素设置') {
    list.forEach((item) => {
      if (item.ruleAttribution !== null && item.ruleAttribution.type === 'graphicalLine') {
        if (item.groupScope === 1) {
          idList.value.push(item.id as number);
        }
      }
    });
  }
};

const getOwnerList = async () => {
  const params = {
    moduleId: moduleId.value,
    groupScope: 1
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  ownerTypeList.value = modalStore.speGroups;
};
</script>

<style lang="scss" scoped>
:deep(.el-checkbox__label) {
  display: none;
}
.attribute-group-main {
  background-color: #fff;
  border: 1px solid #dbe7ee;
  border-radius: 12px;
  margin: 24px 16px 32px;
  position: relative;
  min-height: 80px;
  .item-title {
    position: absolute;
    top: -12px;
    left: 24px;
    right: 32px;
    display: flex;
    justify-content: space-between;
    .handle-title {
      // width: 80px;
      height: 16px;
      color: #333333;
      background-color: #fff;
      .text {
        color: #161d26;
        font-size: 14px;
        text-align: left;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        padding: 8px;
        font-weight: 600;
        vertical-align: top;
        &:hover {
          color: var(--current-color);
        }
      }
    }
  }
  &:hover {
    border: 1px solid var(--current-color);
    // color: #0081ff;
  }
  &:hover .handle-title .text {
    color: var(--current-color);
  }
  .attribute-main {
    // margin:24px 0 16px;
    width: 100%;
    display: flex;
    flex-direction: column;
    .attribite-list {
      // display: flex;
      // flex-direction: column;
      // justify-content: center;
      // align-content: center;
      // align-items: center;
      flex: 1;
      margin: 0px 16px 12px;
      .attribite-item {
        margin: 12px 0;
        padding: 0px 12px;
        height: 40px;
        line-height: 40px;
        border-radius: 4px;
        background-color: rgba(246, 247, 248, 1);
        // text-align: center;
        display: flex;
        align-items: center;
        .svg-item {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          // color: #333;
        }
        .item-type-name {
          height: 40px;
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-weight: 600;
        }
        .item-remark {
          height: 40px;
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        .text-btn {
          margin: 0 8px;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          .svg-item {
            width: 16px;
            height: 16px;
            margin: auto;
            color: #8291a9;
            &:hover {
              color: var(--current-color);
            }
          }
          &:hover {
            width: 24px;
            height: 24px;
            background-color: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
          }
          &:hover .svg-item {
            width: 16px;
            height: 16px;
            background-color: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
          }
        }
      }
    }
  }
}
.acquisition-owner-main {
  background-color: #fff;
  border: 1px solid #dbe7ee;
  border-radius: 12px;
  margin: 24px 16px 32px;
  position: relative;
  min-height: 80px;
  .item-title {
    position: absolute;
    top: -12px;
    left: 24px;
    right: 32px;
    display: flex;
    justify-content: space-between;

    .handle-title {
      // width: 80px;
      height: 16px;
      color: #333333;
      background-color: #fff;
      .text {
        color: #161d26;
        font-size: 14px;
        text-align: left;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        padding: 8px;
        font-weight: 600;
        vertical-align: top;
        &:hover {
          color: var(--current-color);
        }
      }
    }
  }

  &:hover {
    border: 1px solid var(--current-color);
    // color: #0081ff;
  }
  &:hover .handle-title .text {
    color: var(--current-color);
  }
  .attribute-main {
    // margin: 16px;
    width: 100%;
    display: flex;
    flex-direction: column;
    // margin: 24px 0 16px;
    margin-top: 12px;
    .attribite-list {
      // display: flex;
      // flex-direction: column;
      // justify-content: center;
      // align-content: center;
      // align-items: center;
      margin: 0px 16px 12px;
      flex: 1;
      .attribite-item-no {
        margin: 12px 0;
        .no-text {
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-align: center;
          color: #8291a9;
        }
      }
      .attribite-item {
        margin: 12px 0;
        padding: 0px 12px;
        height: 40px;
        line-height: 40px;
        border-radius: 4px;
        background-color: rgba(246, 247, 248, 1);
        // text-align: center;
        display: flex;
        align-items: center;

        .svg-item {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          color: #333;
        }
        .item-type-name {
          height: 40px;
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-weight: 600;
        }
        .item-remark {
          height: 40px;
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        .text-btn {
          margin: 0 8px;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          .svg-item {
            width: 16px;
            height: 16px;
            margin: auto;
            color: #8291a9;
            &:hover {
              color: var(--current-color);
            }
          }
          &:hover {
            width: 24px;
            height: 24px;
            background-color: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
          }
          &:hover .svg-item {
            width: 16px;
            height: 16px;
            background-color: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
          }
        }
      }
    }
  }
}

.el-checkbox-group {
  .el-checkbox {
    :deep(&) {
      .el-checkbox__label {
        display: none;
      }
    }
  }
}
.isfy {
  height: 25px;
  width: 100px;
  color: #161d26;
  font-size: 14px;
  text-align: center;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  font-weight: 600;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 16px;
}
.isrepeat {
  height: 25px;
  width: 150px;
  color: #161d26;
  font-size: 14px;
  text-align: center;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  font-weight: 600;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 16px;
}
.isallAccquire {
  height: 25px;
  width: 170px;
  color: #161d26;
  font-size: 14px;
  text-align: center;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  font-weight: 600;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 16px;
}
</style>
