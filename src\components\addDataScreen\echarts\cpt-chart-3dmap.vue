<!-- 3d地图地区 -->
<template>
  <div style="width: 100%; height: 100%" :id="uuid"></div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import 'echarts-gl';
import { v1 as uuidv1 } from 'uuid';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-chart-3dmap'
});

const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();

// --- 定义变量 ---
const uuid = ref(uuidv1());
let chart: any = null;
const cptData = ref();
const scatterData = ref();
// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  (newVal) => {
    chart?.resize();
  }
);
watch(
  () => props.height,
  (newVal) => {
    chart?.resize();
  }
);
// --- 方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId: string) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (taskId != '') {
      parmas.taskId = taskId;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        if (res.data.map) {
          const names = Object.keys(res.data.map);
          const list: any[] = [];
          names.forEach((v, idx) => {
            list.push({
              name: v,
              value: res.data.map[v]
            });
          });
          cptData.value = list;
        }
        // 处理散点数据
        if (res.data.scatter) {
          scatterData.value = convertScatterData(res.data.scatter);
        }
        loadChart(props.option.attribute);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res.mapData || res;
      // 处理散点数据
      if (res.scatterData) {
        scatterData.value = convertScatterData(res.scatterData);
      }
      loadChart(props.option.attribute);
    });
  }
};

// 散点数据转换函数
const convertScatterData = (data: any[]) => {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => {
    let coordinates: number[] = [];
    let value = 0;

    // 支持多种数据格式
    if (item.lng && item.lat) {
      // 格式1: {name: '北京', lng: 116.46, lat: 39.92, value: 100}
      coordinates = [item.lng, item.lat];
      value = item.value || 0;
    } else if (item.coord && Array.isArray(item.coord)) {
      // 格式2: {name: '北京', coord: [116.46, 39.92], value: 100}
      coordinates = item.coord;
      value = item.value || 0;
    } else if (Array.isArray(item.value) && item.value.length >= 2) {
      // 格式3: {name: '北京', value: [116.46, 39.92, 100]}
      coordinates = [item.value[0], item.value[1]];
      value = item.value[2] || 0;
    } else {
      return null;
    }

    return {
      name: item.name || '',
      value: [...coordinates, value], // [经度, 纬度, 数值]
      itemStyle: item.itemStyle || {},
      label: item.label || {}
    };
  }).filter(Boolean);
};

const loadChart = (attribute: any) => {
  // 构建图表配置
  const chartOption: any = {
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: (params: any) => {
        if (params.seriesName === attribute.scatterName || params.seriesType === 'scatter') {
          return `${params.name}<br/>经度: ${params.value[0]}<br/>纬度: ${params.value[1]}<br/>数值: ${params.value[2]}`;
        }
        return `${params.name}: ${params.value}`;
      }
    }
  };

  // 构建系列数组
  const series: any[] = [
    {
      type: 'map3D',
      name: attribute.titleText,
      center: ['50%', '50%'],
      selectedMode: 'single',
      regionHeight: attribute.mapHeight,
      map: attribute.map,
      viewControl: {
        distance: attribute.distance,
        alpha: attribute.alpha,
        beta: attribute.beta
      },
      label: {
        show: attribute.isShowLabel,
        color: attribute.labelColor,
        fontSize: attribute.fontSize,
        fontWeight: 'bold'
      },
      itemStyle: {
        color: attribute.mapBackgroundColor,
        borderWidth: attribute.borderWidth,
        borderColor: attribute.borderColor,
        opacity: 0.92
      },
      emphasis: {
        label: {
          show: attribute.isShowLed,
          textStyle: {
            color: attribute.ledColor
          }
        },
        itemStyle: {
          color: attribute.mapLedColor,
          borderWidth: attribute.mapLedBorderWidth,
          borderColor: attribute.mapLedBorderColor
        }
      },
      light: {
        main: {
          color: '#fff',
          intensity: 1,
          shadow: true,
          shadowQuality: 'high',
          alpha: 25,
          beta: 2
        },
        ambient: {
          color: '#fff',
          intensity: 0.6
        }
      },
      data: cptData.value
    }
  ];

  // 如果有散点数据且启用散点显示，在地图上叠加散点
  if (scatterData.value && scatterData.value.length > 0 && attribute.showScatter !== false) {
    // 添加geo组件作为散点的坐标系（2D坐标系，但会跟随3D地图）
    chartOption.geo = {
      map: attribute.map,
      roam: false,
      silent: true,
      itemStyle: {
        color: 'transparent',
        borderWidth: 0
      },
      label: {
        show: false
      },
      emphasis: {
        disabled: true
      },
      zlevel: -1
    };

    // 添加散点系列，使用geo坐标系
    series.push({
      type: 'scatter',
      name: attribute.scatterName || '散点数据',
      coordinateSystem: 'geo',
      symbol: attribute.scatterSymbol || 'circle',
      symbolSize: attribute.scatterSize ? attribute.scatterSize[0] : 8,
      itemStyle: {
        color: attribute.scatterColor || '#FF6B6B',
        opacity: attribute.scatterOpacity || 0.8,
        borderWidth: 2,
        borderColor: '#fff'
      },
      label: {
        show: attribute.showScatterLabel || false,
        position: 'top',
        distance: 5,
        color: attribute.scatterLabelColor || '#fff',
        fontSize: attribute.scatterLabelSize || 12,
        fontWeight: 'bold',
        formatter: (params: any) => {
          return params.name || '';
        }
      },
      emphasis: {
        itemStyle: {
          color: attribute.scatterEmphasisColor || '#FF4757',
          opacity: 1,
          shadowBlur: 10,
          shadowColor: attribute.scatterEmphasisColor || '#FF4757'
        },
        label: {
          show: true,
          fontSize: (attribute.scatterLabelSize || 12) + 2
        }
      },
      data: scatterData.value,
      zlevel: 2
    });
  }

  chartOption.series = series;

  chart?.setOption(chartOption);
};

// 初始化
onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));
  refreshCptData();
});
</script>
<style lang="scss" scoped></style>
