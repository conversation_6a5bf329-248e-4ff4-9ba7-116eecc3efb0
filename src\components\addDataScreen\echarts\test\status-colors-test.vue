<!-- 双色状态测试页面 -->
<template>
  <div class="test-container">
    <h2>散点图双色状态测试</h2>
    
    <div class="controls">
      <el-button @click="enableStatusColors">启用状态颜色</el-button>
      <el-button @click="disableStatusColors">禁用状态颜色</el-button>
      <el-button @click="changeColors">更换颜色</el-button>
      <el-button @click="toggleLegend">切换图例</el-button>
    </div>
    
    <div class="chart-wrapper">
      <cpt-chart-geo-scatter 
        :width="800" 
        :height="600" 
        :option="chartOption"
        :key="chartKey"
      />
    </div>
    
    <div class="status-info">
      <h3>当前配置</h3>
      <div class="config-item">
        <p><strong>启用状态颜色:</strong> {{ chartOption.attribute.enableStatusColors ? '是' : '否' }}</p>
        <p><strong>状态字段:</strong> {{ chartOption.attribute.statusField }}</p>
        <p><strong>已完成颜色:</strong> <span :style="{color: chartOption.attribute.completedColor}">{{ chartOption.attribute.completedColor }}</span></p>
        <p><strong>未完成颜色:</strong> <span :style="{color: chartOption.attribute.uncompletedColor}">{{ chartOption.attribute.uncompletedColor }}</span></p>
        <p><strong>显示图例:</strong> {{ chartOption.attribute.showLegend ? '是' : '否' }}</p>
      </div>
    </div>
    
    <div class="data-info">
      <h3>测试数据</h3>
      <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';

defineOptions({
  name: 'status-colors-test'
});

const chartKey = ref(0);

// 测试数据 - 包含状态信息
const testData = [
  { name: '北京', lng: 116.46, lat: 39.92, value: 150, status: true },
  { name: '上海', lng: 121.48, lat: 31.22, value: 200, status: false },
  { name: '广州', lng: 113.23, lat: 23.16, value: 120, status: true },
  { name: '深圳', lng: 114.07, lat: 22.62, value: 180, status: false },
  { name: '杭州', lng: 120.19, lat: 30.26, value: 160, status: true },
  { name: '成都', lng: 104.06, lat: 30.67, value: 140, status: false },
  { name: '武汉', lng: 114.31, lat: 30.52, value: 130, status: true },
  { name: '西安', lng: 108.95, lat: 34.27, value: 110, status: false }
];

const chartOption = ref({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify(testData)
  },
  attribute: {
    titleText: '项目完成状态分布图',
    subtext: '双色状态测试',
    titleLeft: 'center',
    titleTop: 10,
    titleFontSize: 18,
    titleColor: '#333',
    subTitleColor: '#666',
    subTitleFontSize: 12,
    
    map: 'china',
    roam: true,
    zoom: 1.2,
    center: [104, 35],
    
    showMapLabel: true,
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,
    
    seriesName: '项目数据',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 12,
    scatterSizeMultiplier: 0.8,
    scatterColor: '#ff6b6b',
    scatterOpacity: 0.8,
    scatterBorderWidth: 2,
    scatterBorderColor: '#fff',
    
    showScatterLabel: true,
    scatterLabelPosition: 'top',
    scatterLabelColor: '#333',
    scatterLabelSize: 12,
    scatterLabelFormat: 'name',
    
    // 双色状态配置
    enableStatusColors: false,
    statusField: 'status',
    completedColor: '#52c41a',
    uncompletedColor: '#ff4d4f',
    completedLabel: '已完成',
    uncompletedLabel: '未完成',
    showLegend: true,
    
    regionColors: {},
    regionValues: {}
  }
});

const enableStatusColors = () => {
  chartOption.value.attribute.enableStatusColors = true;
  chartKey.value++;
  console.log('启用状态颜色');
};

const disableStatusColors = () => {
  chartOption.value.attribute.enableStatusColors = false;
  chartKey.value++;
  console.log('禁用状态颜色');
};

const changeColors = () => {
  const colors = [
    { completed: '#1890ff', uncompleted: '#faad14' },
    { completed: '#722ed1', uncompleted: '#eb2f96' },
    { completed: '#13c2c2', uncompleted: '#f5222d' }
  ];
  
  const randomColor = colors[Math.floor(Math.random() * colors.length)];
  chartOption.value.attribute.completedColor = randomColor.completed;
  chartOption.value.attribute.uncompletedColor = randomColor.uncompleted;
  
  chartKey.value++;
  console.log('更换颜色:', randomColor);
};

const toggleLegend = () => {
  chartOption.value.attribute.showLegend = !chartOption.value.attribute.showLegend;
  chartKey.value++;
  console.log('切换图例:', chartOption.value.attribute.showLegend);
};

onMounted(() => {
  console.log('双色状态测试页面加载完成');
  console.log('测试数据:', testData);
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.status-info {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.config-item p {
  margin: 8px 0;
}

.data-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.data-info pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

h2, h3 {
  color: #333;
}
</style>
