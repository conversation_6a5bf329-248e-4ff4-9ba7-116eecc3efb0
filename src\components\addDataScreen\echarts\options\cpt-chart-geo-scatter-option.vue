<!-- GEO SVG 散点图配置选项 -->
<template>
  <el-form label-width="120px" style="padding: 20px">
    <!-- 标题配置 -->
    <el-divider content-position="left">标题配置</el-divider>
    <el-form-item label="主标题">
      <el-input v-model="attributeCopy.titleText" />
    </el-form-item>
    <el-form-item label="副标题">
      <el-input v-model="attributeCopy.subtext" />
    </el-form-item>
    <el-form-item label="标题位置">
      <el-select v-model="attributeCopy.titleLeft">
        <el-option label="左对齐" value="left" />
        <el-option label="居中" value="center" />
        <el-option label="右对齐" value="right" />
      </el-select>
    </el-form-item>
    <el-form-item label="标题颜色">
      <el-color-picker v-model="attributeCopy.titleColor" />
    </el-form-item>
    <el-form-item label="标题大小">
      <el-input v-model="attributeCopy.titleFontSize" type="number" @keydown="handleInputKeydown" />
    </el-form-item>
    <!-- 地图配置 -->
    <el-divider content-position="left">地图配置</el-divider>
    <el-form-item label="地图类型">
      <el-select v-model="attributeCopy.map">
        <el-option label="中国" value="china" />
        <el-option label="毕节" value="bijie" />
        <el-option label="贵州省" value="guizhou" />
        <el-option label="常州市" value="changZhou" />
        <el-option label="四会市" value="sihui" />
      </el-select>
    </el-form-item>
    <el-form-item label="允许缩放平移">
      <el-switch v-model="attributeCopy.roam" />
    </el-form-item>
    <el-form-item label="初始缩放">
      <el-slider v-model="attributeCopy.zoom" :min="0.01" :max="20" :step="0.1" />
    </el-form-item>
    <el-form-item label="地图区域颜色">
      <el-color-picker v-model="attributeCopy.mapAreaColor" />
    </el-form-item>
    <el-form-item label="地图边界颜色">
      <el-color-picker v-model="attributeCopy.mapBorderColor" />
    </el-form-item>
    <el-form-item label="边界宽度">
      <el-input v-model="attributeCopy.mapBorderWidth" type="number" @keydown="handleInputKeydown" />
    </el-form-item>

    <!-- 区域颜色配置 -->
    <el-divider content-position="left">区域颜色配置</el-divider>
    <el-form-item label="启用区域颜色">
      <el-switch v-model="enableRegionColors" @change="onRegionColorsToggle" />
    </el-form-item>
    <el-collapse v-if="enableRegionColors" v-model="activeRegionPanels" class="region-collapse">
      <el-collapse-item title="区域颜色设置" name="regions">
        <div class="region-grid">
          <div v-for="region in availableRegions" :key="region" class="region-item">
            <span class="region-name">{{ region }}</span>
            <div class="region-controls">
              <el-color-picker v-model="regionColors[region]" @change="onRegionColorChange" show-alpha size="small" />
              <el-button size="small" @click="resetRegionColor(region)" :disabled="!regionColors[region]" type="text"> 重置 </el-button>
            </div>
          </div>
        </div>
        <div class="region-actions">
          <el-button @click="resetAllRegionColors" size="small">重置所有</el-button>
          <el-button @click="randomRegionColors" size="small" type="primary">随机颜色</el-button>
        </div>
      </el-collapse-item>
    </el-collapse>

    <!-- 散点配置 -->
    <el-divider content-position="left">散点配置</el-divider>
    <el-form-item label="系列名称">
      <el-input v-model="attributeCopy.seriesName" />
    </el-form-item>
    <el-form-item label="散点类型">
      <el-select v-model="attributeCopy.scatterType">
        <el-option label="普通散点" value="scatter" />
        <el-option label="涟漪散点" value="effectScatter" />
      </el-select>
    </el-form-item>
    <el-form-item label="散点符号">
      <el-select v-model="attributeCopy.scatterSymbol">
        <el-option label="圆形" value="circle" />
        <el-option label="矩形" value="rect" />
        <el-option label="圆角矩形" value="roundRect" />
        <el-option label="三角形" value="triangle" />
        <el-option label="菱形" value="diamond" />
        <el-option label="针形" value="pin" />
        <el-option label="箭头" value="arrow" />
        <el-option label="星形" value="star" />
        <el-option label="心形" value="heart" />
        <el-option label="水滴" value="droplet" />
        <el-option label="自定义图片" value="image" />
        <el-option label="自定义路径" value="path" />
      </el-select>
    </el-form-item>

    <!-- 自定义符号配置 -->
    <el-collapse v-if="attributeCopy.scatterSymbol === 'image' || attributeCopy.scatterSymbol === 'path'" class="custom-symbol-collapse">
      <el-collapse-item title="自定义符号配置" name="custom">
        <div v-if="attributeCopy.scatterSymbol === 'image'" class="custom-config">
          <el-form-item label="图片URL">
            <el-input v-model="attributeCopy.customImageUrl" placeholder="输入图片URL或base64" size="small" />
          </el-form-item>
          <el-form-item label="图片大小">
            <el-slider v-model="attributeCopy.customImageSize" :min="10" :max="100" :step="5" />
          </el-form-item>
        </div>

        <div v-if="attributeCopy.scatterSymbol === 'path'" class="custom-config">
          <el-form-item label="SVG路径">
            <el-input v-model="attributeCopy.customPath" type="textarea" :rows="2" placeholder="如：M0,0 L10,10 L20,0 Z" size="small" />
          </el-form-item>
          <div class="path-controls">
            <el-form-item label="大小">
              <el-slider v-model="attributeCopy.customPathSize" :min="10" :max="100" :step="5" />
            </el-form-item>
            <el-form-item label="颜色">
              <el-color-picker v-model="attributeCopy.customPathColor" size="small" />
            </el-form-item>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <el-form-item label="基础大小">
      <el-input v-model="attributeCopy.scatterBaseSize" type="number" @keydown="handleInputKeydown" />
    </el-form-item>
    <el-form-item label="大小倍数">
      <el-slider v-model="attributeCopy.scatterSizeMultiplier" :min="0.1" :max="2" :step="0.1" />
    </el-form-item>
    <el-form-item label="散点颜色">
      <el-color-picker v-model="attributeCopy.scatterColor" />
    </el-form-item>
    <el-form-item label="散点透明度">
      <el-slider v-model="attributeCopy.scatterOpacity" :min="0" :max="1" :step="0.1" />
    </el-form-item>

    <!-- 散点标签 -->
    <el-divider content-position="left">散点标签</el-divider>
    <el-form-item label="显示标签">
      <el-switch v-model="attributeCopy.showScatterLabel" />
    </el-form-item>
    <el-form-item label="标签位置">
      <el-select v-model="attributeCopy.scatterLabelPosition">
        <el-option label="顶部" value="top" />
        <el-option label="底部" value="bottom" />
        <el-option label="左侧" value="left" />
        <el-option label="右侧" value="right" />
        <el-option label="内部" value="inside" />
      </el-select>
    </el-form-item>
    <el-form-item label="标签格式">
      <el-select v-model="attributeCopy.scatterLabelFormat">
        <el-option label="仅名称" value="name" />
        <el-option label="仅数值" value="value" />
        <el-option label="名称+数值" value="both" />
      </el-select>
    </el-form-item>
    <el-form-item label="标签颜色">
      <el-color-picker v-model="attributeCopy.scatterLabelColor" />
    </el-form-item>
    <el-form-item label="标签大小">
      <el-input v-model="attributeCopy.scatterLabelSize" type="number" @keydown="handleInputKeydown" />
    </el-form-item>

    <!-- 涟漪效果 -->
    <el-divider content-position="left">涟漪效果</el-divider>
    <el-form-item label="涟漪类型">
      <el-select v-model="attributeCopy.rippleBrushType">
        <el-option label="描边" value="stroke" />
        <el-option label="填充" value="fill" />
      </el-select>
    </el-form-item>
    <el-form-item label="涟漪缩放">
      <el-slider v-model="attributeCopy.rippleScale" :min="1" :max="5" :step="0.5" />
    </el-form-item>
    <el-form-item label="涟漪周期">
      <el-slider v-model="attributeCopy.ripplePeriod" :min="1" :max="10" :step="1" />
    </el-form-item>

    <!-- 双色状态配置 -->
    <el-divider content-position="left">双色状态配置</el-divider>
    <el-form-item label="启用状态颜色">
      <el-switch v-model="attributeCopy.enableStatusColors" />
    </el-form-item>

    <el-collapse v-if="attributeCopy.enableStatusColors" v-model="activeStatusPanels" class="status-collapse">
      <!-- 基础配置 -->
      <el-collapse-item title="基础配置" name="basic">
        <el-form-item label="状态字段名">
          <el-input v-model="attributeCopy.statusField" placeholder="如: status" size="small" />
        </el-form-item>
        <el-form-item label="显示图例">
          <el-switch v-model="attributeCopy.showLegend" />
        </el-form-item>
        <!-- 添加图例文字配置 -->
        <template v-if="attributeCopy.showLegend">
          <el-form-item label="图例文字颜色">
            <el-color-picker v-model="attributeCopy.legendTextColor" size="small" />
          </el-form-item>
          <el-form-item label="图例文字大小">
            <el-input v-model="attributeCopy.legendTextSize" type="number" size="small" @keydown="handleInputKeydown" />
          </el-form-item>
        </template>
      </el-collapse-item>

      <!-- 颜色配置 -->
      <el-collapse-item title="颜色配置" name="colors">
        <div class="status-config-grid">
          <div class="status-item">
            <label class="status-label">已完成</label>
            <el-color-picker v-model="attributeCopy.completedColor" size="small" />
            <el-input v-model="attributeCopy.completedLabel" placeholder="已完成" size="small" />
          </div>
          <div class="status-item">
            <label class="status-label">未完成</label>
            <el-color-picker v-model="attributeCopy.uncompletedColor" size="small" />
            <el-input v-model="attributeCopy.uncompletedLabel" placeholder="未完成" size="small" />
          </div>
        </div>
      </el-collapse-item>

      <!-- 符号配置 -->
      <el-collapse-item title="符号配置" name="symbols">
        <el-form-item label="启用状态符号">
          <el-switch v-model="attributeCopy.enableStatusSymbols" />
        </el-form-item>

        <div v-if="attributeCopy.enableStatusSymbols" class="symbols-grid">
          <!-- 已完成符号 -->
          <div class="symbol-section">
            <h5>已完成符号</h5>
            <el-select v-model="attributeCopy.completedSymbol" size="small" style="width: 100%">
              <el-option label="圆形" value="circle" />
              <el-option label="矩形" value="rect" />
              <el-option label="三角形" value="triangle" />
              <el-option label="菱形" value="diamond" />
              <el-option label="星形" value="star" />
              <el-option label="心形" value="heart" />
              <el-option label="自定义图片" value="image" />
              <el-option label="自定义路径" value="path" />
            </el-select>

            <div v-if="attributeCopy.completedSymbol === 'image'" class="custom-config">
              <el-input v-model="attributeCopy.completedCustomImageUrl" placeholder="图片URL" size="small" />
              <el-slider v-model="attributeCopy.completedCustomImageSize" :min="10" :max="100" :step="5" />
            </div>

            <div v-if="attributeCopy.completedSymbol === 'path'" class="custom-config">
              <el-input v-model="attributeCopy.completedCustomPath" type="textarea" :rows="2" placeholder="SVG路径" size="small" />
              <div class="path-controls">
                <el-slider v-model="attributeCopy.completedCustomPathSize" :min="10" :max="100" :step="5" />
                <el-color-picker v-model="attributeCopy.completedCustomPathColor" size="small" />
              </div>
            </div>
          </div>

          <!-- 未完成符号 -->
          <div class="symbol-section">
            <h5>未完成符号</h5>
            <el-select v-model="attributeCopy.uncompletedSymbol" size="small" style="width: 100%">
              <el-option label="圆形" value="circle" />
              <el-option label="矩形" value="rect" />
              <el-option label="三角形" value="triangle" />
              <el-option label="菱形" value="diamond" />
              <el-option label="星形" value="star" />
              <el-option label="心形" value="heart" />
              <el-option label="自定义图片" value="image" />
              <el-option label="自定义路径" value="path" />
            </el-select>

            <div v-if="attributeCopy.uncompletedSymbol === 'image'" class="custom-config">
              <el-input v-model="attributeCopy.uncompletedCustomImageUrl" placeholder="图片URL" size="small" />
              <el-slider v-model="attributeCopy.uncompletedCustomImageSize" :min="10" :max="100" :step="5" />
            </div>

            <div v-if="attributeCopy.uncompletedSymbol === 'path'" class="custom-config">
              <el-input v-model="attributeCopy.uncompletedCustomPath" type="textarea" :rows="2" placeholder="SVG路径" size="small" />
              <div class="path-controls">
                <el-slider v-model="attributeCopy.uncompletedCustomPathSize" :min="10" :max="100" :step="5" />
                <el-color-picker v-model="attributeCopy.uncompletedCustomPathColor" size="small" />
              </div>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>

    <!-- 视觉映射 -->
    <el-divider content-position="left">视觉映射</el-divider>
    <el-form-item label="显示视觉映射">
      <el-switch v-model="attributeCopy.showVisualMap" />
    </el-form-item>
    <el-form-item label="最小值">
      <el-input v-model="attributeCopy.visualMapMin" type="number" @keydown="handleInputKeydown" />
    </el-form-item>
    <el-form-item label="最大值">
      <el-input v-model="attributeCopy.visualMapMax" type="number" @keydown="handleInputKeydown" />
    </el-form-item>
    <el-form-item label="最小值颜色">
      <el-color-picker v-model="attributeCopy.visualMapMinColor" />
    </el-form-item>
    <el-form-item label="最大值颜色">
      <el-color-picker v-model="attributeCopy.visualMapMaxColor" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';

defineOptions({
  name: 'cpt-chart-geo-scatter-option'
});

const props = defineProps<{
  attribute: Record<string, any>;
}>();

const emit = defineEmits(['update-attribute']);

const attributeCopy = computed({
  get() {
    return props.attribute;
  },
  set(val) {
    emit('update-attribute', val);
  }
});

// 直接更新属性的方法
const updateAttribute = (key: string, value: any) => {
  const newAttribute = { ...props.attribute };
  newAttribute[key] = value;
  console.log(`更新属性 ${key}:`, value);
  emit('update-attribute', newAttribute);
};

// 区域颜色相关状态
const enableRegionColors = ref(false);
const regionColors = ref<Record<string, string>>({});

// 状态配置折叠面板
const activeStatusPanels = ref(['basic', 'colors']);

// 区域颜色折叠面板
const activeRegionPanels = ref(['regions']);

// 获取当前地图的可用区域
const availableRegions = computed(() => {
  const mapName = props.attribute.map;
  if (!mapName) return [];

  const mapData = echarts.getMap(mapName);
  if (!mapData?.geoJson?.features) return [];

  return mapData.geoJson.features.map((feature: any) => feature.properties?.name).filter(Boolean);
});

// 区域颜色相关方法
const onRegionColorsToggle = () => {
  console.log('区域颜色开关切换:', enableRegionColors.value);

  if (enableRegionColors.value) {
    // 启用时初始化区域颜色
    initializeRegionColors();

    // 如果没有现有颜色，给前几个区域设置默认颜色作为示例
    if (Object.keys(regionColors.value).length === 0 && availableRegions.value.length > 0) {
      const defaultColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];
      availableRegions.value.slice(0, 4).forEach((region: string, index: number) => {
        regionColors.value[region] = defaultColors[index] || '#FF6B6B';
      });
      console.log('设置默认区域颜色:', regionColors.value);
      updateRegionColorsAttribute();
    }
  } else {
    // 禁用时清空区域颜色配置
    regionColors.value = {};
    updateRegionColorsAttribute();
  }
};

const initializeRegionColors = () => {
  const existingColors = props.attribute.regionColors || {};
  availableRegions.value.forEach((region: string) => {
    if (!regionColors.value[region]) {
      regionColors.value[region] = existingColors[region] || '';
    }
  });
};

const onRegionColorChange = () => {
  updateRegionColorsAttribute();
};

const updateRegionColorsAttribute = async () => {
  // 确保正确更新区域颜色配置
  const colorsToUpdate = JSON.parse(JSON.stringify(regionColors.value));

  console.log('更新区域颜色配置:', colorsToUpdate);

  // 直接修改 attributeCopy
  attributeCopy.value.regionColors = colorsToUpdate;

  // 等待下一个tick确保更新完成
  await nextTick();

  console.log('区域颜色配置已更新到 attributeCopy');
};

const resetRegionColor = (region: string) => {
  regionColors.value[region] = '';
  updateRegionColorsAttribute();
};

const resetAllRegionColors = () => {
  availableRegions.value.forEach((region: string) => {
    regionColors.value[region] = '';
  });
  updateRegionColorsAttribute();
};

const randomRegionColors = () => {
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'];

  availableRegions.value.forEach((region: string, index: number) => {
    regionColors.value[region] = colors[index % colors.length];
  });
  updateRegionColorsAttribute();
};

// 监听地图变化，重新初始化区域列表
watch(
  () => props.attribute.map,
  () => {
    if (enableRegionColors.value) {
      regionColors.value = {};
      initializeRegionColors();
    }
  }
);

// 初始化区域颜色
onMounted(() => {
  console.log('配置选项组件初始化，检查区域颜色:', props.attribute.regionColors);

  if (props.attribute.regionColors && Object.keys(props.attribute.regionColors).length > 0) {
    enableRegionColors.value = true;
    regionColors.value = { ...props.attribute.regionColors };
    console.log('初始化区域颜色:', regionColors.value);
  }
});

// 监听属性变化
watch(
  () => props.attribute.regionColors,
  (newColors) => {
    if (newColors && Object.keys(newColors).length > 0) {
      regionColors.value = { ...newColors };
      enableRegionColors.value = true;
    }
  },
  { deep: true }
);

// 处理数字输入
const handleInputKeydown = (event: KeyboardEvent) => {
  const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];
  const isNumber = /[0-9]/.test(event.key);
  const isDot = event.key === '.';
  const isMinus = event.key === '-';

  if (!allowedKeys.includes(event.key) && !isNumber && !isDot && !isMinus) {
    event.preventDefault();
  }
};
</script>

<style scoped>
.el-form {
  max-height: 600px;
  overflow-y: auto;
}

.el-divider {
  margin: 15px 0;
}

.region-colors-panel {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  margin: 10px 0;
  max-height: 300px;
  overflow-y: auto;
}

.region-color-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.region-color-item:last-child {
  border-bottom: none;
}

.region-name {
  flex: 1;
  font-weight: 500;
  color: #333;
  margin-right: 10px;
}

.region-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.status-colors-panel {
  background: #f9f9f9;
  border-radius: 8px;
  margin: 10px 0;
}

.color-config-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 自定义符号配置 */
.custom-symbol-collapse {
  border: none;
  background: transparent;
  margin: 10px 0;
}

.custom-symbol-collapse :deep(.el-collapse-item__header) {
  background: #f0f8ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  margin-bottom: 8px;
  padding: 0 16px;
  font-weight: 500;
  color: #1890ff;
}

.custom-symbol-collapse :deep(.el-collapse-item__content) {
  padding: 16px;
  background: #fff;
  border: 1px solid #91d5ff;
  border-top: none;
  border-radius: 0 0 6px 6px;
  margin-top: -8px;
  display: flex;
  flex-direction: column;
}

/* 状态配置优化样式 */
.status-collapse {
  border: none;
  background: transparent;
}

.status-collapse :deep(.el-collapse-item__header) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
  padding: 0 16px;
  font-weight: 500;
  color: #495057;
}

.status-collapse :deep(.el-collapse-item__content) {
  padding: 16px;
  display: flex;
  flex-direction: column;
  background: #fff;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 6px 6px;
  margin-top: -8px;
}

.status-config-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.status-label {
  min-width: 60px;
  font-weight: 500;
  color: #495057;
  font-size: 13px;
}

.symbols-grid {
  display: flex;
  flex-direction: column;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
}

.symbol-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.symbol-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.custom-config {
  margin-top: 12px;
  padding: 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.path-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.path-controls .el-slider {
  flex: 1;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .status-config-grid,
  .symbols-grid {
    display: flex;
    flex-direction: column;
  }

  .status-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .status-label {
    min-width: auto;
    text-align: center;
  }
}

/* 区域颜色配置 */
.region-collapse {
  border: none;
  background: transparent;
  display: flex;
  flex-direction: column;
  margin: 10px 0;
}

.region-collapse :deep(.el-collapse-item__header) {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  margin-bottom: 8px;
  padding: 0 16px;
  font-weight: 500;
  color: #fa8c16;
}

.region-collapse :deep(.el-collapse-item__content) {
  padding: 16px;
  background: #fff;
  border: 1px solid #ffd591;
  border-top: none;
  border-radius: 0 0 6px 6px;
  margin-top: -8px;
}

.region-grid {
  display: flex;
  flex-direction: column;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.region-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.region-name {
  flex: 1;
  font-weight: 500;
  color: #333;
  font-size: 13px;
  margin-right: 8px;
}

.region-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.region-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
  justify-content: center;
}
</style>
