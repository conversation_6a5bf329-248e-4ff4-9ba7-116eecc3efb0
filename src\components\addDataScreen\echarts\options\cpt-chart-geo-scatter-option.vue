<!-- GEO SVG 散点图配置选项 -->
<template>
  <el-form label-width="120px" style="padding: 20px">
    <!-- 标题配置 -->
    <el-divider content-position="left">标题配置</el-divider>
    <el-form-item label="主标题">
      <el-input v-model="attributeCopy.titleText" />
    </el-form-item>
    <el-form-item label="副标题">
      <el-input v-model="attributeCopy.subtext" />
    </el-form-item>
    <el-form-item label="标题位置">
      <el-select v-model="attributeCopy.titleLeft">
        <el-option label="左对齐" value="left" />
        <el-option label="居中" value="center" />
        <el-option label="右对齐" value="right" />
      </el-select>
    </el-form-item>
    <el-form-item label="标题颜色">
      <el-color-picker v-model="attributeCopy.titleColor" />
    </el-form-item>
    <el-form-item label="标题大小">
      <el-input v-model="attributeCopy.titleFontSize" type="number" @keydown="handleInputKeydown" />
    </el-form-item>
    
    <!-- 地图配置 -->
    <el-divider content-position="left">地图配置</el-divider>
    <el-form-item label="地图类型">
      <el-select v-model="attributeCopy.map">
        <el-option label="中国" value="china" />
        <el-option label="贵州省" value="guizhou" />
        <el-option label="常州市" value="changZhou" />
        <el-option label="四会市" value="sihui" />
        <el-option label="贵阳市(3D)" value="areaGuiYang" />
        <el-option label="中国(3D)" value="areaChina" />
        <el-option label="贵州省(3D)" value="areaGuizhou" />
        <el-option label="云岩区(3D)" value="areaYunYanQu" />
        <el-option label="延川县(3D)" value="areaYanChuan" />
        <el-option label="毕节市(3D)" value="areaBiJie" />
      </el-select>
    </el-form-item>
    <el-form-item label="允许缩放平移">
      <el-switch v-model="attributeCopy.roam" />
    </el-form-item>
    <el-form-item label="初始缩放">
      <el-slider v-model="attributeCopy.zoom" :min="0.5" :max="3" :step="0.1" />
    </el-form-item>
    <el-form-item label="地图区域颜色">
      <el-color-picker v-model="attributeCopy.mapAreaColor" />
    </el-form-item>
    <el-form-item label="地图边界颜色">
      <el-color-picker v-model="attributeCopy.mapBorderColor" />
    </el-form-item>
    <el-form-item label="边界宽度">
      <el-input v-model="attributeCopy.mapBorderWidth" type="number" @keydown="handleInputKeydown" />
    </el-form-item>
    
    <!-- 散点配置 -->
    <el-divider content-position="left">散点配置</el-divider>
    <el-form-item label="系列名称">
      <el-input v-model="attributeCopy.seriesName" />
    </el-form-item>
    <el-form-item label="散点类型">
      <el-select v-model="attributeCopy.scatterType">
        <el-option label="普通散点" value="scatter" />
        <el-option label="涟漪散点" value="effectScatter" />
      </el-select>
    </el-form-item>
    <el-form-item label="散点符号">
      <el-select v-model="attributeCopy.scatterSymbol">
        <el-option label="圆形" value="circle" />
        <el-option label="矩形" value="rect" />
        <el-option label="三角形" value="triangle" />
        <el-option label="菱形" value="diamond" />
        <el-option label="针形" value="pin" />
        <el-option label="箭头" value="arrow" />
      </el-select>
    </el-form-item>
    <el-form-item label="基础大小">
      <el-input v-model="attributeCopy.scatterBaseSize" type="number" @keydown="handleInputKeydown" />
    </el-form-item>
    <el-form-item label="大小倍数">
      <el-slider v-model="attributeCopy.scatterSizeMultiplier" :min="0.1" :max="2" :step="0.1" />
    </el-form-item>
    <el-form-item label="散点颜色">
      <el-color-picker v-model="attributeCopy.scatterColor" />
    </el-form-item>
    <el-form-item label="散点透明度">
      <el-slider v-model="attributeCopy.scatterOpacity" :min="0" :max="1" :step="0.1" />
    </el-form-item>
    
    <!-- 散点标签 -->
    <el-divider content-position="left">散点标签</el-divider>
    <el-form-item label="显示标签">
      <el-switch v-model="attributeCopy.showScatterLabel" />
    </el-form-item>
    <el-form-item label="标签位置">
      <el-select v-model="attributeCopy.scatterLabelPosition">
        <el-option label="顶部" value="top" />
        <el-option label="底部" value="bottom" />
        <el-option label="左侧" value="left" />
        <el-option label="右侧" value="right" />
        <el-option label="内部" value="inside" />
      </el-select>
    </el-form-item>
    <el-form-item label="标签格式">
      <el-select v-model="attributeCopy.scatterLabelFormat">
        <el-option label="仅名称" value="name" />
        <el-option label="仅数值" value="value" />
        <el-option label="名称+数值" value="both" />
      </el-select>
    </el-form-item>
    <el-form-item label="标签颜色">
      <el-color-picker v-model="attributeCopy.scatterLabelColor" />
    </el-form-item>
    <el-form-item label="标签大小">
      <el-input v-model="attributeCopy.scatterLabelSize" type="number" @keydown="handleInputKeydown" />
    </el-form-item>
    
    <!-- 涟漪效果 -->
    <el-divider content-position="left">涟漪效果</el-divider>
    <el-form-item label="涟漪类型">
      <el-select v-model="attributeCopy.rippleBrushType">
        <el-option label="描边" value="stroke" />
        <el-option label="填充" value="fill" />
      </el-select>
    </el-form-item>
    <el-form-item label="涟漪缩放">
      <el-slider v-model="attributeCopy.rippleScale" :min="1" :max="5" :step="0.5" />
    </el-form-item>
    <el-form-item label="涟漪周期">
      <el-slider v-model="attributeCopy.ripplePeriod" :min="1" :max="10" :step="1" />
    </el-form-item>
    
    <!-- 视觉映射 -->
    <el-divider content-position="left">视觉映射</el-divider>
    <el-form-item label="显示视觉映射">
      <el-switch v-model="attributeCopy.showVisualMap" />
    </el-form-item>
    <el-form-item label="最小值">
      <el-input v-model="attributeCopy.visualMapMin" type="number" @keydown="handleInputKeydown" />
    </el-form-item>
    <el-form-item label="最大值">
      <el-input v-model="attributeCopy.visualMapMax" type="number" @keydown="handleInputKeydown" />
    </el-form-item>
    <el-form-item label="最小值颜色">
      <el-color-picker v-model="attributeCopy.visualMapMinColor" />
    </el-form-item>
    <el-form-item label="最大值颜色">
      <el-color-picker v-model="attributeCopy.visualMapMaxColor" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-chart-geo-scatter-option'
});

const props = defineProps<{
  attribute: Record<string, any>;
}>();

const emit = defineEmits(['update-attribute']);

const attributeCopy = computed({
  get() {
    return props.attribute;
  },
  set(val) {
    emit('update-attribute', val);
  }
});

// 处理数字输入
const handleInputKeydown = (event: KeyboardEvent) => {
  const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];
  const isNumber = /[0-9]/.test(event.key);
  const isDot = event.key === '.';
  const isMinus = event.key === '-';
  
  if (!allowedKeys.includes(event.key) && !isNumber && !isDot && !isMinus) {
    event.preventDefault();
  }
};
</script>

<style scoped>
.el-form {
  max-height: 600px;
  overflow-y: auto;
}

.el-divider {
  margin: 15px 0;
}
</style>
