<!-- 毕节底图测试 -->
<template>
  <div class="test-container">
    <h2>毕节底图显示测试</h2>
    
    <div class="controls">
      <el-button @click="testPureMap">测试纯地图</el-button>
      <el-button @click="testWithScatter">测试带散点</el-button>
      <el-button @click="adjustView">调整视图</el-button>
      <el-button @click="showMapInfo">显示地图信息</el-button>
    </div>
    
    <div class="view-controls">
      <label>缩放级别: </label>
      <el-slider v-model="zoomLevel" :min="0.5" :max="10" :step="0.1" @change="updateView" />
      <span>{{ zoomLevel }}</span>
    </div>
    
    <div class="chart-wrapper">
      <div id="bijie-base-test" style="width: 100%; height: 100%;"></div>
    </div>
    
    <div class="status-info">
      <h3>状态信息</h3>
      <p><strong>地图状态:</strong> <span :class="mapStatus.class">{{ mapStatus.text }}</span></p>
      <p><strong>特征数量:</strong> {{ mapStatus.features }}</p>
      <p><strong>当前缩放:</strong> {{ zoomLevel }}</p>
      <p><strong>中心点:</strong> [{{ centerPoint.join(', ') }}]</p>
    </div>
    
    <div class="debug-output">
      <h3>调试输出</h3>
      <div class="logs">
        <div v-for="(log, index) in logs" :key="index" :class="['log', log.type]">
          {{ log.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';

defineOptions({
  name: 'bijie-base-map-test'
});

let chart: any = null;
const logs = ref<Array<{type: string, message: string}>>([]);
const zoomLevel = ref(3);
const centerPoint = ref([105.5, 27.0]);

const mapStatus = computed(() => {
  const mapData = echarts.getMap('bijie');
  if (mapData) {
    return {
      class: 'success',
      text: '已注册',
      features: mapData.geoJson?.features?.length || 0
    };
  } else {
    return {
      class: 'error',
      text: '未注册',
      features: 0
    };
  }
});

const addLog = (type: string, message: string) => {
  logs.value.push({ type, message });
  console.log(`[${type.toUpperCase()}] ${message}`);
};

const testPureMap = () => {
  if (!chart) return;
  
  addLog('info', '测试纯毕节地图（无散点）...');
  
  const option = {
    title: {
      text: '毕节市地图',
      left: 'center',
      top: 10,
      textStyle: {
        color: '#333',
        fontSize: 16
      }
    },
    geo: {
      map: 'bijie',
      roam: true,
      zoom: zoomLevel.value,
      center: centerPoint.value,
      label: {
        show: true,
        fontSize: 12,
        color: '#333',
        fontWeight: 'bold'
      },
      itemStyle: {
        areaColor: '#e6f7ff',
        borderColor: '#1890ff',
        borderWidth: 2
      },
      emphasis: {
        label: {
          show: true,
          color: '#000',
          fontSize: 14
        },
        itemStyle: {
          areaColor: '#bae7ff',
          borderColor: '#096dd9',
          borderWidth: 3
        }
      }
    }
  };
  
  try {
    chart.clear();
    chart.setOption(option);
    addLog('success', '纯地图渲染成功');
  } catch (error) {
    addLog('error', `地图渲染失败: ${error}`);
  }
};

const testWithScatter = () => {
  if (!chart) return;
  
  addLog('info', '测试带散点的毕节地图...');
  
  const scatterData = [
    { name: '七星关区', lng: 105.284852, lat: 27.302085, value: 100 },
    { name: '大方县', lng: 105.609254, lat: 27.143521, value: 80 },
    { name: '黔西市', lng: 106.038299, lat: 27.024923, value: 90 },
    { name: '金沙县', lng: 106.222103, lat: 27.459693, value: 70 }
  ];
  
  const option = {
    title: {
      text: '毕节市散点图',
      left: 'center',
      top: 10,
      textStyle: {
        color: '#333',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        if (params.seriesType === 'scatter') {
          return `${params.name}<br/>经度: ${params.value[0]}<br/>纬度: ${params.value[1]}<br/>数值: ${params.value[2]}`;
        }
        return params.name;
      }
    },
    geo: {
      map: 'bijie',
      roam: true,
      zoom: zoomLevel.value,
      center: centerPoint.value,
      label: {
        show: true,
        fontSize: 10,
        color: '#666'
      },
      itemStyle: {
        areaColor: '#f0f0f0',
        borderColor: '#999',
        borderWidth: 1
      },
      emphasis: {
        itemStyle: {
          areaColor: '#e0e0e0'
        }
      }
    },
    series: [
      {
        name: '数据点',
        type: 'scatter',
        coordinateSystem: 'geo',
        symbol: 'circle',
        symbolSize: 15,
        itemStyle: {
          color: '#ff6b6b',
          opacity: 0.8,
          borderWidth: 2,
          borderColor: '#fff'
        },
        label: {
          show: true,
          position: 'top',
          color: '#333',
          fontSize: 12,
          fontWeight: 'bold',
          formatter: (params: any) => params.name
        },
        data: scatterData.map(item => ({
          name: item.name,
          value: [item.lng, item.lat, item.value]
        }))
      }
    ]
  };
  
  try {
    chart.clear();
    chart.setOption(option);
    addLog('success', `带散点地图渲染成功，显示 ${scatterData.length} 个数据点`);
  } catch (error) {
    addLog('error', `带散点地图渲染失败: ${error}`);
  }
};

const updateView = () => {
  addLog('info', `更新视图: 缩放=${zoomLevel.value}, 中心=[${centerPoint.value.join(', ')}]`);
  // 重新渲染当前地图
  testWithScatter();
};

const adjustView = () => {
  // 自动调整到最佳视图
  zoomLevel.value = 4;
  centerPoint.value = [105.5, 27.0];
  addLog('info', '调整到最佳视图');
  updateView();
};

const showMapInfo = () => {
  const mapData = echarts.getMap('bijie');
  if (mapData) {
    addLog('info', `毕节地图信息: ${mapData.geoJson?.features?.length} 个区县`);
    
    const features = mapData.geoJson?.features || [];
    const districts = features.map((f: any) => f.properties?.name).filter(Boolean);
    addLog('info', `区县列表: ${districts.join(', ')}`);
    
    // 计算边界
    let minLng = Infinity, maxLng = -Infinity;
    let minLat = Infinity, maxLat = -Infinity;
    
    features.forEach((feature: any) => {
      const center = feature.properties?.center;
      if (center && Array.isArray(center)) {
        minLng = Math.min(minLng, center[0]);
        maxLng = Math.max(maxLng, center[0]);
        minLat = Math.min(minLat, center[1]);
        maxLat = Math.max(maxLat, center[1]);
      }
    });
    
    addLog('info', `坐标范围: 经度 ${minLng.toFixed(3)} ~ ${maxLng.toFixed(3)}, 纬度 ${minLat.toFixed(3)} ~ ${maxLat.toFixed(3)}`);
    
    // 检查地图数据格式
    const firstFeature = features[0];
    if (firstFeature) {
      const hasCoordinates = !!firstFeature.geometry?.coordinates;
      const hasEncodeOffsets = !!firstFeature.geometry?.encodeOffsets;
      addLog('info', `数据格式: ${hasCoordinates ? 'GeoJSON坐标' : ''}${hasEncodeOffsets ? 'ECharts压缩' : ''}`);
    }
  } else {
    addLog('error', '毕节地图未注册');
  }
};

onMounted(() => {
  chart = echarts.init(document.getElementById('bijie-base-test'));
  
  addLog('info', '页面初始化完成');
  showMapInfo();
  testPureMap();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.view-controls {
  margin: 20px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.view-controls label {
  font-weight: bold;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.status-info {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.status-info .success {
  color: #52c41a;
  font-weight: bold;
}

.status-info .error {
  color: #ff4d4f;
  font-weight: bold;
}

.debug-output {
  background: #f0f0f0;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.logs {
  background: #000;
  color: #fff;
  padding: 15px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log {
  margin: 2px 0;
}

.log.error {
  color: #ff6b6b;
}

.log.success {
  color: #51cf66;
}

.log.info {
  color: #74c0fc;
}

h2, h3 {
  color: #333;
}
</style>
