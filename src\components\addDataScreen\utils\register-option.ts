import { App } from 'vue';

// dataV

import cpt_dataV_border_option from '@/components/addDataScreen/dataV/options/cpt-dataV-border-option.vue';
import cpt_dataV_scrollTable_option from '@/components/addDataScreen/dataV/options/cpt-dataV-scrollTable-option.vue';
import cpt_dataV_scrollList_option from '@/components/addDataScreen/dataV/options/cpt-dataV-scrollList-option.vue';
import cpt_dataV_waterLevel_option from '@/components/addDataScreen/dataV/options/cpt-dataV-waterLevel-option.vue';
import cpt_dataV_decoration_option from '@/components/addDataScreen/dataV/options/cpt-dataV-decoration-option.vue';
import cpt_dataV_digitalFlop_option from '@/components/addDataScreen/dataV/options/cpt-dataV-digitalFlop-option.vue';
import cpt_dataV_percentPond_option from '@/components/addDataScreen/dataV/options/cpt-dataV-percentPond-option.vue';
import cpt_dataV_activeRing_option from '@/components/addDataScreen/dataV/options/cpt-dataV-activeRing-option.vue';

// element
import cpt_button_option from '@/components/addDataScreen/element/options/cpt-button-option.vue';
import cpt_text_option from '@/components/addDataScreen/element/options/cpt-text-option.vue';
import cpt_select_option from '@/components/addDataScreen/element/options/cpt-select-option.vue';
import cpt_module_option from '@/components/addDataScreen/element/options/cpt-module-option.vue';
import cpt_image_option from '@/components/addDataScreen/element/options/cpt-image-option.vue';
import cpt_carousel_option from '@/components/addDataScreen/element/options/cpt-carousel-option.vue';
import cpt_num_option from '@/components/addDataScreen/element/options/cpt-num-option.vue';
import cpt_rect_num_option from '@/components/addDataScreen/element/options/cpt-rect-num-option.vue';
import cpt_scroll_table_option from '@/components/addDataScreen/element/options/cpt-scroll-table-option.vue';
import cpt_iframe_option from '@/components/addDataScreen/element/options/cpt-iframe-option.vue';
import cpt_organization_option from '@/components/addDataScreen/element/options/cpt-organization-option.vue';

// echarts
import cpt_chart_column_option from '@/components/addDataScreen/echarts/options/cpt-chart-column-option.vue';
import cpt_chart_pie_option from '@/components/addDataScreen/echarts/options/cpt-chart-pie-option.vue';
import cpt_chart_clock_option from '@/components/addDataScreen/echarts/options/cpt-chart-clock-option.vue';
import cpt_chart_mapGc_option from '@/components/addDataScreen/echarts/options/cpt-chart-map-gc-option.vue';
import cpt_chart_mapMigrate_option from '@/components/addDataScreen/echarts/options/cpt-chart-map-migrate-option.vue';
import cpt_chart_tdColumn_option from '@/components/addDataScreen/echarts/options/cpt-chart-td-column-option.vue';
import cpt_chart_gauge_option from '@/components/addDataScreen/echarts/options/cpt-chart-gauge-option.vue';
import cpt_chart_3dmap_option from '@/components/addDataScreen/echarts/options/cpt-chart-3dmap-option.vue';
import cpt_chart_radar_option from '@/components/addDataScreen/echarts/options/cpt-chart-radar-option.vue';
import cpt_chart_gl_bar3d_option from '@/components/addDataScreen/echarts/options/cpt-chart-gl-bar3d-option.vue';
import cpt_chart_line_option from '@/components/addDataScreen/echarts/options/cpt-chart-line-option.vue';
import cpt_chart_geo_scatter_option from '@/components/addDataScreen/echarts/options/cpt-chart-geo-scatter-option.vue';

// three
import cpt_threeJs_dom_option from '@/components/addDataScreen/three/options/cpt-threeJs-dom-option.vue';

const cptOptionsList = [
  cpt_button_option,
  cpt_text_option,
  cpt_select_option,
  cpt_module_option,
  cpt_image_option,
  cpt_carousel_option,
  cpt_chart_column_option,
  cpt_chart_pie_option,
  cpt_chart_clock_option,
  cpt_dataV_border_option,
  cpt_dataV_scrollTable_option,
  cpt_dataV_scrollList_option,
  cpt_chart_mapGc_option,
  cpt_chart_mapMigrate_option,
  cpt_dataV_waterLevel_option,
  cpt_dataV_decoration_option,
  cpt_chart_line_option,
  cpt_dataV_digitalFlop_option,
  cpt_dataV_percentPond_option,
  cpt_iframe_option,
  cpt_chart_tdColumn_option,
  cpt_dataV_activeRing_option,
  cpt_chart_gauge_option,
  cpt_threeJs_dom_option,
  cpt_num_option,
  cpt_rect_num_option,
  cpt_scroll_table_option,
  cpt_chart_3dmap_option,
  cpt_chart_radar_option,
  cpt_chart_gl_bar3d_option,
  cpt_chart_geo_scatter_option,
  cpt_organization_option
];

export default {
  install(app: App): void {
    cptOptionsList.forEach((component) => {
      app.component(component.name, component);
    });
  }
};
