<!-- 地图切换测试页面 -->
<template>
  <div class="test-container">
    <h2>地图切换功能测试</h2>
    
    <div class="controls">
      <div class="map-selector">
        <label>选择地图:</label>
        <el-select v-model="currentMap" @change="onMapChange">
          <el-option 
            v-for="map in availableMaps" 
            :key="map.value" 
            :label="map.label" 
            :value="map.value"
          />
        </el-select>
      </div>
      
      <div class="quick-buttons">
        <el-button @click="switchToChina">中国</el-button>
        <el-button @click="switchToGuizhou">贵州</el-button>
        <el-button @click="switchToChangzhou">常州</el-button>
        <el-button @click="testMapRegistration">测试地图注册</el-button>
      </div>
    </div>
    
    <div class="status-info">
      <p><strong>当前地图:</strong> {{ currentMapInfo.label }}</p>
      <p><strong>地图状态:</strong> <span :class="mapStatus.class">{{ mapStatus.text }}</span></p>
      <p><strong>数据点数量:</strong> {{ currentData.length }}</p>
    </div>
    
    <div class="chart-wrapper">
      <cpt-chart-geo-scatter 
        :width="800" 
        :height="600" 
        :option="chartOption"
        :key="chartKey"
      />
    </div>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <div class="debug-item">
        <strong>已注册的地图:</strong>
        <ul>
          <li v-for="map in registeredMaps" :key="map" :class="{ active: map === currentMap }">
            {{ map }} {{ map === currentMap ? '(当前)' : '' }}
          </li>
        </ul>
      </div>
      
      <div class="debug-item">
        <strong>当前配置:</strong>
        <pre>{{ JSON.stringify(chartOption.attribute, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';
import * as echarts from 'echarts';

defineOptions({
  name: 'map-switch-test'
});

const currentMap = ref('china');
const chartKey = ref(0); // 用于强制重新渲染组件

const availableMaps = [
  { label: '中国', value: 'china' },
  { label: '贵州省', value: 'guizhou' },
  { label: '常州市', value: 'changZhou' },
  { label: '四会市', value: 'sihui' },
  { label: '贵阳市(3D)', value: 'areaGuiYang' },
  { label: '中国(3D)', value: 'areaChina' },
  { label: '贵州省(3D)', value: 'areaGuizhou' }
];

// 不同地图的测试数据
const mapDataSets = {
  china: [
    { name: '北京', lng: 116.46, lat: 39.92, value: 150 },
    { name: '上海', lng: 121.48, lat: 31.22, value: 200 },
    { name: '广州', lng: 113.23, lat: 23.16, value: 120 },
    { name: '深圳', lng: 114.07, lat: 22.62, value: 180 }
  ],
  guizhou: [
    { name: '贵阳市', lng: 106.713478, lat: 26.578343, value: 150 },
    { name: '遵义市', lng: 107.297104, lat: 27.725654, value: 120 },
    { name: '毕节市', lng: 105.300492, lat: 27.302612, value: 100 },
    { name: '铜仁市', lng: 109.191555, lat: 27.718346, value: 90 }
  ],
  changZhou: [
    { name: '天宁区', lng: 119.946973, lat: 31.772752, value: 80 },
    { name: '钟楼区', lng: 119.926762, lat: 31.795048, value: 75 },
    { name: '新北区', lng: 119.973129, lat: 31.810689, value: 70 }
  ],
  default: [
    { name: '测试点1', lng: 110, lat: 30, value: 100 },
    { name: '测试点2', lng: 115, lat: 35, value: 120 }
  ]
};

const currentData = computed(() => {
  return mapDataSets[currentMap.value as keyof typeof mapDataSets] || mapDataSets.default;
});

const currentMapInfo = computed(() => {
  return availableMaps.find(map => map.value === currentMap.value) || { label: '未知', value: '' };
});

const mapStatus = computed(() => {
  const mapData = echarts.getMap(currentMap.value);
  if (mapData) {
    return { class: 'success', text: '已注册' };
  } else {
    return { class: 'error', text: '未注册' };
  }
});

const registeredMaps = computed(() => {
  // 获取所有已注册的地图
  const maps = ['china', 'guizhou', 'changZhou', 'sihui', 'areaGuiYang', 'areaChina', 'areaGuizhou', 'areaYunYanQu', 'areaYanChuan', 'areaBiJie'];
  return maps.filter(map => echarts.getMap(map));
});

const chartOption = computed(() => ({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify(currentData.value)
  },
  attribute: {
    titleText: `${currentMapInfo.value.label}散点分布`,
    subtext: '地图切换测试',
    titleLeft: 'center',
    titleTop: 10,
    titleFontSize: 18,
    titleColor: '#333',
    subTitleColor: '#666',
    subTitleFontSize: 12,
    
    map: currentMap.value,
    roam: true,
    zoom: 1.2,
    center: getMapCenter(currentMap.value),
    
    showMapLabel: true,
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,
    
    showMapEmphasisLabel: true,
    mapEmphasisLabelColor: '#000',
    mapEmphasisAreaColor: '#e0e0e0',
    mapEmphasisBorderColor: '#666',
    
    seriesName: '数据点',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 10,
    scatterSizeMultiplier: 0.8,
    scatterColor: '#ff6b6b',
    scatterOpacity: 0.8,
    scatterBorderWidth: 2,
    scatterBorderColor: '#fff',
    
    scatterEmphasisColor: '#ff4757',
    scatterEmphasisShadowBlur: 10,
    scatterEmphasisShadowColor: '#ff4757',
    
    showScatterLabel: true,
    scatterLabelPosition: 'top',
    scatterLabelColor: '#333',
    scatterLabelSize: 12,
    scatterLabelFormat: 'name',
    
    showVisualMap: false
  }
}));

const onMapChange = () => {
  console.log(`地图切换到: ${currentMap.value}`);
  // 强制重新渲染组件
  chartKey.value++;
};

const switchToChina = () => {
  currentMap.value = 'china';
  onMapChange();
};

const switchToGuizhou = () => {
  currentMap.value = 'guizhou';
  onMapChange();
};

const switchToChangzhou = () => {
  currentMap.value = 'changZhou';
  onMapChange();
};

const testMapRegistration = () => {
  console.log('=== 地图注册测试 ===');
  availableMaps.forEach(map => {
    const mapData = echarts.getMap(map.value);
    console.log(`${map.label} (${map.value}):`, mapData ? '已注册' : '未注册');
  });
};

const getMapCenter = (mapType: string) => {
  const centers: Record<string, number[]> = {
    china: [104, 35],
    guizhou: [106.7, 26.6],
    changZhou: [119.95, 31.78],
    sihui: [112.7, 23.3],
    areaGuiYang: [106.7, 26.6],
    areaChina: [104, 35],
    areaGuizhou: [106.7, 26.6]
  };
  return centers[mapType] || [104, 35];
};

onMounted(() => {
  testMapRegistration();
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.map-selector {
  margin-bottom: 15px;
}

.map-selector label {
  margin-right: 10px;
  font-weight: bold;
}

.quick-buttons {
  display: flex;
  gap: 10px;
}

.status-info {
  background: #e6f7ff;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #1890ff;
}

.status-info .success {
  color: #52c41a;
  font-weight: bold;
}

.status-info .error {
  color: #ff4d4f;
  font-weight: bold;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.debug-info {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.debug-item {
  margin: 15px 0;
}

.debug-item ul {
  list-style: none;
  padding: 0;
  margin: 10px 0;
}

.debug-item li {
  padding: 5px 10px;
  margin: 2px 0;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.debug-item li.active {
  background: #e6f7ff;
  border-color: #1890ff;
  font-weight: bold;
}

.debug-item pre {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
}

h2, h3 {
  color: #333;
}
</style>
