<!-- 简单的散点图测试 -->
<template>
  <div class="test-container">
    <h2>散点图组件简单测试</h2>
    
    <div class="status">
      <p>组件状态: <span :class="componentStatus.class">{{ componentStatus.text }}</span></p>
      <p>错误信息: <span class="error">{{ errorMessage }}</span></p>
    </div>
    
    <div class="chart-wrapper">
      <cpt-chart-geo-scatter 
        :width="600" 
        :height="400" 
        :option="testOption"
        @error="handleError"
      />
    </div>
    
    <div class="controls">
      <el-button @click="refreshData">刷新数据</el-button>
      <el-button @click="changeMap">切换地图</el-button>
    </div>
    
    <div class="data-info">
      <h3>测试数据</h3>
      <pre>{{ JSON.stringify(testOption, null, 2) }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';

defineOptions({
  name: 'geo-scatter-simple-test'
});

const errorMessage = ref('');
const currentMapIndex = ref(0);

const maps = ['china', 'guizhou', 'changZhou'];

const testOption = ref({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify([
      { name: '北京', lng: 116.46, lat: 39.92, value: 150 },
      { name: '上海', lng: 121.48, lat: 31.22, value: 200 },
      { name: '广州', lng: 113.23, lat: 23.16, value: 120 }
    ])
  },
  attribute: {
    titleText: '测试散点地图',
    subtext: '简单测试',
    titleLeft: 'center',
    titleTop: 10,
    titleFontSize: 16,
    titleColor: '#333',
    subTitleColor: '#666',
    subTitleFontSize: 12,
    
    map: 'china',
    roam: true,
    zoom: 1.2,
    center: [104, 35],
    
    showMapLabel: true,
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,
    
    seriesName: '测试数据',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 8,
    scatterSizeMultiplier: 0.5,
    scatterColor: '#ff6b6b',
    scatterOpacity: 0.8,
    scatterBorderWidth: 1,
    scatterBorderColor: '#fff',
    
    showScatterLabel: true,
    scatterLabelPosition: 'top',
    scatterLabelColor: '#333',
    scatterLabelSize: 12,
    scatterLabelFormat: 'name',
    
    showVisualMap: false
  }
});

const componentStatus = computed(() => {
  if (errorMessage.value) {
    return { class: 'error', text: '错误' };
  }
  return { class: 'success', text: '正常' };
});

const handleError = (error: any) => {
  errorMessage.value = error.message || error.toString();
  console.error('组件错误:', error);
};

const refreshData = () => {
  const newData = [
    { name: '测试点1', lng: 110 + Math.random() * 10, lat: 30 + Math.random() * 10, value: Math.floor(Math.random() * 200) },
    { name: '测试点2', lng: 110 + Math.random() * 10, lat: 30 + Math.random() * 10, value: Math.floor(Math.random() * 200) },
    { name: '测试点3', lng: 110 + Math.random() * 10, lat: 30 + Math.random() * 10, value: Math.floor(Math.random() * 200) }
  ];
  
  testOption.value.cptDataForm.dataText = JSON.stringify(newData);
  errorMessage.value = '';
};

const changeMap = () => {
  currentMapIndex.value = (currentMapIndex.value + 1) % maps.length;
  testOption.value.attribute.map = maps[currentMapIndex.value];
  testOption.value.attribute.titleText = `${maps[currentMapIndex.value]} 散点图`;
  errorMessage.value = '';
};

// 错误处理
window.addEventListener('error', (event) => {
  if (event.message.includes('cpt-chart-geo-scatter')) {
    errorMessage.value = event.message;
  }
});

onMounted(() => {
  console.log('散点图测试页面已加载');
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.status {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.status .success {
  color: #67c23a;
  font-weight: bold;
}

.status .error {
  color: #f56c6c;
  font-weight: bold;
}

.chart-wrapper {
  width: 600px;
  height: 400px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.data-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
}

.data-info pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

h2, h3 {
  color: #333;
}

.error {
  color: #f56c6c;
}
</style>
