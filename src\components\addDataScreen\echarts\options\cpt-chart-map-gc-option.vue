<template>
  <el-form label-width="100px">
    <el-form-item label="地图">
      <el-select v-model="attributeCopy.map">
        <el-option label="全国" value="china" />
        <el-option label="常州" value="changZhou" />
        <el-option label="贵州" value="guizhou" />
        <el-option label="四会市" value="sihui" />
        <el-option label="四会市地籍子区" value="sihuidijizq" />
        <el-option label="陕西省延安市延川县" value="areaYanChuan" />
      </el-select>
    </el-form-item>
    <el-form-item label="标题">
      <el-input v-model="attributeCopy.titleText" />
    </el-form-item>
    <el-form-item label="标题颜色">
      <el-color-picker v-model="attributeCopy.titleColor" show-alpha />
    </el-form-item>
    <el-form-item label="标题大小">
      <el-input-number v-model="attributeCopy.titleFontSize" :min="4" :max="100" />
    </el-form-item>
    <el-form-item label="副标题">
      <el-input v-model="attributeCopy.subtext" />
    </el-form-item>
    <el-form-item label="副标题颜色">
      <el-color-picker v-model="attributeCopy.subTitleColor" show-alpha />
    </el-form-item>
    <el-form-item label="副标题大小">
      <el-input-number v-model="attributeCopy.subTitleFontSize" :min="4" :max="100" />
    </el-form-item>
    <el-form-item label="标题位置(左)">
      <el-input v-model="attributeCopy.titleLeft" />
    </el-form-item>
    <el-form-item label="标题位置(上)">
      <el-input v-model="attributeCopy.titleTop" />
    </el-form-item>
    <el-form-item label="说明">
      <el-input v-model="attributeCopy.seriesName" />
    </el-form-item>
    <el-form-item label="地图字颜色">
      <el-color-picker v-model="attributeCopy.geoLabelColor" show-alpha />
    </el-form-item>
    <el-form-item label="地图字大小">
      <el-input-number v-model="attributeCopy.geoLabelSize" :min="4" :max="100" />
    </el-form-item>
    <el-form-item label="允许缩放">
      <el-switch v-model="attributeCopy.roam" active-text="开" inactive-text="关" />
    </el-form-item>
    <el-form-item label="最小值标题">
      <el-input v-model="attributeCopy.piecesMinLabel"></el-input>
    </el-form-item>
    <el-form-item label="最小值">
      <el-input v-model="attributeCopy.piecesMin" type="number"></el-input>
    </el-form-item>
    <el-form-item label="最大值标题">
      <el-input v-model="attributeCopy.piecesMaxLabel"></el-input>
    </el-form-item>
    <el-form-item label="最大值">
      <el-input v-model="attributeCopy.piecesMax" type="number"></el-input>
    </el-form-item>
    <el-form-item label="程度最低色值">
      <el-input v-model="attributeCopy.piecesMinColor"></el-input>
    </el-form-item>
    <el-form-item label="程度中间色值">
      <el-input v-model="attributeCopy.piecesCenterColor"></el-input>
    </el-form-item>
    <el-form-item label="程度最高色值">
      <el-input v-model="attributeCopy.piecesMaxColor"></el-input>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-chart-map-gc-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = reactive(props.attribute);
</script>

<style scoped></style>
