# 3D地图散点图使用说明

## 概述

3D地图组件现已支持在地图表面上渲染散点，散点会随着3D地图的旋转、缩放而同步变化。适用于显示监测点、数据中心、传感器位置等地理位置相关的数据点。

## 功能特性

- ✅ 散点直接渲染在3D地图表面上
- ✅ 散点随地图视角变化而同步变化
- ✅ 支持多种散点符号（圆形、矩形、三角形、菱形、针形）
- ✅ 可自定义散点大小、颜色、透明度
- ✅ 支持散点标签显示
- ✅ 支持鼠标悬停高亮效果
- ✅ 支持动态数据更新
- ✅ 完全兼容现有地图配置

## 数据格式

### 地图数据格式
```json
{
  "mapData": [
    { "name": "区域名称", "value": 数值 }
  ],
  "scatterData": [
    { "name": "点名称", "lng": 经度, "lat": 纬度, "value": 数值 }
  ]
}
```

### 支持的散点数据格式

#### 格式1：经纬度分离
```json
{
  "name": "北京",
  "lng": 116.46,
  "lat": 39.92,
  "value": 100
}
```

#### 格式2：坐标数组
```json
{
  "name": "北京",
  "coord": [116.46, 39.92],
  "value": 100
}
```

#### 格式3：值数组
```json
{
  "name": "北京",
  "value": [116.46, 39.92, 100]
}
```

## 技术实现

### 坐标系统
- 使用2D geo坐标系叠加在3D地图上
- 散点会自动跟随3D地图的视角变化
- 保持散点与地图的完美同步

### 渲染层级
- 3D地图作为底层
- 2D geo坐标系作为透明中间层
- 散点作为顶层显示

## 配置选项

### 散点显示控制
- `showScatter`: 是否显示散点 (boolean)
- `scatterName`: 散点系列名称 (string)

### 散点样式
- `scatterSymbol`: 散点符号类型 ('circle' | 'rect' | 'triangle' | 'diamond' | 'pin')
- `scatterSize`: 散点大小 [宽度, 高度] (number[])
- `scatterColor`: 散点颜色 (string)
- `scatterOpacity`: 散点透明度 (0-1)
- `scatterEmphasisColor`: 散点高亮颜色 (string)

### 散点标签
- `showScatterLabel`: 是否显示散点标签 (boolean)
- `scatterLabelColor`: 散点标签颜色 (string)
- `scatterLabelSize`: 散点标签字体大小 (number)

## 使用示例

### 基础用法
```vue
<template>
  <cpt-chart-3dmap 
    :width="800" 
    :height="600" 
    :option="chartOption" 
  />
</template>

<script setup>
const chartOption = {
  cptDataForm: {
    dataSource: 1,
    dataText: JSON.stringify({
      mapData: [
        { name: '北京', value: 100 },
        { name: '上海', value: 200 }
      ],
      scatterData: [
        { name: '数据中心1', lng: 116.46, lat: 39.92, value: 150 },
        { name: '数据中心2', lng: 121.48, lat: 31.22, value: 180 }
      ]
    })
  },
  attribute: {
    // 地图基础配置
    map: 'areaChina',
    titleText: '全国数据分布',
    
    // 散点配置
    showScatter: true,
    scatterName: '数据中心',
    scatterSymbol: 'pin',
    scatterSize: [8, 25],
    scatterColor: '#FF6B6B',
    showScatterLabel: true
  }
}
</script>
```

### 动态更新散点数据
```javascript
// 更新散点数据
const updateScatterData = (newData) => {
  chartOption.value.cptDataForm.dataText = JSON.stringify({
    mapData: existingMapData,
    scatterData: newData
  });
};
```

## 注意事项

1. **坐标系统**: 散点数据使用WGS84坐标系统（经纬度）
2. **性能考虑**: 建议散点数量控制在1000个以内，过多会影响渲染性能
3. **视角同步**: 散点会自动与地图保持相同的视角控制
4. **数据验证**: 组件会自动过滤无效的坐标数据

## 常见问题

### Q: 散点位置不准确？
A: 请检查经纬度数据是否正确，确保使用的是WGS84坐标系统。

### Q: 散点不显示？
A: 检查以下配置：
- `showScatter` 是否为 true
- `scatterData` 数据格式是否正确
- 坐标是否在地图范围内

### Q: 如何自定义散点样式？
A: 可以通过 `scatterColor`、`scatterSize`、`scatterSymbol` 等属性自定义样式。

## 更新日志

- v1.0.0: 初始版本，支持基础散点渲染
- v1.1.0: 添加多种数据格式支持
- v1.2.0: 增加散点标签和高亮效果
