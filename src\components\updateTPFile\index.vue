<!-- 选择文件夹更新图片 -->
<template>
  <div class="updateExcel-main" v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      title="批量更新图片"
      v-model="updateTPFileDialogCopy"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="875px"
      :before-close="handleClose"
    >
      <div class="title-label">1、操作方式</div>
      <el-radio-group v-model="operaType">
        <el-tooltip class="item" effect="dark" content="针对更新普通属性组" placement="top">
          <el-radio :value="1">一对一</el-radio>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="针对更新特殊（公示照片，一张照片多个宗地更新）" placement="top">
          <el-radio :value="2">一对多</el-radio>
        </el-tooltip>
      </el-radio-group>
      <div class="title-label">2、选择文件夹</div>
      <el-upload
        ref="uploadFolderRef"
        :show-file-list="false"
        :headers="headers"
        :accept="accept"
        :auto-upload="false"
        name="files"
        :action="`${base}/qjt/file/multi/upload`"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        :on-error="uploadError"
        :on-exceed="exceed"
        multiple
        :on-change="handleChangeFCZD"
      >
        <el-button type="primary" size="small">点击选取文件夹</el-button>
      </el-upload>
      <div style="margin-top: 10px" v-show="folderName">已选文件夹：{{ folderName }}</div>
      <div class="title-label">3、选择节点</div>
      <el-cascader
        clearable
        style="width: 100%"
        v-model="nodeId"
        :options="ruleTree"
        placeholder="请选择节点"
        :props="{ checkStrictly: true, children: 'list', value: 'id', label: 'typeName' }"
        @change="handleChange"
      ></el-cascader>
      <div class="title-label">4、选择属性组及映射字段</div>
      <div class="flex-row">
        <el-select v-model="groupId" placeholder="请选择" @change="changeGroup" style="width: 100%">
          <el-option v-for="item in fieldGroupModelList" :key="item.id" :label="item.typeName" :value="item.id"> </el-option>
        </el-select>
        <el-select v-model="ysFieldName" placeholder="请选择映射字段" clearable filterable style="margin-left: 10px">
          <el-option v-for="item in localfields" :key="item.fieldName" :label="item.fieldCn" :disabled="item.disable" :value="item.fieldName">
          </el-option>
        </el-select>
      </div>
      <div class="title-label">5、关联字段</div>
      <div class="flex-row">
        <el-select v-model="ysChooseGroupId" placeholder="请选择关联属性组" style="width: 100%" @change="chooseYSGroup">
          <el-option v-for="item in fieldGroupModelList" :key="item.id" :label="item.typeName" :value="item.id"> </el-option>
        </el-select>
        <el-select v-model="ysChooseField" placeholder="请选择关联字段" clearable filterable style="margin-left: 10px">
          <el-option v-for="item in ysChooseFieldList" :key="item.fieldName" :label="item.fieldCn" :value="item.fieldName"> </el-option>
        </el-select>
      </div>
      <div class="title-label">6、更新方式</div>
      <el-radio-group v-model="type">
        <el-radio :value="item.value" v-for="(item, index) in typeList" :key="index" :disabled="item.disable">{{ item.label }}</el-radio>
      </el-radio-group>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">提 交</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="文件上传进度"
      v-model="uploadFileProDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="300px"
    >
      <div class="down-dialog">
        <el-progress :stroke-width="16" type="circle" :percentage="uploadFileProgress"></el-progress>
        <div style="margin-top: 10px">已上传：{{ nowCount }}/总条数：{{ uploadCount }}</div>
      </div>
    </el-dialog>
    <!-- 数据上传进度 -->
    <el-dialog
      title="数据导入中"
      v-model="uploadLodingDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="300px"
    >
      <div class="down-dialog">
        <el-progress :stroke-width="16" type="circle" :percentage="uploadProgress"></el-progress>
        <div style="margin-top: 10px">{{ uploadMsg }}</div>
      </div>
    </el-dialog>
    <!-- 上传前验证弹窗 -->
    <el-dialog
      title="上传前校验"
      :append-to-body="true"
      :modal-append-to-body="false"
      v-model="verificationDialog"
      :close-on-click-modal="false"
      @closed="closeVerification"
      width="650px"
    >
      <div class="dialog-row">校验进度：</div>
      <el-progress :text-inside="true" :stroke-width="26" :percentage="verificationPlan"></el-progress>
      <!--  -->
      <template v-if="verificationPlan >= 100">
        <div class="dialog-row" style="margin-top: 10px">总数据量：{{ uploadCount }} 条</div>
        <div class="dialog-row">校验成功：{{ verSuccNum }} 条</div>
        <div class="dialog-row">校验失败：{{ verErrorNum }} 条</div>
        <div class="dialog-row" v-show="uploadShpError.length != 0"><el-link type="primary" @click="downLoadErrorLog">下载错误日志</el-link></div>
        <div class="error-div" v-show="uploadShpError.length != 0">
          <div class="item" v-for="(item, index) in uploadShpError" :key="index">
            {{ item }}
          </div>
        </div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeVerification">取 消</el-button>
          <el-button type="primary" @click="nextSubmit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getToken } from '@/utils/auth';
import { selectRules } from '@/api/modal';
import { updateInstance, updateInstanceCheck } from '@/api/project';
import { ref, computed, reactive, watch, nextTick } from 'vue';
// ---Props---
interface Props {
  // 打开弹框
  updateTPFileDialog: boolean;
  // 模块id
  moduleId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  updateTPFileDialog: false,
  moduleId: ''
});

const updateTPFileDialogCopy = computed(() => props.updateTPFileDialog);

const headers = { Authorization: 'Bearer ' + getToken() }; //请求头
const accept = `.${['png', 'jpg', 'jpeg'].join(',.')}`; //允许上传的后缀名
const base = import.meta.env.VITE_APP_BASE_API;
const fileList = ref([]);
const sortUrl = '/project/fund/excel/upload';
const isUpload = ref(false); //是否已经上传文件
const fileMsg = ref('');
const fullscreenLoading = ref(false);
const ruleTree = ref([]); //树节点
const nodeId = ref(''); //选择的节点id
const chooseNode: any = ref({}); //当前选择的节点
const fieldGroupModelList = ref([]); //节点下的属性组
const chooseGroup: any = ref({}); //选择的属性组
const groupId = ref(''); //选择属性组的id
const operaType = ref(1);
const localfields = ref([]); //本地字段
const shpFields = ref([]); //表格表头
const ysChooseGroupId = ref(''); //映射属性组选择的id
const ysChooseGroup: any = ref({}); //映射的属性组
const ysChooseFieldList = ref([]); //映射选择的属性组对应的字段列表
const ysChooseField = ref(''); //映射的字段
const uploadLodingDialog = ref(false); //数据上传进度弹窗
const uploadProgress = ref(0); //上传进度
const uploadMsg = ref(''); //上传成功条数信息
const uploadShpError = ref([]); //上传失败的数据
const folderName = ref(''); //文件夹名称
const uploadFolderRef = ref(null); //上传组件引用
const ysFieldName = ref(''); //映射字段
const uploadList = ref([]); //上传的文件列表
const uploadCount = ref(0); //选择文件总数量
const uploadFileProDialog = ref(false); //文件上传进度弹窗
const uploadFileProgress = ref(0); //文件上传进度
const nowCount = ref(0); //当前上传成功的条数
const verificationDialog = ref(false); //上传前验证数据弹窗
const verSuccNum = ref(0); //验证成功条数
const verErrorNum = ref(0); //验证失败条数
const nextEndList = ref([]); //最终提交的数据列表
const verificationPlan = ref(0); //校验进度
const successNum = ref(0); //成功的条数
const type = ref(20); // 1 表示增量；3表示覆盖
//更新方式列表
const typeList = ref([
  { label: '增加', value: 21, disable: false },
  { label: '覆盖', value: 20, disable: false }
]);
const uploadShpNum = ref(0); //上传的条数

onMounted(() => {
  setTimeout(() => {
    // 在Element Plus中启用文件夹上传
    const uploadInput = document.querySelector('.el-upload__input');
    if (uploadInput) {
      (uploadInput as HTMLInputElement).webkitdirectory = true;
    }
  }, 200);
  getTree();
  // 初始化
  nextEndList.value = [];
  ruleTree.value = []; //树节点
  nodeId.value = ''; //选择的节点id
  chooseNode.value = {}; //当前选择的节点
  fieldGroupModelList.value = []; //节点下的属性组
  chooseGroup.value = {}; //选择的属性组
  groupId.value = ''; //选择属性组的id
  localfields.value = []; //本地字段
  shpFields.value = []; //表格表头
  uploadList.value = []; //文件数据
  nextEndList.value = [];

  nextTick(() => {
    uploadFolderRef.value?.clearFiles();
  });
  folderName.value = '';
});

// ---watch---
watch(
  updateTPFileDialogCopy,
  (newVal) => {
    if (newVal) {
    }
  },
  { deep: true }
);
// 定义emit
const emit = defineEmits<{
  (e: 'closeUpdateTP'): void;
}>();

// ---定义方法部分---
const handleClose = () => {
  ruleTree.value = []; //树节点
  nodeId.value = ''; //选择的节点id
  chooseNode.value = {}; //当前选择的节点
  fieldGroupModelList.value = []; //节点下的属性组
  chooseGroup.value = {}; //选择的属性组
  groupId.value = ''; //选择属性组的id
  localfields.value = []; //本地字段
  shpFields.value = []; //表格表头
  uploadList.value = []; //文件数据
  nextEndList.value = [];
  uploadFolderRef.value.clearFiles();
  folderName.value = '';
  emit('closeUpdateTP');
};
/**
 * 上传成功
 * @param response
 * @param file
 * @param fileList
 */
const uploadSuccess = (response, file, fileList) => {
  nowCount.value++;
  uploadFileProgress.value =
    +((nowCount.value / uploadCount.value) * 100).toFixed(2) > 100 ? 100 : +((nowCount.value / uploadCount.value) * 100).toFixed(2);
  let id = response.data[0].name.split('.')[0].trim();
  if (id.indexOf('(') != -1) {
    //代表有多个
    id = id.substring(0, id.indexOf('(')).trim();
  }
  const foundObject = uploadList.value.find((obj) => obj.id == id);
  if (foundObject) {
    //存在
    foundObject.list.push({
      url: response.data[0].path
    });
  } else {
    //不存在
    uploadList.value.push({
      id: id,
      list: [
        {
          url: response.data[0].path
        }
      ]
    });
  }
  if (nowCount.value == uploadCount.value) {
    //代表上传完毕，需要真正处理数据上传
    uploadFileProDialog.value = false;
    endSubmit();
  }
};
/**
 * 获取树结构
 */
const getTree = () => {
  selectRules({ moduleId: props.moduleId }).then((res) => {
    if (res.code == 200) {
      ruleTree.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const handleChange = (val) => {
  getChooseNode(ruleTree.value, val[val.length - 1]);
};
/**
 * 通过id迭代得到节点信息
 * @param list
 * @param id
 */
const getChooseNode = (list, id) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].id == id) {
      chooseNode.value = list[i];
      fieldGroupModelList.value = list[i].fieldGroupModelList;
      break;
    }
    if (list[i].list.length != 0) {
      getChooseNode(list[i].list, id);
    }
  }
};
/**
 * 改变属性组
 * @param val
 */
const changeGroup = (val) => {
  for (let i = 0; i < fieldGroupModelList.value.length; i++) {
    if (fieldGroupModelList.value[i].id == val) {
      chooseGroup.value = fieldGroupModelList.value[i];
      localfields.value = [];
      fieldGroupModelList.value[i].fieldModelList.forEach((v) => {
        if (v.valueMethod == 'idCardScan') {
          //身份证识别
          if (v.attribution.expendList) {
            //新版身份证识别 可以自己定义字段名
            v.attribution.expendList.forEach((k) => {
              const ite: any = {
                fieldCn: k.cnName,
                fieldName: k.enName,
                valueMethod: k.valueMethod
              };
              if (!['upload', 'idCardBitmap', 'xtqm', 'xtzw'].includes(k.valueMethod)) {
                //只允许选择上传图片的控件
                ite.disable = true;
              }
              localfields.value.push(ite);
            });
          }
        } else {
          if (!['upload', 'idCardBitmap', 'xtqm', 'xtzw'].includes(v.valueMethod)) {
            //只允许选择上传图片的控件
            v.disable = true;
          }
          localfields.value.push(v);
        }
      });
      getLocalFild();
      break;
    }
  }
};
/**
 * 获取本地字段
 */
const getLocalFild = async () => {
  localfields.value.forEach((f) => {
    for (let index = 0; index < shpFields.value.length; index++) {
      if (f.fieldName.toUpperCase() == shpFields.value[index]) {
        f.yz = shpFields.value[index];
        break;
      }
    }
  });
};
/**
 * 选择映射关联字段属性组
 * @param val
 */
const chooseYSGroup = (val) => {
  for (let i = 0; i < fieldGroupModelList.value.length; i++) {
    if (fieldGroupModelList.value[i].id == val) {
      ysChooseGroup.value = fieldGroupModelList.value[i];
      ysChooseFieldList.value = fieldGroupModelList.value[i].fieldModelList;
      break;
    }
  }
};

/**
 * 最终提交
 */
const endSubmit = async () => {
  const params = [];
  let num = 1; //排序用
  uploadList.value.forEach((v) => {
    const item = {
      appId: 0,
      attribution: {},
      groupId: chooseGroup.value.id,
      linkId: chooseGroup.value.linkId,
      ruleAttribution: chooseGroup.value.ruleAttribution,
      groupModel: {
        id: ysChooseGroup.value.id,
        linkId: ysChooseGroup.value.linkId,
        fieldModelList: [
          {
            fieldName: ysChooseField.value
          }
        ],
        ruleId: chooseNode.value.id
      },
      shpId: num
    };
    item.attribution[ysFieldName.value] = v.list;
    item.attribution[ysChooseField.value] = v.id;
    num++;
    params.push(item);
  });
  uploadShpNum.value = params.length;

  // 初始化最终上传列表
  nextEndList.value = [];
  uploadShpError.value = [];
  verErrorNum.value = 0;
  verSuccNum.value = 0;

  const chunksData: any = await handleChunkData(params, 10);
  // 这里需要上传前验证 为了实现整批数据要么一起上传要么都不上传
  verificationDialog.value = true;
  uploadFileProDialog.value = false;
  verificationPlan.value = 0;
  // 依次上传子数组校验
  for (let index = 0; index < chunksData.chunks.length; index++) {
    try {
      const resultNum = await verificationOnece(chunksData.chunks[index], index + 1, chunksData.count, chunksData.chunkSize);
      if (resultNum == 100) {
      }
    } catch (error) {
      ElMessage.error(error);
      // 处理错误，比如跳过当前子数组或中断整个上传过程
      continue;
    }
  }
};

/**
 * 单次校验一批数据
 * @param list
 * @param num
 * @param count
 * @param chunkSize
 */
const verificationOnece = async (list, num, count, chunkSize) => {
  return new Promise((resolve, reject) => {
    let moreFlag = 0;
    if (operaType.value == 2) {
      //一对多 同时更新多条
      moreFlag = 1;
    }
    updateInstanceCheck(list, moreFlag).then((res) => {
      if (res.code == 200) {
        setTimeout(() => {
          if (res.data.length == 0) {
            //代表该批次数据都可以成功导入
            verSuccNum.value = verSuccNum.value + list.length;
            nextEndList.value.push(...list);
          } else {
            //代表失败一部分或者全部失败
            verErrorNum.value = verErrorNum.value + res.data.length;
            if (res.data.length < list.length) {
              //这个时候需要把失败的批次里面成功的数据提取出来
              // shpId
              const errNums = [];
              res.data.forEach((v) => {
                errNums.push(parseInt(v.substring(1, v.indexOf('行'))));
              });
              list.forEach((v) => {
                if (!errNums.some((obj) => obj == v.shpId)) {
                  nextEndList.value.push(v);
                  verSuccNum.value++;
                }
              });
            }
            uploadShpError.value.push(...res.data);
          }
          verificationPlan.value = Number(
            +(((num * chunkSize) / count) * 100).toFixed(2) > 100 ? 100 : (((num * chunkSize) / count) * 100).toFixed(2)
          );
          resolve(verificationPlan.value);
        }, 500);
      } else {
        reject(res.msg);
      }
    });
  });
};
/**
 *  公共方法抽取数据 分段截取数据
 * @param list
 * @param num
 */
const handleChunkData = (list, num) => {
  const count = list.length;
  const chunkSize = num || 10;
  const chunks = [];
  // 拆分数组
  for (let i = 0; i < list.length; i += chunkSize) {
    // 这里创建二维数组内容，分 10 个 截成一个二维数组
    chunks.push(list.slice(i, i + chunkSize));
  }
  const resultItem = {
    count: count,
    chunkSize: chunkSize,
    chunks: chunks
  };
  return new Promise((resolve) => {
    resolve(resultItem);
  });
};

/**
 * 提交
 */
const submit = () => {
  if (!isUpload.value) {
    ElMessage.error('请选择文件！');
    return;
  }
  if (!nodeId.value) {
    ElMessage.error('请选择节点！');
    return;
  }
  if (!ysFieldName.value) {
    ElMessage.error('请选择映射字段');
    return;
  }
  if (!groupId.value) {
    ElMessage.error('请选择属性组！');
    return;
  }
  uploadFolderRef.value.submit();
  successNum.value = 0;
  uploadFileProDialog.value = true;
};
/**
 * excel导入分段提交
 * @param list
 * @param num
 */
const subsectionSubmit = (list, num) => {
  return new Promise((resolve, reject) => {
    let moreFlag = 0;
    if (operaType.value == 2) {
      //一对多 同时更新多条
      moreFlag = 1;
    }
    updateInstance(list, moreFlag, type.value.toString()).then((res) => {
      if (res.code == 200) {
        uploadProgress.value =
          +(((num * 10) / uploadShpNum.value) * 100).toFixed(2) > 100 ? 100 : +(((num * 10) / uploadShpNum.value) * 100).toFixed(2);
        uploadMsg.value = `已成功导入${num * 10 + list.length}条`;
        successNum.value = successNum.value + res.data.length;

        resolve(null);
      } else {
        uploadShpError.value.push(...list);
        reject(res.msg);
      }
    });
  });
};

/**
 * 上传文件夹前
 * @param file
 * @returns
 */
const beforeUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  return isJPG;
};

/**
 * 上传失败
 * @param err
 * @param file
 * @param fileList
 */
const uploadError = (err, file, fileList) => {
  ElMessage.error('上传失败');
};
/**
 * 上传文件数量太大，分散上传吧！
 * @param file
 * @param fileList
 */
const exceed = (file, fileList) => {
  ElMessage.error('上传文件数量太大，分散上传吧！');
};

/**
 *
 * @param file
 * @param fileList
 */
const handleChangeFCZD = (file, fileList) => {
  isUpload.value = true;
  // 文件总数量
  uploadCount.value = fileList.length;
  // 获取文件夹名称
  if (file.raw.webkitRelativePath) {
    folderName.value = file.raw.webkitRelativePath.split('/')[0];
  }
};
const closeVerification = () => {
  verificationDialog.value = false;
  emit('closeUpdateTP');
};
/**
 * 下载错误日志
 */
const downLoadErrorLog = () => {
  //下载txt文件
  const element = document.createElement('a');
  const endContent = uploadShpError.value.join('\n');
  element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(endContent));
  element.setAttribute('download', 'shp上传错误日志');
  element.style.display = 'none';
  element.click();
  document.body.removeChild(element); //下载完成移除元素
};
/**
 *
 */
const nextSubmit = async () => {
  if (nextEndList.value.length == 0) {
    ElMessage.error('校验成功的数据为0，请修改后重新提交！！！');
    return;
  }
  verificationDialog.value = false;
  const chunkSize = 10;
  const chunks = [];

  // 拆分数组
  for (let i = 0; i < nextEndList.value.length; i += chunkSize) {
    chunks.push(nextEndList.value.slice(i, i + chunkSize));
  }

  // this.batchDialog = false
  // 初始化上传进度弹窗
  uploadMsg.value = '已成功导入0条';
  uploadLodingDialog.value = true;
  uploadShpError.value = [];
  // 依次上传子数组
  for (let index = 0; index < chunks.length; index++) {
    try {
      await subsectionSubmit(chunks[index], index + 1);
    } catch (error) {
      console.error('上传失败:', error);
      // this.$message.error(error)
      // 处理错误，比如跳过当前子数组或中断整个上传过程
      continue;
    }
  }
  uploadLodingDialog.value = false;
  handleClose();

  const str = `成功${operaType.value == 1 ? '新增' : '更新'}${successNum.value}条数据`;
  operaType.value = null;
  ElMessageBox.alert(str, `${operaType.value == 1 ? '新增' : '更新'}成功`, {
    confirmButtonText: '确定',
    callback: (action) => {
      // 在这添加是否切换公司的标识。
      // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
      // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
      sessionStorage.setItem('qiehuan_company', 'false');
      location.reload();
    }
  });
};
</script>
<style lang="scss" scoped>
.dialog-row {
  margin-bottom: 10px;
}
.error-div {
  height: 400px;
  overflow: auto;
  .item {
    margin-bottom: 5px;
  }
}
.error-content {
  .flex-row {
    margin-bottom: 10px;
  }
  .error-div {
    height: 400px;
    overflow: auto;
    .item {
      margin-bottom: 5px;
    }
  }
}
.updateExcel-main {
}
.down-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.title-label {
  margin-bottom: 10px;
  margin-top: 10px;
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
