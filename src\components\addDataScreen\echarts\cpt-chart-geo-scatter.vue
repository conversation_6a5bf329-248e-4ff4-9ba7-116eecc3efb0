<!-- GEO SVG 散点图组件 -->
<template>
  <div style="width: 100%; height: 100%" :id="uuid"></div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
import { toRaw } from 'vue';
// 导入地图注册文件，确保地图数据已注册
import '@/utils/RegisterMap';

defineOptions({
  name: 'cpt-chart-geo-scatter'
});

const route = useRoute();
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();

// --- 定义变量 ---
const uuid = ref(uuidv1());
let chart: any = null;
const cptData = ref([]);

// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true } //深度监听
);

// 专门监听地图变化
watch(
  () => props.option.attribute.map,
  (newMap, oldMap) => {
    if (newMap !== oldMap && chart) {
      loadChart(props.option.attribute);
    }
  }
);

// 专门监听区域颜色变化
watch(
  () => props.option.attribute.regionColors,
  (newColors, oldColors) => {
    if (chart) {
      loadChart(props.option.attribute);
    }
  },
  { deep: true }
);

watch(
  () => props.width,
  () => {
    chart?.resize();
  }
);

watch(
  () => props.height,
  () => {
    chart?.resize();
  }
);

// 监听数据变化
watch(
  () => cptData.value,
  (newData) => {
    console.log('散点数据更新:', newData?.length, '条记录');
    if (chart && newData) {
      loadChart(props.option.attribute);
    }
  },
  { deep: true }
);

// 刷新数据
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};

// 手动刷新数据的方法
const manualRefresh = () => {
  console.log('手动刷新散点数据...');
  refreshCptData();
};

defineExpose({
  refreshCptData,
  manualRefresh
});

const loadData = (taskId?: string) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (taskId && taskId !== '') {
      parmas.taskId = taskId;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        if (res.data.scatter) {
          cptData.value = res.data.scatter;
        }
        loadChart(props.option.attribute);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;
      loadChart(props.option.attribute);
    });
  }
};

// 数据转换函数
const convertScatterData = (data: any[], attribute?: any) => {
  if (!data || !Array.isArray(data)) return [];

  return data
    .map((item) => {
      let coordinates: number[] = [];
      let value = 0;
      let name = '';

      // 支持多种数据格式
      if (item.lng && item.lat) {
        // 格式1: {name: '北京', lng: 116.46, lat: 39.92, value: 100}
        coordinates = [item.lng, item.lat];
        value = item.value || 0;
        name = item.name || '';
      } else if (item.coord && Array.isArray(item.coord)) {
        // 格式2: {name: '北京', coord: [116.46, 39.92], value: 100}
        coordinates = item.coord;
        value = item.value || 0;
        name = item.name || '';
      } else if (Array.isArray(item.value) && item.value.length >= 2) {
        // 格式3: {name: '北京', value: [116.46, 39.92, 100]}
        coordinates = [item.value[0], item.value[1]];
        value = item.value[2] || 0;
        name = item.name || '';
      } else if (item.address || item.poi || item.detail) {
        // 格式4: 地址数据格式 - 需要地理编码
        name = item.poi || item.address || item.detail || '';
        value = parseFloat(item.poi_distance) || parseFloat(item.address_distance) || 0;

        // 尝试从已知的城市/区县获取大概坐标
        coordinates = getCoordinatesFromLocation(item);

        if (coordinates.length === 0) {
          console.warn(`无法获取坐标信息: ${name}`, item);
          return null;
        }
      } else {
        return null;
      }

      const result: any = {
        name: name,
        value: [...coordinates, value], // [经度, 纬度, 数值]
        itemStyle: item.itemStyle || {},
        label: item.label || {},
        // 保存原始数据用于tooltip显示
        rawData: item
      };

      // 如果启用了状态颜色，添加状态信息
      if (attribute?.enableStatusColors && attribute.statusField) {
        result.status = item[attribute.statusField];
      }

      return result;
    })
    .filter(Boolean);
};

// 获取最优缩放级别
const getOptimalZoom = (mapName: string, defaultZoom: number) => {
  const smallMaps = ['bijie', 'changZhou', 'sihui']; // 小范围地图列表
  if (smallMaps.includes(mapName)) {
    return Math.max(defaultZoom, 1); // 小地图最小缩放为2
  }
  return defaultZoom;
};

// 获取最优中心点
const getOptimalCenter = (mapName: string, defaultCenter: number[]) => {
  const mapCenters: Record<string, number[]> = {
    bijie: [105.27, 26.89],
    changZhou: [119.95, 31.78],
    sihui: [112.7, 23.3],
    guizhou: [106.7, 26.6],
    china: [104, 35]
  };

  return mapCenters[mapName] || defaultCenter || [104, 35];
};

// 根据地址信息获取坐标
const getCoordinatesFromLocation = (item: any): number[] => {
  // 毕节市各区县的大概坐标
  const bijieCoordinates: Record<string, number[]> = {
    '七星关区': [105.284852, 27.302085],
    '大方县': [105.609254, 27.143521],
    '黔西市': [106.038299, 27.024923],
    '金沙县': [106.222103, 27.459693],
    '织金县': [105.768896, 26.668019],
    '纳雍县': [105.378853, 26.774193],
    '威宁彝族回族苗族自治县': [104.286523, 26.859636],
    '赫章县': [104.726451, 27.119806]
  };

  // 贵州省主要城市坐标
  const guizhouCoordinates: Record<string, number[]> = {
    '贵阳市': [106.713478, 26.578343],
    '遵义市': [107.297104, 27.725654],
    '安顺市': [105.932188, 26.245544],
    '毕节市': [105.28501, 27.301693],
    '铜仁市': [109.191555, 27.718346],
    '黔西南布依族苗族自治州': [104.906301, 25.08812],
    '六盘水市': [104.830359, 26.584643],
    '黔南布依族苗族自治州': [107.517156, 26.258219],
    '黔东南苗族侗族自治州': [107.977488, 26.583352]
  };

  // 优先匹配区县
  if (item.county && bijieCoordinates[item.county]) {
    const baseCoord = bijieCoordinates[item.county];
    // 添加一些随机偏移，避免点重叠
    const offset = 0.01; // 约1公里的偏移
    return [
      baseCoord[0] + (Math.random() - 0.5) * offset,
      baseCoord[1] + (Math.random() - 0.5) * offset
    ];
  }

  // 匹配城市
  if (item.city && guizhouCoordinates[item.city]) {
    const baseCoord = guizhouCoordinates[item.city];
    const offset = 0.02; // 约2公里的偏移
    return [
      baseCoord[0] + (Math.random() - 0.5) * offset,
      baseCoord[1] + (Math.random() - 0.5) * offset
    ];
  }

  // 默认返回毕节市中心坐标
  const offset = 0.05; // 约5公里的偏移
  return [
    105.28501 + (Math.random() - 0.5) * offset,
    27.301693 + (Math.random() - 0.5) * offset
  ];
};

// 处理自定义符号
const getCustomSymbol = (attribute: any) => {
  if (attribute.scatterSymbol === 'image' && attribute.customImageUrl) {
    return {
      symbol: `image://${attribute.customImageUrl}`,
      symbolSize: attribute.customImageSize || 30
    };
  }

  if (attribute.scatterSymbol === 'path' && attribute.customPath) {
    return {
      symbol: `path://${attribute.customPath}`,
      symbolSize: attribute.customPathSize || 20,
      itemStyle: {
        color: attribute.customPathColor || '#ff6b6b'
      }
    };
  }

  // 内置符号映射
  const symbolMap: Record<string, string> = {
    star: 'path://M28.218,23.944l-5.864,5.712l1.384,8.072c0.081,0.474-0.407,0.834-0.834,0.615L16,34.672l-6.904,3.671c-0.427,0.219-0.915-0.141-0.834-0.615l1.384-8.072l-5.864-5.712c-0.343-0.334-0.158-0.915,0.316-0.99l8.112-1.179L15.684,14.1c0.214-0.434,0.832-0.434,1.046,0l3.63,7.355l8.112,1.179C28.917,22.709,29.102,23.29,28.218,23.944z',
    heart:
      'path://M23.6,2c-3.363,0-6.258,2.736-7.599,5.594C14.660,4.736,11.765,2,8.402,2C4.014,2,0.5,5.514,0.5,9.902c0,4.388,3.514,7.902,7.902,7.902c0.027,0,0.054,0,0.081,0C9.933,20.645,14.105,25.508,16,30c1.895-4.492,6.067-9.355,7.517-12.196c0.027,0,0.054,0,0.081,0c4.388,0,7.902-3.514,7.902-7.902C31.5,5.514,27.986,2,23.6,2z',
    droplet: 'path://M16,2C16,2,4,14,4,22c0,6.627,5.373,12,12,12s12-5.373,12-12C28,14,16,2,16,2z'
  };

  if (symbolMap[attribute.scatterSymbol]) {
    return {
      symbol: symbolMap[attribute.scatterSymbol],
      symbolSize: attribute.scatterBaseSize || 20
    };
  }

  return {
    symbol: attribute.scatterSymbol,
    symbolSize: (val: number[]) => {
      const baseSize = attribute.scatterBaseSize || 10;
      const sizeMultiplier = attribute.scatterSizeMultiplier || 1;
      return Math.max(baseSize, val[2] * sizeMultiplier);
    }
  };
};

// 获取状态符号配置
const getStatusSymbol = (attribute: any, isCompleted: boolean) => {
  if (!attribute.enableStatusSymbols) {
    return getCustomSymbol(attribute);
  }

  const symbolType = isCompleted ? attribute.completedSymbol : attribute.uncompletedSymbol;

  if (symbolType === 'image') {
    const imageUrl = isCompleted ? attribute.completedCustomImageUrl : attribute.uncompletedCustomImageUrl;
    const imageSize = isCompleted ? attribute.completedCustomImageSize : attribute.uncompletedCustomImageSize;

    if (imageUrl) {
      return {
        symbol: `image://${imageUrl}`,
        symbolSize: imageSize || 30
      };
    }
  }

  if (symbolType === 'path') {
    const path = isCompleted ? attribute.completedCustomPath : attribute.uncompletedCustomPath;
    const pathSize = isCompleted ? attribute.completedCustomPathSize : attribute.uncompletedCustomPathSize;
    const pathColor = isCompleted ? attribute.completedCustomPathColor : attribute.uncompletedCustomPathColor;

    if (path) {
      return {
        symbol: `path://${path}`,
        symbolSize: pathSize || 20,
        itemStyle: {
          color: pathColor
        }
      };
    }
  }

  // 内置符号映射
  const symbolMap: Record<string, string> = {
    star: 'path://M28.218,23.944l-5.864,5.712l1.384,8.072c0.081,0.474-0.407,0.834-0.834,0.615L16,34.672l-6.904,3.671c-0.427,0.219-0.915-0.141-0.834-0.615l1.384-8.072l-5.864-5.712c-0.343-0.334-0.158-0.915,0.316-0.99l8.112-1.179L15.684,14.1c0.214-0.434,0.832-0.434,1.046,0l3.63,7.355l8.112,1.179C28.917,22.709,29.102,23.29,28.218,23.944z',
    heart:
      'path://M23.6,2c-3.363,0-6.258,2.736-7.599,5.594C14.660,4.736,11.765,2,8.402,2C4.014,2,0.5,5.514,0.5,9.902c0,4.388,3.514,7.902,7.902,7.902c0.027,0,0.054,0,0.081,0C9.933,20.645,14.105,25.508,16,30c1.895-4.492,6.067-9.355,7.517-12.196c0.027,0,0.054,0,0.081,0c4.388,0,7.902-3.514,7.902-7.902C31.5,5.514,27.986,2,23.6,2z',
    droplet: 'path://M16,2C16,2,4,14,4,22c0,6.627,5.373,12,12,12s12-5.373,12-12C28,14,16,2,16,2z'
  };

  if (symbolMap[symbolType]) {
    return {
      symbol: symbolMap[symbolType],
      symbolSize: attribute.scatterBaseSize || 20
    };
  }

  return {
    symbol: symbolType,
    symbolSize: (val: number[]) => {
      const baseSize = attribute.scatterBaseSize || 10;
      const sizeMultiplier = attribute.scatterSizeMultiplier || 1;
      return Math.max(baseSize, val[2] * sizeMultiplier);
    }
  };
};

// 处理双色状态数据
const processStatusData = (data: any[], attribute: any) => {
  if (!attribute.enableStatusColors) {
    // 未启用状态颜色，返回单一系列
    const customSymbol = getCustomSymbol(attribute);

    return [
      {
        name: attribute.seriesName,
        type: attribute.scatterType,
        coordinateSystem: 'geo',
        symbol: customSymbol.symbol,
        symbolSize: customSymbol.symbolSize,
        itemStyle: {
          color: attribute.scatterColor,
          opacity: attribute.scatterOpacity,
          borderWidth: attribute.scatterBorderWidth,
          borderColor: attribute.scatterBorderColor,
          ...customSymbol.itemStyle
        },
        label: {
          show: attribute.showScatterLabel,
          position: attribute.scatterLabelPosition,
          color: attribute.scatterLabelColor,
          fontSize: attribute.scatterLabelSize,
          formatter: (params: any) => {
            if (attribute.scatterLabelFormat === 'name') return params.name;
            if (attribute.scatterLabelFormat === 'value') return params.value[2];
            return `${params.name}: ${params.value[2]}`;
          }
        },
        emphasis: {
          itemStyle: {
            color: attribute.scatterEmphasisColor,
            shadowBlur: attribute.scatterEmphasisShadowBlur,
            shadowColor: attribute.scatterEmphasisShadowColor
          }
        },
        // 涟漪效果配置（仅当 scatterType 为 'effectScatter' 时生效）
        ...(attribute.scatterType === 'effectScatter' && {
          rippleEffect: {
            brushType: attribute.rippleBrushType,
            scale: attribute.rippleScale,
            period: attribute.ripplePeriod
          }
        }),
        data: data
      }
    ];
  }

  // 启用状态颜色，按状态分组
  const completedData = data.filter((item) => item.status === true || item.status === 'completed' || item.status === '已完成');
  const uncompletedData = data.filter((item) => item.status === false || item.status === 'uncompleted' || item.status === '未完成');

  const series = [];

  // 已完成系列
  if (completedData.length > 0) {
    const completedSymbol = getStatusSymbol(attribute, true);

    series.push({
      name: attribute.completedLabel,
      type: attribute.scatterType,
      coordinateSystem: 'geo',
      symbol: completedSymbol.symbol,
      symbolSize: completedSymbol.symbolSize,
      itemStyle: {
        color: attribute.completedColor,
        opacity: attribute.scatterOpacity,
        borderWidth: attribute.scatterBorderWidth,
        borderColor: attribute.scatterBorderColor,
        ...completedSymbol.itemStyle
      },
      label: {
        show: attribute.showScatterLabel,
        position: attribute.scatterLabelPosition,
        color: attribute.scatterLabelColor,
        fontSize: attribute.scatterLabelSize,
        formatter: (params: any) => {
          if (attribute.scatterLabelFormat === 'name') return params.name;
          if (attribute.scatterLabelFormat === 'value') return params.value[2];
          return `${params.name}: ${params.value[2]}`;
        }
      },
      emphasis: {
        itemStyle: {
          color: attribute.scatterEmphasisColor,
          shadowBlur: attribute.scatterEmphasisShadowBlur,
          shadowColor: attribute.scatterEmphasisShadowColor
        }
      },
      // 涟漪效果配置（仅当 scatterType 为 'effectScatter' 时生效）
      ...(attribute.scatterType === 'effectScatter' && {
        rippleEffect: {
          brushType: attribute.rippleBrushType,
          scale: attribute.rippleScale,
          period: attribute.ripplePeriod
        }
      }),
      data: completedData
    });
  }

  // 未完成系列
  if (uncompletedData.length > 0) {
    const uncompletedSymbol = getStatusSymbol(attribute, false);

    series.push({
      name: attribute.uncompletedLabel,
      type: attribute.scatterType,
      coordinateSystem: 'geo',
      symbol: uncompletedSymbol.symbol,
      symbolSize: uncompletedSymbol.symbolSize,
      itemStyle: {
        color: attribute.uncompletedColor,
        opacity: attribute.scatterOpacity,
        borderWidth: attribute.scatterBorderWidth,
        borderColor: attribute.scatterBorderColor,
        ...uncompletedSymbol.itemStyle
      },
      label: {
        show: attribute.showScatterLabel,
        position: attribute.scatterLabelPosition,
        color: attribute.scatterLabelColor,
        fontSize: attribute.scatterLabelSize,
        formatter: (params: any) => {
          if (attribute.scatterLabelFormat === 'name') return params.name;
          if (attribute.scatterLabelFormat === 'value') return params.value[2];
          return `${params.name}: ${params.value[2]}`;
        }
      },
      emphasis: {
        itemStyle: {
          color: attribute.scatterEmphasisColor,
          shadowBlur: attribute.scatterEmphasisShadowBlur,
          shadowColor: attribute.scatterEmphasisShadowColor
        }
      },
      // 涟漪效果配置（仅当 scatterType 为 'effectScatter' 时生效）
      ...(attribute.scatterType === 'effectScatter' && {
        rippleEffect: {
          brushType: attribute.rippleBrushType,
          scale: attribute.rippleScale,
          period: attribute.ripplePeriod
        }
      }),
      data: uncompletedData
    });
  }

  return series;
};

// 处理地图区域数据和颜色配置
const processMapRegionData = (mapData: any, attribute: any) => {
  const features = mapData.geoJson?.features || [];
  // 确保正确获取区域颜色配置
  let regionColors = attribute.regionColors;

  // 尝试多种方式获取区域颜色配置
  if (!regionColors || Object.keys(regionColors).length === 0) {
    regionColors = toRaw(attribute.regionColors) || {};
  }

  // 如果还是空的，尝试直接访问
  if (!regionColors || Object.keys(regionColors).length === 0) {
    regionColors = attribute['regionColors'] || {};
  }

  // 只返回有自定义颜色的区域
  return features
    .map((feature: any) => {
      const regionName = feature.properties?.name || '';
      const customColor = regionColors[regionName];

      if (customColor) {
        return {
          name: regionName,
          itemStyle: {
            areaColor: customColor,
            borderColor: attribute.mapBorderColor || '#999',
            borderWidth: attribute.mapBorderWidth || 1
          }
        };
      }
      return null;
    })
    .filter(Boolean);
};

const loadChart = (attribute: any) => {
  const processedData = convertScatterData(cptData.value, attribute);
  // 检查地图是否已注册
  const mapData = echarts.getMap(attribute.map);
  if (!mapData) {
    console.warn(`地图 ${attribute.map} 未注册，请检查 RegisterMap.ts`);
    return;
  }
  // 处理地图区域数据和颜色
  const mapRegionData = processMapRegionData(mapData, attribute);

  // 处理散点系列数据
  const scatterSeries = processStatusData(processedData, attribute);

  const chartOption = {
    title: {
      text: attribute.titleText,
      subtext: attribute.subtext,
      left: attribute.titleLeft,
      top: attribute.titleTop,
      textStyle: {
        color: attribute.titleColor,
        fontSize: attribute.titleFontSize
      },
      subtextStyle: {
        color: attribute.subTitleColor,
        fontSize: attribute.subTitleFontSize
      }
    },

    // 图例配置
    ...(attribute.enableStatusColors &&
      attribute.showLegend && {
        legend: {
          data: scatterSeries.map((series: any) => series.name),
          left: 'right',
          top: 'top',
          orient: 'vertical',
          textStyle: {
            color: attribute.legendTextColor,
            fontSize: attribute.legendTextSize
          }
        }
      }),

    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        if (params.seriesType === 'scatter' || params.seriesType === 'effectScatter') {
          const data = params.data;
          const rawData = data.rawData;

          if (rawData && rawData.detail) {
            // 地址数据格式的tooltip
            return `
              <div style="padding: 8px; max-width: 300px;">
                <div style="font-weight: bold; margin-bottom: 8px; color: #333;">${data.name}</div>
                <div style="margin-bottom: 4px;"><strong>详细地址:</strong> ${rawData.detail}</div>
                <div style="margin-bottom: 4px;"><strong>所属区县:</strong> ${rawData.county || '未知'}</div>
                <div style="margin-bottom: 4px;"><strong>所属乡镇:</strong> ${rawData.town || '未知'}</div>
                <div style="margin-bottom: 4px;"><strong>距离:</strong> ${rawData.poi_distance || rawData.address_distance}米</div>
                <div style="margin-bottom: 4px;"><strong>坐标:</strong> ${data.value[0].toFixed(4)}, ${data.value[1].toFixed(4)}</div>
                ${params.seriesName ? `<div style="margin-bottom: 4px;"><strong>状态:</strong> ${params.seriesName}</div>` : ''}
              </div>
            `;
          } else {
            // 普通数据格式的tooltip
            return `
              <div style="padding: 8px;">
                <div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>
                <div>经度: ${data.value[0].toFixed(4)}</div>
                <div>纬度: ${data.value[1].toFixed(4)}</div>
                <div>数值: ${data.value[2]}</div>
                ${params.seriesName ? `<div>状态: ${params.seriesName}</div>` : ''}
              </div>
            `;
          }
        }
        return `${params.name}<br/>状态: ${params.seriesName}`;
      }
    },
    geo: {
      map: attribute.map,
      roam: attribute.roam, // 允许缩放和平移
      zoom: getOptimalZoom(attribute.map, attribute.zoom),
      center: getOptimalCenter(attribute.map, attribute.center),
      label: {
        show: attribute.showMapLabel,
        fontSize: attribute.mapLabelSize,
        color: attribute.mapLabelColor
      },
      itemStyle: {
        areaColor: attribute.mapAreaColor,
        borderColor: attribute.mapBorderColor,
        borderWidth: attribute.mapBorderWidth
      },
      emphasis: {
        label: {
          show: attribute.showMapEmphasisLabel,
          color: attribute.mapEmphasisLabelColor
        },
        itemStyle: {
          areaColor: attribute.mapEmphasisAreaColor,
          borderColor: attribute.mapEmphasisBorderColor
        }
      },
      // 支持单独的区域颜色配置
      ...(mapRegionData.length > 0 && { regions: mapRegionData })
    },
    visualMap: attribute.showVisualMap
      ? {
          min: attribute.visualMapMin,
          max: attribute.visualMapMax,
          left: attribute.visualMapLeft,
          top: attribute.visualMapTop,
          text: [attribute.visualMapMaxText, attribute.visualMapMinText],
          textStyle: {
            color: attribute.visualMapTextColor
          },
          inRange: {
            color: [attribute.visualMapMinColor, attribute.visualMapMaxColor]
          },
          calculable: true
        }
      : null,
    series: scatterSeries
  };

  // 强制重新渲染，特别是在地图切换时
  chart?.clear();
  chart?.setOption(chartOption);
};

// 初始化
onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));
  refreshCptData();
});
</script>

<style scoped></style>
