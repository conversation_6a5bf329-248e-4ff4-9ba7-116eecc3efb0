<!-- GEO SVG 散点图组件 -->
<template>
  <div style="width: 100%; height: 100%" :id="uuid"></div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
import { toRaw } from 'vue';
// 导入地图注册文件，确保地图数据已注册
import '@/utils/RegisterMap';

defineOptions({
  name: 'cpt-chart-geo-scatter'
});

const route = useRoute();
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();

// --- 定义变量 ---
const uuid = ref(uuidv1());
let chart: any = null;
const cptData = ref([]);

// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true } //深度监听
);

// 专门监听地图变化
watch(
  () => props.option.attribute.map,
  (newMap, oldMap) => {
    if (newMap !== oldMap && chart) {
      loadChart(props.option.attribute);
    }
  }
);

// 专门监听区域颜色变化
watch(
  () => props.option.attribute.regionColors,
  (newColors, oldColors) => {
    if (chart) {
      loadChart(props.option.attribute);
    }
  },
  { deep: true }
);

watch(
  () => props.width,
  () => {
    chart?.resize();
  }
);

watch(
  () => props.height,
  () => {
    chart?.resize();
  }
);

// 刷新数据
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};

defineExpose({
  refreshCptData
});

const loadData = (taskId?: string) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (taskId && taskId !== '') {
      parmas.taskId = taskId;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        if (res.data.scatter) {
          cptData.value = res.data.scatter;
        }
        loadChart(props.option.attribute);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;
      loadChart(props.option.attribute);
    });
  }
};

// 数据转换函数
const convertScatterData = (data: any[], attribute?: any) => {
  if (!data || !Array.isArray(data)) return [];

  return data
    .map((item) => {
      let coordinates: number[] = [];
      let value = 0;

      // 支持多种数据格式
      if (item.lng && item.lat) {
        // 格式1: {name: '北京', lng: 116.46, lat: 39.92, value: 100}
        coordinates = [item.lng, item.lat];
        value = item.value || 0;
      } else if (item.coord && Array.isArray(item.coord)) {
        // 格式2: {name: '北京', coord: [116.46, 39.92], value: 100}
        coordinates = item.coord;
        value = item.value || 0;
      } else if (Array.isArray(item.value) && item.value.length >= 2) {
        // 格式3: {name: '北京', value: [116.46, 39.92, 100]}
        coordinates = [item.value[0], item.value[1]];
        value = item.value[2] || 0;
      } else {
        return null;
      }

      const result: any = {
        name: item.name || '',
        value: [...coordinates, value], // [经度, 纬度, 数值]
        itemStyle: item.itemStyle || {},
        label: item.label || {}
      };

      // 如果启用了状态颜色，添加状态信息
      if (attribute?.enableStatusColors && attribute.statusField) {
        result.status = item[attribute.statusField];
      }

      return result;
    })
    .filter(Boolean);
};

// 获取最优缩放级别
const getOptimalZoom = (mapName: string, defaultZoom: number) => {
  const smallMaps = ['bijie', 'changZhou', 'sihui']; // 小范围地图列表
  if (smallMaps.includes(mapName)) {
    return Math.max(defaultZoom, 1); // 小地图最小缩放为2
  }
  return defaultZoom;
};

// 获取最优中心点
const getOptimalCenter = (mapName: string, defaultCenter: number[]) => {
  const mapCenters: Record<string, number[]> = {
    bijie: [105.27, 26.89],
    changZhou: [119.95, 31.78],
    sihui: [112.7, 23.3],
    guizhou: [106.7, 26.6],
    china: [104, 35]
  };

  return mapCenters[mapName] || defaultCenter || [104, 35];
};

// 处理双色状态数据
const processStatusData = (data: any[], attribute: any) => {
  if (!attribute.enableStatusColors) {
    // 未启用状态颜色，返回单一系列
    return [{
      name: attribute.seriesName,
      type: attribute.scatterType,
      coordinateSystem: 'geo',
      symbol: attribute.scatterSymbol,
      symbolSize: (value: number[]) => {
        return attribute.scatterBaseSize + (value[2] || 0) * attribute.scatterSizeMultiplier;
      },
      itemStyle: {
        color: attribute.scatterColor,
        opacity: attribute.scatterOpacity,
        borderWidth: attribute.scatterBorderWidth,
        borderColor: attribute.scatterBorderColor
      },
      label: {
        show: attribute.showScatterLabel,
        position: attribute.scatterLabelPosition,
        color: attribute.scatterLabelColor,
        fontSize: attribute.scatterLabelSize,
        formatter: (params: any) => {
          if (attribute.scatterLabelFormat === 'name') return params.name;
          if (attribute.scatterLabelFormat === 'value') return params.value[2];
          return `${params.name}: ${params.value[2]}`;
        }
      },
      emphasis: {
        itemStyle: {
          color: attribute.scatterEmphasisColor,
          shadowBlur: attribute.scatterEmphasisShadowBlur,
          shadowColor: attribute.scatterEmphasisShadowColor
        }
      },
      // 涟漪效果配置（仅当 scatterType 为 'effectScatter' 时生效）
      ...(attribute.scatterType === 'effectScatter' && {
        rippleEffect: {
          brushType: attribute.rippleBrushType,
          scale: attribute.rippleScale,
          period: attribute.ripplePeriod
        }
      }),
      data: data
    }];
  }

  // 启用状态颜色，按状态分组
  const completedData = data.filter(item => item.status === true || item.status === 'completed' || item.status === '已完成');
  const uncompletedData = data.filter(item => item.status === false || item.status === 'uncompleted' || item.status === '未完成');

  const series = [];

  // 已完成系列
  if (completedData.length > 0) {
    series.push({
      name: attribute.completedLabel,
      type: attribute.scatterType,
      coordinateSystem: 'geo',
      symbol: attribute.scatterSymbol,
      symbolSize: (value: number[]) => {
        return attribute.scatterBaseSize + (value[2] || 0) * attribute.scatterSizeMultiplier;
      },
      itemStyle: {
        color: attribute.completedColor,
        opacity: attribute.scatterOpacity,
        borderWidth: attribute.scatterBorderWidth,
        borderColor: attribute.scatterBorderColor
      },
      label: {
        show: attribute.showScatterLabel,
        position: attribute.scatterLabelPosition,
        color: attribute.scatterLabelColor,
        fontSize: attribute.scatterLabelSize,
        formatter: (params: any) => {
          if (attribute.scatterLabelFormat === 'name') return params.name;
          if (attribute.scatterLabelFormat === 'value') return params.value[2];
          return `${params.name}: ${params.value[2]}`;
        }
      },
      emphasis: {
        itemStyle: {
          color: attribute.scatterEmphasisColor,
          shadowBlur: attribute.scatterEmphasisShadowBlur,
          shadowColor: attribute.scatterEmphasisShadowColor
        }
      },
      // 涟漪效果配置（仅当 scatterType 为 'effectScatter' 时生效）
      ...(attribute.scatterType === 'effectScatter' && {
        rippleEffect: {
          brushType: attribute.rippleBrushType,
          scale: attribute.rippleScale,
          period: attribute.ripplePeriod
        }
      }),
      data: completedData
    });
  }

  // 未完成系列
  if (uncompletedData.length > 0) {
    series.push({
      name: attribute.uncompletedLabel,
      type: attribute.scatterType,
      coordinateSystem: 'geo',
      symbol: attribute.scatterSymbol,
      symbolSize: (value: number[]) => {
        return attribute.scatterBaseSize + (value[2] || 0) * attribute.scatterSizeMultiplier;
      },
      itemStyle: {
        color: attribute.uncompletedColor,
        opacity: attribute.scatterOpacity,
        borderWidth: attribute.scatterBorderWidth,
        borderColor: attribute.scatterBorderColor
      },
      label: {
        show: attribute.showScatterLabel,
        position: attribute.scatterLabelPosition,
        color: attribute.scatterLabelColor,
        fontSize: attribute.scatterLabelSize,
        formatter: (params: any) => {
          if (attribute.scatterLabelFormat === 'name') return params.name;
          if (attribute.scatterLabelFormat === 'value') return params.value[2];
          return `${params.name}: ${params.value[2]}`;
        }
      },
      emphasis: {
        itemStyle: {
          color: attribute.scatterEmphasisColor,
          shadowBlur: attribute.scatterEmphasisShadowBlur,
          shadowColor: attribute.scatterEmphasisShadowColor
        }
      },
      // 涟漪效果配置（仅当 scatterType 为 'effectScatter' 时生效）
      ...(attribute.scatterType === 'effectScatter' && {
        rippleEffect: {
          brushType: attribute.rippleBrushType,
          scale: attribute.rippleScale,
          period: attribute.ripplePeriod
        }
      }),
      data: uncompletedData
    });
  }

  return series;
};

// 处理地图区域数据和颜色配置
const processMapRegionData = (mapData: any, attribute: any) => {
  const features = mapData.geoJson?.features || [];
  // 确保正确获取区域颜色配置
  let regionColors = attribute.regionColors;

  // 尝试多种方式获取区域颜色配置
  if (!regionColors || Object.keys(regionColors).length === 0) {
    regionColors = toRaw(attribute.regionColors) || {};
  }

  // 如果还是空的，尝试直接访问
  if (!regionColors || Object.keys(regionColors).length === 0) {
    regionColors = attribute['regionColors'] || {};
  }

  // 只返回有自定义颜色的区域
  return features
    .map((feature: any) => {
      const regionName = feature.properties?.name || '';
      const customColor = regionColors[regionName];

      if (customColor) {
        return {
          name: regionName,
          itemStyle: {
            areaColor: customColor,
            borderColor: attribute.mapBorderColor || '#999',
            borderWidth: attribute.mapBorderWidth || 1
          }
        };
      }
      return null;
    })
    .filter(Boolean);
};

const loadChart = (attribute: any) => {
  const processedData = convertScatterData(cptData.value, attribute);
  // 检查地图是否已注册
  const mapData = echarts.getMap(attribute.map);
  if (!mapData) {
    console.warn(`地图 ${attribute.map} 未注册，请检查 RegisterMap.ts`);
    return;
  }
  // 处理地图区域数据和颜色
  const mapRegionData = processMapRegionData(mapData, attribute);

  // 处理散点系列数据
  const scatterSeries = processStatusData(processedData, attribute);

  const chartOption = {
    title: {
      text: attribute.titleText,
      subtext: attribute.subtext,
      left: attribute.titleLeft,
      top: attribute.titleTop,
      textStyle: {
        color: attribute.titleColor,
        fontSize: attribute.titleFontSize
      },
      subtextStyle: {
        color: attribute.subTitleColor,
        fontSize: attribute.subTitleFontSize
      }
    },

    // 图例配置
    ...(attribute.enableStatusColors && attribute.showLegend && {
      legend: {
        data: scatterSeries.map((series: any) => series.name),
        left: 'right',
        top: 'top',
        orient: 'vertical'
      }
    }),

    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.name}<br/>经度: ${params.value[0]}<br/>纬度: ${params.value[1]}<br/>数值: ${params.value[2]}`;
      }
    },
    geo: {
      map: attribute.map,
      roam: attribute.roam, // 允许缩放和平移
      zoom: getOptimalZoom(attribute.map, attribute.zoom),
      center: getOptimalCenter(attribute.map, attribute.center),
      label: {
        show: attribute.showMapLabel,
        fontSize: attribute.mapLabelSize,
        color: attribute.mapLabelColor
      },
      itemStyle: {
        areaColor: attribute.mapAreaColor,
        borderColor: attribute.mapBorderColor,
        borderWidth: attribute.mapBorderWidth
      },
      emphasis: {
        label: {
          show: attribute.showMapEmphasisLabel,
          color: attribute.mapEmphasisLabelColor
        },
        itemStyle: {
          areaColor: attribute.mapEmphasisAreaColor,
          borderColor: attribute.mapEmphasisBorderColor
        }
      },
      // 支持单独的区域颜色配置
      ...(mapRegionData.length > 0 && { regions: mapRegionData })
    },
    visualMap: attribute.showVisualMap
      ? {
          min: attribute.visualMapMin,
          max: attribute.visualMapMax,
          left: attribute.visualMapLeft,
          top: attribute.visualMapTop,
          text: [attribute.visualMapMaxText, attribute.visualMapMinText],
          textStyle: {
            color: attribute.visualMapTextColor
          },
          inRange: {
            color: [attribute.visualMapMinColor, attribute.visualMapMaxColor]
          },
          calculable: true
        }
      : null,
    series: scatterSeries
  };

  // 强制重新渲染，特别是在地图切换时
  chart?.clear();
  chart?.setOption(chartOption);
};

// 初始化
onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));
  refreshCptData();
});
</script>

<style scoped></style>
