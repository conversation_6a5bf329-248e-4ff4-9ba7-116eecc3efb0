const dataText = JSON.stringify([
  { name: '北京', lng: 116.46, lat: 39.92, value: 150 },
  { name: '上海', lng: 121.48, lat: 31.22, value: 200 },
  { name: '广州', lng: 113.23, lat: 23.16, value: 120 },
  { name: '深圳', lng: 114.07, lat: 22.62, value: 180 },
  { name: '杭州', lng: 120.19, lat: 30.26, value: 90 },
  { name: '南京', lng: 118.78, lat: 32.04, value: 110 },
  { name: '成都', lng: 104.06, lat: 30.67, value: 130 },
  { name: '西安', lng: 108.95, lat: 34.27, value: 100 },
  { name: '武汉', lng: 114.31, lat: 30.52, value: 140 },
  { name: '重庆', lng: 106.54, lat: 29.59, value: 160 }
]);

export default {
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: dataText
  },
  attribute: {
    // 标题配置
    titleText: '全国数据分布散点图',
    subtext: '数据可视化',
    titleLeft: 'center',
    titleTop: 10,
    titleFontSize: 18,
    titleColor: '#333',
    subTitleColor: '#666',
    subTitleFontSize: 12,
    
    // 地图配置
    map: 'china', // 地图类型
    roam: true, // 允许缩放和平移
    zoom: 1.2, // 初始缩放比例
    center: [104, 35], // 地图中心点 [经度, 纬度]
    
    // 地图样式
    showMapLabel: true, // 显示地图标签
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0', // 地图区域颜色
    mapBorderColor: '#999', // 地图边界颜色
    mapBorderWidth: 1,
    
    // 地图高亮样式
    showMapEmphasisLabel: true,
    mapEmphasisLabelColor: '#000',
    mapEmphasisAreaColor: '#e0e0e0',
    mapEmphasisBorderColor: '#666',
    
    // 散点配置
    seriesName: '数据点',
    scatterType: 'scatter', // 'scatter' 或 'effectScatter'
    scatterSymbol: 'circle', // 散点符号
    scatterBaseSize: 8, // 基础大小
    scatterSizeMultiplier: 0.5, // 大小倍数
    scatterColor: '#ff6b6b', // 散点颜色
    scatterOpacity: 0.8, // 散点透明度
    scatterBorderWidth: 1,
    scatterBorderColor: '#fff',
    
    // 散点高亮样式
    scatterEmphasisColor: '#ff4757',
    scatterEmphasisShadowBlur: 10,
    scatterEmphasisShadowColor: '#ff4757',
    
    // 散点标签
    showScatterLabel: true,
    scatterLabelPosition: 'top',
    scatterLabelColor: '#333',
    scatterLabelSize: 12,
    scatterLabelFormat: 'name', // 'name', 'value', 'both'
    
    // 涟漪效果（仅当 scatterType 为 'effectScatter' 时生效）
    rippleBrushType: 'stroke', // 'stroke' 或 'fill'
    rippleScale: 2.5,
    ripplePeriod: 4,
    
    // 视觉映射
    showVisualMap: false,
    visualMapMin: 0,
    visualMapMax: 200,
    visualMapLeft: 'left',
    visualMapTop: 'bottom',
    visualMapMaxText: '高',
    visualMapMinText: '低',
    visualMapTextColor: '#333',
    visualMapMinColor: '#50a3ba',
    visualMapMaxColor: '#eac736',

    // 区域颜色配置
    regionColors: {}, // 存储每个区域的自定义颜色
    regionValues: {} // 存储每个区域的数值（可选）
  }
};
