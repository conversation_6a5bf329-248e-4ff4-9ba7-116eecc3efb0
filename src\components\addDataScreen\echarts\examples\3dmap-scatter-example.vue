<!-- 3D地图散点图使用示例 -->
<template>
  <div class="example-container">
    <h2>3D地图散点图示例</h2>
    <div class="chart-container">
      <cpt-chart-3dmap 
        :width="800" 
        :height="600" 
        :option="chartOption" 
      />
    </div>
    <div class="controls">
      <el-button @click="toggleScatter">{{ showScatter ? '隐藏' : '显示' }}散点</el-button>
      <el-button @click="changeScatterData">更换散点数据</el-button>
      <el-button @click="resetView">重置视角</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChart3dmap from '../cpt-chart-3dmap.vue';

defineOptions({
  name: '3dmap-scatter-example'
});

// 示例数据
const mapData = [
  { 'name': '毕节市', 'value': 105 },
  { 'name': '遵义市', 'value': 41 },
  { 'name': '铜仁市', 'value': 30 },
  { 'name': '贵阳市', 'value': 52 },
  { 'name': '黔东南苗族侗族自治州', 'value': 66 },
  { 'name': '黔南布依族苗族自治州', 'value': 12 },
  { 'name': '安顺市', 'value': 99 },
  { 'name': '六盘水市', 'value': 72 },
  { 'name': '黔西南布依族苗族自治州', 'value': 82 }
];

const scatterDataSet1 = [
  { 'name': '贵阳数据中心', 'lng': 106.713478, 'lat': 26.578343, 'value': 150 },
  { 'name': '遵义分站', 'lng': 107.297104, 'lat': 27.725654, 'value': 89 },
  { 'name': '毕节监测点', 'lng': 105.300492, 'lat': 27.302612, 'value': 76 },
  { 'name': '铜仁传感器', 'lng': 109.191555, 'lat': 27.718346, 'value': 65 }
];

const scatterDataSet2 = [
  { 'name': '安顺基站', 'lng': 105.932188, 'lat': 26.245544, 'value': 120 },
  { 'name': '六盘水节点', 'lng': 104.846743, 'lat': 26.584643, 'value': 95 },
  { 'name': '黔南中心', 'lng': 107.517156, 'lat': 26.258219, 'value': 110 }
];

const showScatter = ref(true);
const currentScatterData = ref(scatterDataSet1);

const chartOption = computed(() => ({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify({
      mapData: mapData,
      scatterData: currentScatterData.value
    })
  },
  attribute: {
    map: 'areaGuizhou',
    titleText: '贵州省3D地图散点示例',
    mapHeight: 4,
    distance: 120,
    alpha: 45,
    beta: -20,
    isShowLabel: true,
    labelColor: '#fff',
    fontSize: 12,
    mapBackgroundColor: '#4389ED',
    borderWidth: 2,
    borderColor: '#61CFF8',
    isShowLed: true,
    ledColor: '#fff',
    mapLedColor: '#007EE8',
    mapLedBorderWidth: 3,
    mapLedBorderColor: '#6BECF5',
    
    // 散点配置
    showScatter: showScatter.value,
    scatterName: '监测站点',
    scatterSymbol: 'pin',
    scatterSize: [10, 30],
    scatterColor: '#FF6B6B',
    scatterOpacity: 0.9,
    scatterEmphasisColor: '#FF4757',
    showScatterLabel: true,
    scatterLabelColor: '#fff',
    scatterLabelSize: 14
  }
}));

// 控制方法
const toggleScatter = () => {
  showScatter.value = !showScatter.value;
};

const changeScatterData = () => {
  currentScatterData.value = currentScatterData.value === scatterDataSet1 
    ? scatterDataSet2 
    : scatterDataSet1;
};

const resetView = () => {
  // 可以通过修改attribute来重置视角
  console.log('重置视角');
};
</script>

<style scoped>
.example-container {
  padding: 20px;
}

.chart-container {
  width: 800px;
  height: 600px;
  border: 1px solid #ddd;
  margin: 20px 0;
}

.controls {
  display: flex;
  gap: 10px;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}
</style>
