<!-- 简单区域颜色测试 -->
<template>
  <div class="test-container">
    <h2>简单区域颜色测试</h2>

    <div class="controls">
      <el-button @click="enableColors">启用区域颜色</el-button>
      <el-button @click="disableColors">禁用区域颜色</el-button>
      <el-button @click="showConfig">显示配置</el-button>
    </div>

    <div class="chart-wrapper">
      <cpt-chart-geo-scatter :width="800" :height="600" :option="chartOption" :key="chartKey" />
    </div>

    <div class="config-display">
      <h3>当前配置</h3>
      <pre>{{ JSON.stringify(chartOption.attribute.regionColors, null, 2) }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';

defineOptions({
  name: 'simple-region-test'
});

const chartKey = ref(0);
const colorsEnabled = ref(false);

const chartOption = computed(() => ({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify([
      { name: '七星关区', lng: 105.284852, lat: 27.302085, value: 100 },
      { name: '大方县', lng: 105.609254, lat: 27.143521, value: 80 }
    ])
  },
  attribute: {
    titleText: '毕节市区域颜色测试',
    map: 'bijie',
    roam: true,
    zoom: 4,
    center: [105.5, 27.0],

    showMapLabel: true,
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,

    showMapEmphasisLabel: true,
    mapEmphasisLabelColor: '#000',
    mapEmphasisAreaColor: '#e0e0e0',
    mapEmphasisBorderColor: '#666',

    seriesName: '测试数据',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 10,
    scatterColor: '#ff6b6b',
    showScatterLabel: true,

    // 区域颜色配置
    regionColors: colorsEnabled.value
      ? {
          '七星关区': '#FF6B6B',
          '大方县': '#4ECDC4',
          '黔西市': '#45B7D1',
          '金沙县': '#96CEB4'
        }
      : {},
    regionValues: {}
  }
}));

const enableColors = () => {
  colorsEnabled.value = true;
  chartKey.value++;
  console.log('启用区域颜色');
};

const disableColors = () => {
  colorsEnabled.value = false;
  chartKey.value++;
  console.log('禁用区域颜色');
};

const showConfig = () => {
  console.log('当前配置:', chartOption.value.attribute);
};

onMounted(() => {
  console.log('简单区域颜色测试页面加载完成');
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.config-display {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.config-display pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

h2,
h3 {
  color: #333;
}
</style>
