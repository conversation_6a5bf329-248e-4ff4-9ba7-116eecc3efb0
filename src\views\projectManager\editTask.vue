<!-- 任务 -->
<template>
  <container-card>
    <div class="handle-title">
      <div class="item" @click="goBack">
        <el-icon><ArrowLeft /></el-icon> 返回
      </div>
      <div class="center">{{ title }}</div>
      <el-button type="primary" @click="handleSubmitAddTask">保存</el-button>
    </div>
    <el-form ref="addTaskFormRef" :model="addTaskForm" :rules="addTaskFormRules" label-position="top">
      <div class="content-box">
        <div class="flex-item">
          <el-form-item label="业务模块" prop="moduleId">
            <el-select
              v-model="addTaskForm.moduleId"
              :disabled="title == '修改任务'"
              filterable
              clearable
              placeholder="请选择业务模块"
              style="width: 100%"
              @change="changeModal"
            >
              <el-option v-for="item in appTypeOptions" :key="item.id" :label="getAddTaskLable(item)" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <!-- <div class="flex-item">
          <el-form-item label="任务类型" prop="scope">
            <el-select v-model="addTaskForm.scope" placeholder="请选择" style="width:100%" @change="handleChangeScope">
              <el-option
                v-for="item in scopeList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </div> -->
        <div class="flex-item">
          <el-form-item label="任务名称" prop="name">
            <el-input v-model="addTaskForm.name" placeholder="请输入任务名称" maxlength="30" />
          </el-form-item>
        </div>
        <div class="flex-item">
          <el-form-item label="任务字段" prop="taskGroupModels">
            <el-input
              placeholder="请选择任务字段"
              readonly
              @focus="handleOpenField"
              v-if="!addTaskForm.taskGroupModels || (addTaskForm.taskGroupModels && addTaskForm.taskGroupModels.length == 0)"
            />
            <el-input value="已选择任务字段" v-else readonly @focus="handleOpenField"></el-input>
          </el-form-item>
        </div>
        <div class="flex-item">
          <el-form-item label="任务权限" prop="type">
            <el-select
              v-model="addTaskForm.type"
              @focus="handleFocusTaskOptType"
              :disabled="title == '修改任务'"
              placeholder="请选择任务权限"
              style="width: 100%"
            >
              <template v-if="addTaskForm.scope == 0">
                <el-option v-for="item in taskOptTypeComputedOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </template>
              <template v-else>
                <el-option v-for="item in taskOptTypeComputedOptionsOut" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </template>
            </el-select>
          </el-form-item>
        </div>
        <div class="flex-item" v-if="addTaskForm.scope === 5 || addTaskForm.type == 2">
          <el-form-item :label="taskName" prop="parcelNames">
            <el-input v-model="addTaskForm.parcelNames" multiple :placeholder="taskPlacehlder" @focus="handleOpenZDManagementDialog"></el-input>
          </el-form-item>
        </div>
        <div class="flex-item" v-show="addTaskForm.scope == 0">
          <el-link type="danger" class="title-link" @click="clearAllMember">清除所有任务人员</el-link>
          <el-form-item label="任务人员" prop="receiverNames">
            <el-input v-model="addTaskForm.receiverNames" placeholder="请选择任务人员" readonly @focus="handleOpenUser"></el-input>
          </el-form-item>
        </div>
        <div class="flex-item" v-show="addTaskForm.scope == 0 && addTaskForm.parcelNames && addTaskForm.receiverNames">
          <el-form-item label="分配任务数据">
            <el-radio-group v-model="addTaskForm.allocationType">
              <el-radio :value="item.value" v-for="(item, index) in allocationTypeList" :key="index">{{ item.label }}</el-radio>
            </el-radio-group>
            <el-link v-show="addTaskForm.allocationType != 0" type="primary" @click="goAllot" style="margin-left: 10px">去分配</el-link>
          </el-form-item>
        </div>
        <div class="flex-item" v-show="addTaskForm.scope == 0">
          <el-form-item label="预计工作量" prop="workload">
            <el-input v-model="addTaskForm.workload" placeholder="请输入预计工作量" maxlength="100" type="number"></el-input>
          </el-form-item>
        </div>
        <div class="flex-item">
          <el-form-item label="任务工期" prop="dateRange">
            <el-date-picker
              @input="handleDateRank"
              v-model="addTaskForm.dateRange"
              style="width: 100%"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </div>
        <div class="flex-item">
          <el-form-item label="项目负责人" prop="chargeUserId">
            <el-select v-model="addTaskForm.chargeUserId" filterable clearable remote placeholder="请选择项目负责人" style="width: 100%">
              <el-option v-for="item in taskReceiversOptions" :key="item.userId" :label="item.custName" :value="item.userId"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="flex-item">
          <el-form-item label="任务描述" prop="descs">
            <el-input v-model="addTaskForm.descs" type="textarea" placeholder="请输入任务描述" maxlength="100"></el-input>
          </el-form-item>
        </div>
        <div class="flex-item">
          <el-form-item label="任务备注">
            <el-input v-model="addTaskForm.remark" type="textarea" placeholder="请输入任务备注" maxlength="100"></el-input>
          </el-form-item>
        </div>

        <div class="flex-item">
          <el-form-item label="大屏分组字段">
            <el-input v-model="addTaskForm.groupName" type="textarea" placeholder="请输入乡镇名称" maxlength="100"></el-input>
          </el-form-item>
        </div>
        <div class="flex-item">
          <el-form-item label="应采数据量（㎡）">
            <el-input v-model="addTaskForm.area" placeholder="请输入应采集面积（㎡）" maxlength="100" type="number">
              <template #append>㎡</template>
            </el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <!-- 选择人员 -->
    <chooseUser
      :userDialog="userDialog"
      @chageUserDialog="chageUserDialog"
      @getChooseUser="getChooseUser"
      :users="addTaskForm.users"
      :flowList="[]"
      :isShowDisable="false"
    ></chooseUser>
    <!-- 选择任务字段 -->
    <chooseField
      v-if="!!addTaskForm.moduleId"
      :moduleId="addTaskForm.moduleId"
      :chooseFieldDialog="chooseFieldDialog"
      @update:chooseFieldDialog="chooseFieldDialog = $event"
      :taskGroupModels="addTaskForm.taskGroupModels"
      @closeFieldDialog="closeFieldDialog"
      @submitField="submitField"
      :title="title"
    ></chooseField>
    <!-- 任务错误弹窗 -->
    <el-dialog v-model="errorDialog" title="错误提示" :close-on-click-modal="false" width="400px" :before-close="handleCloseErrorDialog">
      <div class="dialog-row" style="color: red">以下数据在其他任务存在(共{{ errorList.length }}条)：</div>
      <div class="dialog-box">
        <div class="dialog-row" v-for="(item, index) in errorList" :key="index">
          <div class="item">数据名称：{{ item.parcelName }}</div>
          <div class="item">任务名称：{{ item.taskName }}</div>
        </div>
      </div>
    </el-dialog>
    <!-- 分配任务数据 -->
    <allotData
      @closeAllot="closeAllot"
      :allotDialog="allotDialog"
      :users="addTaskForm.users"
      :selectedList="selectedList"
      @submitAllotData="submitAllotData"
    ></allotData>
    <!-- 筛选数据 -->
    <searchData
      :searchDialog="searchDialog"
      @closeSearchDialog="closeSearchDialog"
      @getChooseData="getChooseData"
      :zdList="selectedList"
      :moduleIdPop="String(addTaskForm.moduleId)"
      :isManager="false"
      :taskId="addTaskForm.id ? String(addTaskForm.id) : ''"
      :taskName="addTaskForm.name"
    ></searchData>
  </container-card>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed, onUnmounted } from 'vue';
import { getModuleList } from '@/api/modal';
import { getSearchTask, addTask, getTaskDetail } from '@/api/task';
import chooseUser from './component/taskManagement/chooseUser.vue';
import allotData from '@/components/allotData/index.vue';
import searchData from '@/components/taskDataSearch/index.vue';
import chooseField from './component/taskManagement/chooseField.vue';
import { listUser } from '@/api/system/user';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';

// 定义接口
interface AppTypeOption {
  id: string | number;
  moduleName: string;
  status: number;
  [key: string]: any;
}

interface UserData {
  userId: string | number;
  custName: string;
  deptId: number;
  [key: string]: any;
}

interface TaskGroupModel {
  linkId: string | number;
  groupId: string | number;
  fieldModels: Array<{
    fieldId: string | number;
    required: number;
    attribution: {
      default?: any;
      expendList?: Array<{
        enName: string;
        cnName: string;
        checked?: boolean;
        newRequired?: boolean;
        default?: any;
      }>;
    };
  }>;
}

interface ParcelData {
  id?: string | number;
  geomArcgis: any;
  custName: string;
  lockNum: number;
  parcelId: string | number;
  parcelName: string;
  taskId: string | number | null;
  userId?: number;
}

// 定义User接口让它与chooseUser组件兼容
interface User {
  userId: number;
  custName: string;
  deptId: number;
  userName: string;
  nickName: string;
  deptName: string;
  checked: boolean;
  isContains: any[];
}

interface TaskFormData {
  id?: string | number;
  name: string;
  moduleId: string | number;
  type: string | number;
  users: User[];
  workload: string;
  dateRange: string[];
  chargeUserId: string | number;
  descs: string;
  remark: string;
  taskGroupModels: TaskGroupModel[];
  scope: number;
  receiverNames?: string;
  parcelNames?: string;
  parcelIds?: (string | number)[];
  chargeUserName?: string;
  allocationType?: number;
  taskParcelModels?: ParcelData[];
  timeTamp?: string;
  area: number;
  groupName: string;
}

interface AllocationType {
  label: string;
  value: number;
}

interface ErrorItem {
  parcelName: string;
  taskName: string;
}

// 路由信息
const route = useRoute();
const router = useRouter();

// 引用实例
const addTaskFormRef = ref<FormInstance>();

// 响应式状态
const title = ref('新增任务');
const appTypeOptions = ref<AppTypeOption[]>([]);
const taskReceiversOptions = ref<UserData[]>([]);
const userDialog = ref(false);
const chooseFieldDialog = ref(false);
const errorDialog = ref(false);
const errorList = ref<ErrorItem[]>([]);
const allotDialog = ref(false);
const selectedList = ref<ParcelData[]>([]);
const searchDialog = ref(false);
const taskName = ref('任务数据');
const taskPlacehlder = ref('请选择任务数据');
const order = ref<number>(3);

// 表单数据
const addTaskForm = reactive<TaskFormData>({
  name: '',
  moduleId: '',
  type: '',
  users: [],
  workload: '',
  dateRange: [],
  chargeUserId: '',
  descs: '',
  remark: '',
  taskGroupModels: [],
  scope: 0,
  area: '',
  groupName: ''
});

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  type: '',
  moduleId: '',
  taskStatus: '',
  dateRange: [] as number[],
  taskReceivers: '',
  scope: ''
});

// 用户查询参数
const searchUser = reactive({
  pageNum: 1,
  pageSize: 10
});

// 表单验证规则
const validateParcelNames = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请选择宗地'));
  } else {
    callback();
  }
};

const validateModule = (rule: any, value: any, callback: any) => {
  if (value === '' || value == 0) {
    callback(new Error('请选择业务模块'));
  } else {
    callback();
  }
};

const validateInternalFields = (rule: any, value: any, callback: any) => {
  if (addTaskForm.scope === 0) {
    if (!value) {
      callback(new Error(rule.message));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

const addTaskFormRules = reactive<FormRules>({
  moduleId: [{ required: true, validator: validateModule, trigger: 'change' }],
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  scope: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
  type: [{ required: true, message: '请选择任务权限', trigger: 'change' }],
  taskGroupModels: [{ required: true, message: '请选择任务字段', trigger: 'change' }],
  chargeUserId: [{ required: true, message: '请选择项目负责人', trigger: 'blur' }],
  descs: [{ required: true, message: '请输入任务描述', trigger: 'blur' }],
  remark: [{ required: true, message: '请输入任务备注', trigger: 'blur' }],
  // 内部任务特有的验证规则
  receiverNames: [{ required: true, validator: validateInternalFields, message: '请选择任务人员', trigger: 'change' }],
  workload: [{ required: true, validator: validateInternalFields, message: '请输入预计工作量', trigger: 'blur' }],
  dateRange: [{ required: true, validator: validateInternalFields, message: '请选择任务工期', trigger: 'change' }],
  parcelNames: [{ required: true, validator: validateParcelNames, trigger: 'blur' }]
});

// 任务类型选项
const scopeList = [
  { label: '内部使用', value: 0 },
  { label: '外部使用', value: 5 }
];

// 任务权限选项
const taskOptTypeComputedOptions = [
  { label: '新增', value: 1 },
  { label: '修改', value: 2 }
];

// 外部任务权限选项
const taskOptTypeComputedOptionsOut = [{ label: '只读', value: 0 }];

// 任务分配类型选项
const allocationTypeList: AllocationType[] = [
  { label: '共享任务', value: 0 },
  { label: '自定义分配', value: 1 }
];

// 生命周期钩子
onMounted(() => {
  const taskId = route.params.id;
  if (taskId && taskId !== '0') {
    title.value = '修改任务';
    getTaskDetail(taskId as string).then(handleTaskDetail);
  }
  getModelList();
  getUserList();
});

// 组件卸载时清空数据
onUnmounted(() => {
  selectedList.value = [];
  addTaskForm.parcelNames = '';
  addTaskForm.parcelIds = [];
  addTaskForm.taskParcelModels = [];
});

/**
 * 获取任务标签
 * @param item 任务类型
 * @returns 任务标签
 */
const getAddTaskLable = (item: AppTypeOption): string => {
  return item.status == 8 ? `${item.moduleName}(测试)` : item.moduleName;
};

const getModelList = async () => {
  try {
    const res = await getModuleList([1, 8]);
    if (res.code == 200) {
      appTypeOptions.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

/**
 * 获取任务列表
 * @param searchOrder 搜索顺序
 */
const getSearchTaskList = (searchOrder: number) => {
  const type = 1; // type  1:我创建的  2：我收到的 3:全部
  let timeStart = '';
  let timeEnd = '';
  if (queryParams.dateRange && queryParams.dateRange.length !== 0) {
    timeStart = `${queryParams.dateRange[0]}`;
    timeEnd = `${queryParams.dateRange[1]}`;
  }
  const params = {
    pageSize: queryParams.pageSize,
    pageNum: queryParams.pageNum,
    type: queryParams.type,
    name: queryParams.name,
    moduleId: queryParams.moduleId,
    status: queryParams.taskStatus,
    createTimeStart: timeStart,
    createTimeEnd: timeEnd,
    userId: queryParams.taskReceivers,
    scope: queryParams.scope
  } as any;

  if (!searchOrder || searchOrder == 3) {
    order.value = 3;
    params.pageType = 3;
  } else if (searchOrder == 2) {
    order.value = 2;
    params.pageType = 2;
  } else if (searchOrder == 1) {
    order.value = 1;
    params.pageType = 1;
  }

  // getSearchTask(params, 1).then((res) => {
  //   if (res.code == 200) {
  //     // 仅用于回调后执行其他操作，实际数据未使用到
  //   } else {
  //     ElMessage.error(res.msg);
  //   }
  // });
};

const changeModal = () => {
  addTaskForm.taskGroupModels = [];
};

const handleOpenField = () => {
  if (!addTaskForm.moduleId) {
    addTaskFormRef.value?.validateField('moduleId');
    return false;
  } else {
    // 在打开弹窗前，确保传递的是深拷贝而不是响应式引用
    // 这样可以避免组件内部修改直接影响外部数据
    addTaskForm.taskGroupModels = JSON.parse(JSON.stringify(addTaskForm.taskGroupModels || []));
    chooseFieldDialog.value = true;
  }
};

const handleFocusTaskOptType = () => {
  if (!addTaskForm.moduleId) {
    addTaskFormRef.value?.validateField('moduleId');
    return false;
  }
};

/**
 * 处理输入
 * @param value 值
 */
const handleInput = (value: string) => {
  // 只保留非零开头的数字
  addTaskForm.workload = value.replace(/^0+([1-9]\d*)/, '$1').replace(/\D/g, '');
};

/**
 * 处理日期范围
 * @param val 日期范围
 */
const handleDateRank = (val: string[]) => {
  addTaskForm.dateRange = val;
};

const handleOpenZDManagementDialog = () => {
  if (!addTaskForm.moduleId) {
    addTaskFormRef.value?.validateField('moduleId');
    return false;
  } else {
    searchDialog.value = true;
  }
};

const goBack = () => {
  router.go(-1);
};

/**
 * 切换用户对话框
 * @param flg 布尔值
 */
const chageUserDialog = (flg: boolean) => {
  userDialog.value = flg;
};

const handleOpenUser = () => {
  userDialog.value = true;
};

/**
 * 获取选择用户
 * @param list 用户列表
 */
const getChooseUser = (list: User[]) => {
  addTaskForm.users = list.map((user) => ({
    ...user,
    userId: Number(user.userId),
    userName: user.userName || user.custName,
    nickName: user.nickName || user.custName,
    deptName: user.deptName || '',
    checked: user.checked || false,
    isContains: user.isContains || []
  }));
  const receiverNames = list.map((v) => v.custName);
  addTaskForm.receiverNames = receiverNames.join(',');
  addTaskFormRef.value?.clearValidate('users');
  userDialog.value = false;
};

const closeFieldDialog = () => {
  chooseFieldDialog.value = false;
  if (!addTaskForm.id) {
    addTaskForm.taskGroupModels = [];
  }
};

/**
 * 提交字段
 * @param list 任务组模型列表
 */
const submitField = (list: TaskGroupModel[]) => {
  // 使用深拷贝确保断开与组件内部数据的引用关系
  addTaskForm.taskGroupModels = JSON.parse(JSON.stringify(list));
  addTaskFormRef.value?.clearValidate('taskGroupModels');
  chooseFieldDialog.value = false;
};

/**
 * 关闭错误对话框
 */
const handleCloseErrorDialog = () => {
  errorDialog.value = false;
};

/**
 * 关闭分配对话框
 */
const closeAllot = () => {
  allotDialog.value = false;
};

/**
 * 提交分配数据
 * @param list 宗地列表
 */
const submitAllotData = (list: ParcelData[]) => {
  addTaskForm.taskParcelModels = list;
  for (let i = 0; i < list.length; i++) {
    if (list[i].userId) {
      //代表分配过，那么就把allocationType设置为1
      addTaskForm.allocationType = 1;
      break;
    }
  }
  allotDialog.value = false;
};

/**
 * 关闭搜索对话框
 */
const closeSearchDialog = () => {
  // 添加一个标志位，避免循环触发
  if (!searchDialog.value) return;
  searchDialog.value = false;
};

/**
 * 获取选择数据
 * @param list 列表
 */
const getChooseData = (list: any[]) => {
  selectedList.value = [];
  list.forEach((v) => {
    const obj: ParcelData = {
      geomArcgis: v.geomArcgis,
      custName: v.custName || '',
      lockNum: v.lockNum || 0,
      parcelId: v.id,
      parcelName: v.parcelName,
      taskId: v.taskId || null,
      userId: v.userId ? Number(v.userId) : undefined
    };

    if (v.geomWkb || v.isNew) {
      // 代表是后台反的数据
      obj.id = v.relationId || null;
    } else {
      obj.id = v.id;
    }

    selectedList.value.push(obj);
  });

  addTaskForm.taskParcelModels = selectedList.value;

  const parcelNames: string[] = [];
  const parcelIds: (string | number)[] = [];

  list.forEach((item) => {
    parcelNames.push(item.parcelName);
    parcelIds.push(item.id);
  });

  addTaskForm.parcelNames = parcelNames.join(',');
  addTaskForm.parcelIds = parcelIds;

  if (addTaskForm.parcelNames && addTaskForm.parcelNames !== '') {
    addTaskFormRef.value?.clearValidate('parcelNames');
  }

  searchDialog.value = false;
};

/**
 * 打开分配对话框
 */
const goAllot = () => {
  // 打开分配对话框
  allotDialog.value = true;
};

/**
 * 获取用户列表
 */
const getUserList = async () => {
  searchUser.pageNum = 1;
  searchUser.pageSize = 10000000;

  try {
    const response = await listUser(searchUser);
    if (response.code == 200) {
      taskReceiversOptions.value = response.rows.map((user) => ({
        ...user,
        custName: user.custName
      }));
    }
  } catch (error) {}
};

/**
 * 提交添加任务
 */
const handleSubmitAddTask = () => {
  // 先进行表单验证
  addTaskFormRef.value?.validate(async (valid) => {
    if (!valid) {
      ElMessage.error('请填写必填项');
      return;
    }

    // 特别验证内部任务的必填项
    if (addTaskForm.scope === 0) {
      if (!addTaskForm.receiverNames || !addTaskForm.users || addTaskForm.users.length === 0) {
        ElMessage.error('请选择任务人员');
        addTaskFormRef.value?.validateField('receiverNames');
        return;
      }
      if (!addTaskForm.workload) {
        ElMessage.error('请输入预计工作量');
        addTaskFormRef.value?.validateField('workload');
        return;
      }
      if (!addTaskForm.dateRange || addTaskForm.dateRange.length !== 2) {
        ElMessage.error('请选择任务工期');
        addTaskFormRef.value?.validateField('dateRange');
        return;
      }
    }

    // 处理没有图形的时候问题 id是空的
    if (addTaskForm.taskParcelModels && Array.isArray(addTaskForm.taskParcelModels)) {
      addTaskForm.taskParcelModels = addTaskForm.taskParcelModels.map((v) => ({
        ...v,
        id: v.id || v.parcelId
      }));
    }

    // 设置项目负责人名称
    const chargeUser = taskReceiversOptions.value.find((i) => i.userId === Number(addTaskForm.chargeUserId));
    if (chargeUser) {
      addTaskForm.chargeUserName = chargeUser.custName;
    }

    const params = {
      moduleId: addTaskForm.moduleId,
      name: addTaskForm.name,
      type: addTaskForm.type,
      remark: addTaskForm.remark,
      descs: addTaskForm.descs,
      startTime: addTaskForm.dateRange[0] ? new Date(addTaskForm.dateRange[0]).getTime() : null,
      endTime: addTaskForm.dateRange[1] ? new Date(addTaskForm.dateRange[1]).getTime() : null,
      workload: addTaskForm.workload,
      chargeUserName: addTaskForm.chargeUserName,
      chargeUserId: addTaskForm.chargeUserId,
      receiverNames: addTaskForm.receiverNames,
      users: addTaskForm.users,
      taskGroupModels: addTaskForm.taskGroupModels,
      scope: addTaskForm.scope,
      taskParcelModels: addTaskForm.taskParcelModels,
      allocationType: addTaskForm.allocationType,
      timeTamp: addTaskForm.timeTamp,
      area: addTaskForm.area === '' ? 0 : Number(addTaskForm.area),
      groupName: addTaskForm.groupName
    } as any;

    if (addTaskForm.type === 2 || addTaskForm.type === 0) {
      params.parcelIds = addTaskForm.parcelIds;
    }

    if (addTaskForm.id) {
      params.id = addTaskForm.id;
    }

    try {
      const res = await addTask(params);
      if (res.code === 200) {
        ElMessage.success('操作成功');
        getSearchTaskList(order.value);
        // 清空选择的数据
        selectedList.value = [];
        //重置全选状态
        addTaskForm.parcelNames = '';
        addTaskForm.parcelIds = [];
        addTaskForm.taskParcelModels = [];
        //清空当前页面所有数据
        addTaskFormRef.value?.resetFields();
        goBack();
      } else if (res.code === 500) {
        if (res.msg) {
          ElMessage.error(res.msg);
        } else {
          errorDialog.value = true;
          errorList.value = res.data.errors || [];
        }
      } else {
        ElMessage.error(res.msg || '保存失败');
      }
    } catch (err) {
      ElMessage.error('保存失败');
    }
  });
};

/**
 * 处理任务详情
 * @param res 响应
 */
const handleTaskDetail = (res: any) => {
  if (res.code == 200) {
    const obj = res.data;
    const dateRange = [obj.startTime, obj.endTime];

    obj.dateRange = dateRange;

    const users = obj.users.map((v: any) => ({
      deptId: v.deptId,
      userId: Number(v.userId),
      custName: v.custName
    }));

    obj.users = users;

    if (obj.type == 2 || obj.type == 0) {
      // 代表是指派任务 有宗地
      const parcelNames: string[] = [];
      const ids: (string | number)[] = [];

      // 确保 taskParcelModels 中的 userId 是 number 类型或 undefined
      if (obj.taskParcelModels && Array.isArray(obj.taskParcelModels)) {
        obj.taskParcelModels = obj.taskParcelModels.map((v: any) => ({
          ...v,
          userId: v.userId ? Number(v.userId) : undefined
        }));
      }

      obj.taskParcelModels.forEach((v: any) => {
        ids.push(v.parcelId);
        parcelNames.push(v.parcelName);
      });

      obj.parcelNames = parcelNames.join(',');
      selectedList.value = obj.taskParcelModels;
    }

    Object.assign(addTaskForm, obj);
  } else {
    ElMessage.error(res.msg);
  }
};

/**
 * 清除所有成员
 */
const clearAllMember = () => {
  ElMessageBox.confirm('确定要清除所有任务人员吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      addTaskForm.users = [];
      addTaskForm.receiverNames = '';
      // 清除任务人员后，重置相关的分配数据
      addTaskForm.allocationType = 0;
      if (addTaskForm.taskParcelModels) {
        addTaskForm.taskParcelModels = addTaskForm.taskParcelModels.map((model) => ({
          ...model,
          userId: undefined,
          custName: model.custName || ''
        }));
      }
      // 触发表单验证
      addTaskFormRef.value?.validateField('receiverNames');
      ElMessage({
        type: 'success',
        message: '清除成功'
      });
    })
    .catch(() => {});
};

// 添加watch监听searchDialog的变化
watch(searchDialog, (newVal, oldVal) => {
  // 获取调用栈信息
});
</script>

<style lang="scss" scoped>
.handle-title {
  display: flex;
  padding-bottom: 10px;
  border-bottom: #d3d3d3 solid 2px;
  margin-bottom: 10px;
  justify-content: space-between;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
  .item {
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  .center {
    color: rgba(0, 0, 0, 1);
    font-size: 16px;
    font-weight: 600;
  }
}
.content-box {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 10px;
  .flex-item {
    width: calc(100% / 4 - 16px);
    margin-right: 16px;
    margin-bottom: 16px;
    position: relative;
    .title-link {
      position: absolute;
      right: 0;
      top: 8px;
      z-index: 1;
    }
  }
}
</style>
