import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FormulaData, FormulaQuery } from '@/api/forumulation/types';

/**
 * 通过类型查询公式列表 type 1 函数 2 常量 不传查全部
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getForumuList(params: FormulaQuery): AxiosPromise<any> {
  return request({
    url: '/tool/formula/selectList',
    method: 'post',
    params: params
  });
}

/**
 * 添加表达式的保存按钮
 * type 1 数字函数 2 字符串函数 3 系统函数 4 几何图形 动态常量 5 线要素函数 6 操作符 7：属性组函数 8：模块变量 99 变量
 * @param list 公式数据
 * @returns {AxiosPromise}
 */
export function saveForumulation(list: FormulaData | FormulaData[]): AxiosPromise<any> {
  return request({
    url: '/tool/formula/save',
    method: 'post',
    data: list
  });
}

/**
 * 校验表达式的内容 是否正确
 * @param params 公式数据
 * @returns {AxiosPromise}
 */
export function checkForumulation(params: FormulaQuery): AxiosPromise<any> {
  return request({
    url: '/tool/formula/check',
    method: 'post',
    params: params
  });
}

/**
 * 计算表达式的内容
 * @param params 公式数据
 * @returns {AxiosPromise}
 */
export function calculateForumulation(params: FormulaQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/formula/calculate',
    method: 'post',
    params: params
  });
}
