<!-- 状态符号测试页面 -->
<template>
  <div class="test-container">
    <h2>双色状态符号测试</h2>
    
    <div class="controls">
      <el-button @click="enableStatusSymbols">启用状态符号</el-button>
      <el-button @click="disableStatusSymbols">禁用状态符号</el-button>
      <el-button @click="changeSymbols">更换符号</el-button>
      <el-button @click="useImageSymbols">使用图片符号</el-button>
    </div>
    
    <div class="chart-wrapper">
      <cpt-chart-geo-scatter 
        :width="800" 
        :height="600" 
        :option="chartOption"
        :key="chartKey"
      />
    </div>
    
    <div class="status-info">
      <h3>当前配置</h3>
      <div class="config-grid">
        <div class="config-item">
          <h4>基础配置</h4>
          <p><strong>启用状态颜色:</strong> {{ chartOption.attribute.enableStatusColors ? '是' : '否' }}</p>
          <p><strong>启用状态符号:</strong> {{ chartOption.attribute.enableStatusSymbols ? '是' : '否' }}</p>
          <p><strong>显示图例:</strong> {{ chartOption.attribute.showLegend ? '是' : '否' }}</p>
        </div>
        
        <div class="config-item">
          <h4>已完成状态</h4>
          <p><strong>颜色:</strong> <span :style="{color: chartOption.attribute.completedColor}">{{ chartOption.attribute.completedColor }}</span></p>
          <p><strong>符号:</strong> {{ chartOption.attribute.completedSymbol }}</p>
          <p><strong>标签:</strong> {{ chartOption.attribute.completedLabel }}</p>
        </div>
        
        <div class="config-item">
          <h4>未完成状态</h4>
          <p><strong>颜色:</strong> <span :style="{color: chartOption.attribute.uncompletedColor}">{{ chartOption.attribute.uncompletedColor }}</span></p>
          <p><strong>符号:</strong> {{ chartOption.attribute.uncompletedSymbol }}</p>
          <p><strong>标签:</strong> {{ chartOption.attribute.uncompletedLabel }}</p>
        </div>
      </div>
    </div>
    
    <div class="data-info">
      <h3>测试数据</h3>
      <div class="data-grid">
        <div class="data-column">
          <h4>已完成项目</h4>
          <ul>
            <li v-for="item in completedItems" :key="item.name">
              {{ item.name }} ({{ item.value }})
            </li>
          </ul>
        </div>
        <div class="data-column">
          <h4>未完成项目</h4>
          <ul>
            <li v-for="item in uncompletedItems" :key="item.name">
              {{ item.name }} ({{ item.value }})
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';

defineOptions({
  name: 'status-symbols-test'
});

const chartKey = ref(0);

// 测试数据
const testData = [
  { name: '北京项目', lng: 116.46, lat: 39.92, value: 150, status: true },
  { name: '上海项目', lng: 121.48, lat: 31.22, value: 200, status: false },
  { name: '广州项目', lng: 113.23, lat: 23.16, value: 120, status: true },
  { name: '深圳项目', lng: 114.07, lat: 22.62, value: 180, status: false },
  { name: '杭州项目', lng: 120.19, lat: 30.26, value: 160, status: true },
  { name: '成都项目', lng: 104.06, lat: 30.67, value: 140, status: false },
  { name: '武汉项目', lng: 114.31, lat: 30.52, value: 130, status: true },
  { name: '西安项目', lng: 108.95, lat: 34.27, value: 110, status: false }
];

const completedItems = computed(() => testData.filter(item => item.status === true));
const uncompletedItems = computed(() => testData.filter(item => item.status === false));

const chartOption = ref({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify(testData)
  },
  attribute: {
    titleText: '项目状态符号分布图',
    subtext: '不同状态使用不同符号',
    titleLeft: 'center',
    titleTop: 10,
    titleFontSize: 18,
    titleColor: '#333',
    subTitleColor: '#666',
    subTitleFontSize: 12,
    
    map: 'china',
    roam: true,
    zoom: 1.2,
    center: [104, 35],
    
    showMapLabel: true,
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,
    
    seriesName: '项目数据',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 15,
    scatterSizeMultiplier: 0.8,
    scatterColor: '#ff6b6b',
    scatterOpacity: 0.8,
    scatterBorderWidth: 2,
    scatterBorderColor: '#fff',
    
    showScatterLabel: true,
    scatterLabelPosition: 'top',
    scatterLabelColor: '#333',
    scatterLabelSize: 12,
    scatterLabelFormat: 'name',
    
    // 双色状态配置
    enableStatusColors: true,
    statusField: 'status',
    completedColor: '#52c41a',
    uncompletedColor: '#ff4d4f',
    completedLabel: '已完成',
    uncompletedLabel: '未完成',
    showLegend: true,
    
    // 状态符号配置
    enableStatusSymbols: false,
    completedSymbol: 'circle',
    uncompletedSymbol: 'triangle',
    
    // 已完成自定义符号
    completedCustomImageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTkgMTZMMTIgMTNMMjIgM0wyMCA0TDEyIDE2TDQgOEw2IDZMOSAxNloiIGZpbGw9IiM1MmM0MWEiLz4KPHN2Zz4K',
    completedCustomImageSize: 25,
    completedCustomPath: 'M12,2L13.09,8.26L20,9L13.09,9.74L12,16L10.91,9.74L4,9L10.91,8.26L12,2Z',
    completedCustomPathSize: 20,
    completedCustomPathColor: '#52c41a',
    
    // 未完成自定义符号
    uncompletedCustomImageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE4IDZMNiAxOE02IDZMMTggMTgiIHN0cm9rZT0iI2ZmNGQ0ZiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHN2Zz4K',
    uncompletedCustomImageSize: 25,
    uncompletedCustomPath: 'M18,6L6,18M6,6L18,18',
    uncompletedCustomPathSize: 20,
    uncompletedCustomPathColor: '#ff4d4f'
  }
});

const enableStatusSymbols = () => {
  chartOption.value.attribute.enableStatusSymbols = true;
  chartOption.value.attribute.completedSymbol = 'star';
  chartOption.value.attribute.uncompletedSymbol = 'triangle';
  chartKey.value++;
  console.log('启用状态符号');
};

const disableStatusSymbols = () => {
  chartOption.value.attribute.enableStatusSymbols = false;
  chartKey.value++;
  console.log('禁用状态符号');
};

const changeSymbols = () => {
  const symbols = ['circle', 'rect', 'triangle', 'diamond', 'star', 'heart'];
  const randomCompleted = symbols[Math.floor(Math.random() * symbols.length)];
  const randomUncompleted = symbols[Math.floor(Math.random() * symbols.length)];
  
  chartOption.value.attribute.completedSymbol = randomCompleted;
  chartOption.value.attribute.uncompletedSymbol = randomUncompleted;
  chartKey.value++;
  
  console.log(`更换符号: 已完成=${randomCompleted}, 未完成=${randomUncompleted}`);
};

const useImageSymbols = () => {
  chartOption.value.attribute.enableStatusSymbols = true;
  chartOption.value.attribute.completedSymbol = 'image';
  chartOption.value.attribute.uncompletedSymbol = 'image';
  chartKey.value++;
  console.log('使用图片符号');
};

onMounted(() => {
  console.log('状态符号测试页面加载完成');
  console.log('测试数据:', testData);
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.status-info {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.config-item {
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.config-item h4 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.config-item p {
  margin: 8px 0;
}

.data-info {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.data-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 15px;
}

.data-column {
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.data-column h4 {
  margin-top: 0;
  color: #333;
}

.data-column ul {
  list-style: none;
  padding: 0;
  margin: 10px 0;
}

.data-column li {
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

h2, h3 {
  color: #333;
}
</style>
