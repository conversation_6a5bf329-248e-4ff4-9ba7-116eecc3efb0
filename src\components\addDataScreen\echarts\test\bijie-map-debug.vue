<!-- 毕节地图调试页面 -->
<template>
  <div class="debug-container">
    <h2>毕节地图调试</h2>
    
    <div class="debug-info">
      <h3>地图注册状态</h3>
      <div class="status-grid">
        <div class="status-item">
          <span class="label">China地图:</span>
          <span :class="['status', chinaStatus.registered ? 'success' : 'error']">
            {{ chinaStatus.registered ? '✅ 已注册' : '❌ 未注册' }}
          </span>
          <span class="details">特征数: {{ chinaStatus.features }}</span>
        </div>
        <div class="status-item">
          <span class="label">Bijie地图:</span>
          <span :class="['status', bijieStatus.registered ? 'success' : 'error']">
            {{ bijieStatus.registered ? '✅ 已注册' : '❌ 未注册' }}
          </span>
          <span class="details">特征数: {{ bijieStatus.features }}</span>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h3>地图对比测试</h3>
      <div class="map-comparison">
        <div class="map-container">
          <h4>China地图</h4>
          <div class="chart-wrapper">
            <cpt-chart-geo-scatter 
              :width="400" 
              :height="300" 
              :option="chinaOption"
              :key="'china-' + refreshKey"
            />
          </div>
        </div>
        
        <div class="map-container">
          <h4>Bijie地图</h4>
          <div class="chart-wrapper">
            <cpt-chart-geo-scatter 
              :width="400" 
              :height="300" 
              :option="bijieOption"
              :key="'bijie-' + refreshKey"
            />
          </div>
        </div>
      </div>
    </div>
    
    <div class="controls">
      <el-button @click="refreshMaps">刷新地图</el-button>
      <el-button @click="testBijieData">测试毕节数据</el-button>
      <el-button @click="showMapDetails">显示地图详情</el-button>
    </div>
    
    <div class="data-section">
      <h3>测试数据</h3>
      <div class="data-comparison">
        <div class="data-item">
          <h4>China测试数据</h4>
          <pre>{{ JSON.stringify(chinaTestData, null, 2) }}</pre>
        </div>
        <div class="data-item">
          <h4>Bijie测试数据</h4>
          <pre>{{ JSON.stringify(bijieTestData, null, 2) }}</pre>
        </div>
      </div>
    </div>
    
    <div class="debug-output">
      <h3>调试输出</h3>
      <div class="console-output">
        <div v-for="(log, index) in debugLogs" :key="index" :class="['log-item', log.type]">
          <span class="timestamp">{{ log.timestamp }}</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';
import * as echarts from 'echarts';

defineOptions({
  name: 'bijie-map-debug'
});

const refreshKey = ref(0);
const debugLogs = ref<Array<{type: string, message: string, timestamp: string}>>([]);

// 测试数据
const chinaTestData = [
  { name: '北京', lng: 116.46, lat: 39.92, value: 150 },
  { name: '上海', lng: 121.48, lat: 31.22, value: 200 }
];

const bijieTestData = [
  { name: '七星关区', lng: 105.284852, lat: 27.302085, value: 100 },
  { name: '大方县', lng: 105.608847, lat: 27.143102, value: 80 },
  { name: '黔西县', lng: 106.038021, lat: 27.026627, value: 90 },
  { name: '金沙县', lng: 106.220663, lat: 27.462603, value: 70 }
];

// 地图状态检查
const chinaStatus = computed(() => {
  const mapData = echarts.getMap('china');
  return {
    registered: !!mapData,
    features: mapData?.geoJson?.features?.length || 0
  };
});

const bijieStatus = computed(() => {
  const mapData = echarts.getMap('bijie');
  return {
    registered: !!mapData,
    features: mapData?.geoJson?.features?.length || 0
  };
});

// 地图配置
const chinaOption = computed(() => ({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify(chinaTestData)
  },
  attribute: {
    titleText: 'China地图测试',
    map: 'china',
    roam: true,
    zoom: 1,
    center: [104, 35],
    showMapLabel: true,
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,
    seriesName: '测试数据',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 8,
    scatterColor: '#ff6b6b',
    showScatterLabel: true,
    scatterLabelPosition: 'top',
    showVisualMap: false
  }
}));

const bijieOption = computed(() => ({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify(bijieTestData)
  },
  attribute: {
    titleText: 'Bijie地图测试',
    map: 'bijie',
    roam: true,
    zoom: 1,
    center: [105.5, 27.3], // 毕节市中心坐标
    showMapLabel: true,
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,
    seriesName: '测试数据',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 8,
    scatterColor: '#ff6b6b',
    showScatterLabel: true,
    scatterLabelPosition: 'top',
    showVisualMap: false
  }
}));

const addLog = (type: string, message: string) => {
  debugLogs.value.push({
    type,
    message,
    timestamp: new Date().toLocaleTimeString()
  });
};

const refreshMaps = () => {
  refreshKey.value++;
  addLog('info', '地图已刷新');
};

const testBijieData = () => {
  addLog('info', '开始测试毕节数据...');
  
  // 检查地图注册
  const mapData = echarts.getMap('bijie');
  if (!mapData) {
    addLog('error', 'Bijie地图未注册');
    return;
  }
  
  addLog('success', `Bijie地图已注册，包含 ${mapData.geoJson?.features?.length} 个特征`);
  
  // 检查坐标范围
  bijieTestData.forEach(point => {
    if (point.lng < 104 || point.lng > 107 || point.lat < 26 || point.lat > 28) {
      addLog('warning', `坐标可能超出毕节范围: ${point.name} (${point.lng}, ${point.lat})`);
    } else {
      addLog('success', `坐标正常: ${point.name} (${point.lng}, ${point.lat})`);
    }
  });
};

const showMapDetails = () => {
  addLog('info', '=== 地图详情 ===');
  
  const chinaMap = echarts.getMap('china');
  const bijieMap = echarts.getMap('bijie');
  
  if (chinaMap) {
    addLog('info', `China地图: ${chinaMap.geoJson?.features?.length} 个省份/地区`);
  }
  
  if (bijieMap) {
    addLog('info', `Bijie地图: ${bijieMap.geoJson?.features?.length} 个区县`);
    
    // 显示毕节的区县名称
    const features = bijieMap.geoJson?.features || [];
    const districts = features.map((f: any) => f.properties?.name).filter(Boolean);
    addLog('info', `毕节区县: ${districts.join(', ')}`);
  }
};

onMounted(() => {
  addLog('info', '页面加载完成');
  testBijieData();
  showMapDetails();
});
</script>

<style scoped>
.debug-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.debug-info {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.status-grid {
  display: grid;
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.label {
  font-weight: bold;
  min-width: 100px;
}

.status.success {
  color: #52c41a;
}

.status.error {
  color: #ff4d4f;
}

.details {
  color: #666;
  font-size: 12px;
}

.map-comparison {
  display: flex;
  gap: 20px;
  margin: 20px 0;
}

.map-container {
  flex: 1;
}

.chart-wrapper {
  width: 400px;
  height: 300px;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: #fff;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.data-comparison {
  display: flex;
  gap: 20px;
  margin: 20px 0;
}

.data-item {
  flex: 1;
}

.data-item pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.console-output {
  background: #000;
  color: #fff;
  padding: 15px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  margin: 2px 0;
  display: flex;
  gap: 10px;
}

.log-item.error {
  color: #ff6b6b;
}

.log-item.success {
  color: #51cf66;
}

.log-item.warning {
  color: #ffd43b;
}

.log-item.info {
  color: #74c0fc;
}

.timestamp {
  color: #adb5bd;
  min-width: 80px;
}

h2, h3, h4 {
  color: #333;
}
</style>
