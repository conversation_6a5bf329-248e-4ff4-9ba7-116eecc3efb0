<template>
  <div class="profile-base-info-continer">
    <el-button v-if="user.phonenumber == '18885119457'" type="primary" style="position: absolute; right: 30px">数据迁移</el-button>
    <div class="flex-row">
      <div class="title" style="height: 70px">头像</div>
      <div style="height: 70px; position: relative">
        <userAvatar v-show="false" ref="userRef" :user="user" />
        <user-img
          v-if="avatar"
          class="img"
          :authSrc="avatarUrl"
          :width="'60px'"
          :height="'60px'"
          :radios="'50%'"
          style="margin: 0; cursor: pointer"
          @click="handleUserAvatar"
        />
        <img v-else :src="defaultAvatar" class="user-avatar" @click="handleUserAvatar" />
      </div>
    </div>
    <div class="flex-row">
      <div class="title">姓名</div>
      <div v-if="user.nickName" class="content">{{ user.custName }}</div>
    </div>
    <div class="flex-row">
      <div class="title">手机号</div>
      <div v-if="user.phonenumber" class="content">{{ user.phonenumber }}</div>
    </div>
    <div class="flex-row">
      <div class="title">密码</div>
      <div class="flex items-center">
        <el-button type="primary" size="small" @click="handleEdit">修改</el-button>
      </div>
    </div>
    <div class="flex-row">
      <div class="title">所属部门</div>
      <div v-if="user.deptNames" class="content">{{ user.deptNames }}</div>
    </div>
    <div class="flex-row">
      <div class="title">所属角色</div>
      <div v-if="roleGroup" class="content">{{ roleGroup }}</div>
    </div>
    <div v-if="isSystemPower" class="flex-row">
      <div class="title">公司位置</div>
      <div class="content">
        <selectAreaCode ref="selectAreaCodeRef" @submitCode="submitCode" />
      </div>
    </div>
    <el-button v-if="user.phonenumber == '18285070490'" type="primary" size="small" @click="textDown">测试下载</el-button>
    <div class="flex-row">
      <div class="title">个人签名</div>
      <div class="content-signature">
        <div class="signature-btn">
          <el-button type="primary" size="small" @click="handleShowEsgin">
            {{ signImg && signImg !== '' ? '编辑' : '新建' }}
          </el-button>
        </div>
        <div style="border: 1px solid #ccc; width: 106px; height: 80px; border-radius: 4px; cursor: pointer">
          <el-image
            v-if="signImg"
            :src="signImg"
            style="width: 100px; height: 70px; object-fit: fill"
            :preview-src-list="bigSignImg"
            :initial-index="0"
          />
        </div>
      </div>
    </div>
    <div v-if="user.phonenumber == '18285070490'" style="display: flex">
      <el-input v-model="moduleId" placeholder="请输入moduleId" type="number" style="width: 236px; margin-right: 16px" />
      <el-button type="primary" @click="handleLG">兰刚数据</el-button>
    </div>

    <!-- 修改密码的弹框 -->
    <el-dialog v-model="editPasssWordVisible" :title="isForgetPassword ? '忘记密码' : '修改密码'" :close-on-click-modal="false">
      <!-- 使用原密码修改 -->
      <el-form
        v-if="!isForgetPassword"
        ref="editPassWordFormRef"
        :model="editPassWordForm"
        :rules="editPassWordRules"
        label-width="100px"
        class="demo-editPassWordForm"
      >
        <el-form-item label="原密码" prop="oldpass">
          <el-input v-model="editPassWordForm.oldpass" type="password" autocomplete="off" />
        </el-form-item>
        <el-form-item label="新密码" prop="newpass">
          <el-input v-model="editPassWordForm.newpass" type="password" autocomplete="off" />
        </el-form-item>
        <el-form-item label="确认密码" prop="checkPass">
          <el-input v-model="editPassWordForm.checkPass" type="password" autocomplete="off" />
        </el-form-item>
      </el-form>

      <!-- 使用验证码修改 -->
      <el-form
        v-else
        ref="forgetPassWordFormRef"
        :model="forgetPassWordForm"
        :rules="forgetPassWordRules"
        label-width="100px"
        class="demo-editPassWordForm"
      >
        <el-form-item label="手机号" prop="username">
          <el-input v-model="forgetPassWordForm.username" disabled :placeholder="user.phonenumber" />
        </el-form-item>
        <el-form-item label="验证码" prop="smscode">
          <el-input v-model="forgetPassWordForm.smscode" maxlength="6" placeholder="请输入验证码">
            <template #suffix>
              <el-link :type="showTypeTitle" :disabled="showDisabled" :underline="false" @click="getCode" style="line-height: 44px">{{
                sendCodeText
              }}</el-link>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input v-model="forgetPassWordForm.password" type="password" placeholder="请输入新密码" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="alginPassword">
          <el-input v-model="forgetPassWordForm.alginPassword" type="password" placeholder="请再次输入密码" show-password />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer" style="display: flex; justify-content: space-between">
          <div>
            <el-button v-if="!isForgetPassword" type="primary" link @click="switchToForgetPassword">忘记原密码？</el-button>
          </div>
          <div>
            <el-button @click="handleCanclePassWord">取 消</el-button>
            <el-button type="primary" @click="handleSubmitPassWord">确 定</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 设置签名 -->
    <el-dialog v-model="signatureDialogVisible" title="签名" width="800px" :before-close="handleClose" :close-on-click-modal="false">
      <webSignature ref="signRef" :height="300" />
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" @click="handleClose">取 消</el-button>
          <el-button type="danger" size="small" @click="handleResetSign">重写</el-button>
          <el-button type="primary" size="small" @click="handleSubmitSign">生成签名</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
// 接口声明需要在所有import语句之前
declare global {
  interface Navigator {
    msSaveOrOpenBlob?: (blob: Blob, defaultName?: string) => boolean;
  }
}

import { ref, reactive, computed, watch, onMounted } from 'vue';
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
import type { FormInstance, FormRules } from 'element-plus';
import userAvatar from './userAvatar.vue';
import UserImg from '@/layout/components/UserImg/index.vue';
import { getUserProfile, modifyPwd, getDataMove, updateSign, qjtDeal } from '@/api/system/user';
import { getMessageCode, forgetPassword } from '@/api/login/index';
import defaultAvatar from '@/assets/images/default-avatar.png';
import selectAreaCode from '@/components/selectAreaCode/index.vue';
import webSignature from '@/components/webSignature/index.vue';
import { editCompany, getCompany } from '@/api/control';
import { getToken } from '@/utils/auth';
import { findAsyncFileText } from '@/api/project';
import { AVATAR_PREFIX_URL } from '@/constants/index';

// Store
const store = useUserStore();
const avatar = computed(() => store.avatar);
const roles = computed(() => store.roles);

// 头像URL
const avatarUrl = computed(() => {
  if (avatar.value) {
    // 检查avatar是否已经是完整URL
    if (avatar.value.startsWith('http://') || avatar.value.startsWith('https://')) {
      return avatar.value;
    }
    return `${AVATAR_PREFIX_URL}${avatar.value}?att=1`;
  }
  return '';
});

// Refs
const userRef = ref();
const selectAreaCodeRef = ref();
const signRef = ref();
const editPassWordFormRef = ref<FormInstance>();
const forgetPassWordFormRef = ref<FormInstance>();

// 基础数据
const user = ref<any>({});
const roleGroup = ref<any>({});
const postGroup = ref<any>({});
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/');
const token = ref(getToken());

// 系统管理员
const isSystemPower = ref(false);

// 公司信息
const company = reactive({
  areaCode: 520000000000,
  companyId: null as number | null,
  companyAddress: null as string | null,
  companyArea: null as number | null
});

// 签名相关
const signImg = ref('');
const bigSignImg = ref<string[]>([]);
const signatureDialogVisible = ref(false);

// 修改密码相关
const editPasssWordVisible = ref(false);
const isForgetPassword = ref(false); // 是否是忘记密码模式

// 原密码修改表单
const editPassWordForm = reactive({
  oldpass: '',
  checkPass: '',
  newpass: ''
});

// 忘记密码表单
const forgetPassWordForm = reactive({
  username: '', // 用于存储手机号
  smscode: '', // 验证码
  password: '', // 新密码
  alginPassword: '' // 确认密码
});

// 验证码相关
const time = ref(60);
const sendCodeText = ref('获取验证码');
const showTypeTitle = ref<'primary' | 'default' | 'success' | 'warning' | 'info' | 'danger'>('primary');
const showDisabled = ref(false);

// 设置倒计时
const setTime = (val: number) => {
  if (time.value == 0) {
    sendCodeText.value = '获取验证码';
    time.value = 60;
    showTypeTitle.value = 'primary';
    showDisabled.value = false;
  } else {
    showTypeTitle.value = 'info';
    showDisabled.value = true;
    time.value--;
    sendCodeText.value = `重新发送${time.value}`;
    setTimeout(() => {
      setTime(time.value);
    }, 1000);
  }
};

// 获取验证码
const getCode = () => {
  if (!user.value.phonenumber) {
    ElMessage.error('获取手机号失败');
    return;
  }

  const param = {
    username: user.value.phonenumber
  };

  getMessageCode(param).then((res: any) => {
    if (res.code === 200) {
      setTime(time.value);
      ElMessage.success('验证码发送成功');
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 切换到忘记密码模式
const switchToForgetPassword = () => {
  isForgetPassword.value = true;
  forgetPassWordForm.username = user.value.phonenumber;
};

const validatePass = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入新密码'));
  } else if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/.test(value)) {
    callback(new Error('密码需为8-20位数字和字母组合'));
  } else {
    if (editPassWordForm.checkPass !== '') {
      editPassWordFormRef.value?.validateField('checkPass');
    }
    callback();
  }
};

const validatePass2 = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'));
  } else if (value !== editPassWordForm.newpass) {
    callback(new Error('两次输入密码不一致!'));
  } else {
    callback();
  }
};

const editPassWordRules = reactive<FormRules>({
  newpass: [{ validator: validatePass, trigger: ['blur', 'change'] }],
  checkPass: [{ validator: validatePass2, trigger: ['blur', 'change'] }],
  oldpass: [{ required: true, message: '请输入原密码', trigger: ['blur', 'change'] }]
});

// 下载相关
const currentPercent = ref(0);
let loadingProgess: any = null;

// 测试相关
const moduleId = ref(0);
const videoDialog = ref(false);
const checkedVideoUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/qjt/smvideo/detail/74dc4f0c1f88f16ced81a7657e6ed019`);

// 监视下载进度
watch(
  currentPercent,
  (val) => {
    const percent = val.toFixed(2);
    if (loadingProgess) {
      loadingProgess.setText(`数据下载中${percent}MB`);
    }
  },
  { deep: true }
);

// 生命周期钩子
onMounted(() => {
  if (roles.value.includes('admin')) {
    isSystemPower.value = true;
  }
  getUser();
  getData();
});

// 方法
const handleLG = () => {
  qjtDeal({ moduleId: moduleId.value }).then((res: any) => {
    if (res.code == 200) {
      // 处理成功
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const getData = () => {
  getCompany().then((res: any) => {
    if (res.code == 200) {
      company.companyArea = res.data.companyArea;
      company.companyId = res.data.companyId;
      company.companyAddress = res.data.companyAddress;

      // 只有是管理员的时候才调用选择行政区域的接口
      if (isSystemPower.value && selectAreaCodeRef.value) {
        selectAreaCodeRef.value.init(company.companyArea);
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 获取用户信息
const getUser = () => {
  getUserProfile().then((response: any) => {
    user.value = response.data;
    if (response.data.signature) {
      // 修改签名图片路径和预览图片列表
      signImg.value = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/${response.data.signature}?token=${token.value}`;
      bigSignImg.value = [`${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/${response.data.signature}?token=${token.value}`];
    }
    roleGroup.value = response.roleGroup;
    postGroup.value = response.postGroup;
  });
};

// 修改头像
const handleUserAvatar = () => {
  userRef.value.editCropper();
};

// 修改密码
const handleEdit = () => {
  editPasssWordVisible.value = true;
  isForgetPassword.value = false;
};

// 忘记密码表单验证规则
const validateForgetPass = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入新密码'));
  } else if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/.test(value)) {
    callback(new Error('密码需为8-20位数字和字母组合'));
  } else {
    if (forgetPassWordForm.alginPassword !== '') {
      forgetPassWordFormRef.value?.validateField('alginPassword');
    }
    callback();
  }
};

const validateForgetPass2 = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'));
  } else if (value !== forgetPassWordForm.password) {
    callback(new Error('两次输入密码不一致!'));
  } else {
    callback();
  }
};

const forgetPassWordRules = reactive<FormRules>({
  smscode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  password: [{ validator: validateForgetPass, trigger: ['blur', 'change'] }],
  alginPassword: [{ validator: validateForgetPass2, trigger: ['blur', 'change'] }]
});

// 修改密码的提交按钮
const handleSubmitPassWord = () => {
  if (!isForgetPassword.value) {
    // 使用原密码修改
    editPassWordFormRef.value?.validate((valid) => {
      if (valid) {
        const params = {
          newPassword: editPassWordForm.newpass,
          password: editPassWordForm.oldpass
        };
        modifyPwd(params).then((res: any) => {
          if (res.code == 200) {
            editPasssWordVisible.value = false;
            ElMessage.success(res.msg);
          } else {
            ElMessage.error(res.msg);
            editPasssWordVisible.value = true;
          }
        });
      }
    });
  } else {
    // 使用验证码修改
    forgetPassWordFormRef.value?.validate((valid) => {
      if (valid) {
        forgetPassword(forgetPassWordForm).then((res: any) => {
          if (res.code == 200) {
            editPasssWordVisible.value = false;
            ElMessage.success('密码修改成功');
            handleCanclePassWord();
          } else {
            ElMessage.error(res.msg);
          }
        });
      }
    });
  }
};

// 修改密码的取消按钮
const handleCanclePassWord = () => {
  editPasssWordVisible.value = false;
  isForgetPassword.value = false;
  editPassWordFormRef.value?.resetFields();
  forgetPassWordFormRef.value?.resetFields();
};

const showProcess = (progress: any) => {
  currentPercent.value = progress.loaded / 1024 / 1024;
  const percent = currentPercent.value.toFixed(2);
  loadingProgess = ElLoading.service({
    lock: true,
    text: `数据下载中${percent}MB`,
    spinner: 'el-icon-loading',
    background: 'rgba(255, 255, 255, 0.9)'
  });
};

const textDown = () => {
  ElMessageBox.prompt('请输入文件名', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  })
    .then(({ value }) => {
      const params = {
        fileName: value
      };
      findAsyncFileText(params, showProcess).then((res: any) => {
        if (res.data.type == 'application/json') {
          // 导出异常
          const read = new FileReader();
          read.readAsText(res.data, 'utf-8');
          const that = { $message: ElMessage };
          let bugMsg = '';
          read.onload = (data: any) => {
            bugMsg = JSON.parse(data.currentTarget.result).msg;
            that.$message.error(JSON.parse(data.currentTarget.result).msg);
            if (!bugMsg) {
              bugMsg = '未知异常';
            }
          };
        } else {
          // 导出正常
          const name = decodeURI(res.headers['content-disposition']);
          const index = name.indexOf('=');
          const endFileName = name.substring(index + 1, name.length) || '测试.zip';
          const blob = new Blob([res.data], { type: 'application/zip' });
          // 针对ie浏览器
          if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(blob, '测试.zip');
          } else {
            // 非ie浏览器
            const downloadElement = document.createElement('a');
            const href = window.URL.createObjectURL(blob); // 创建下载的链接
            downloadElement.href = href;
            downloadElement.download = '测试.zip'; // 下载后文件名
            document.body.appendChild(downloadElement);
            downloadElement.click(); // 点击下载
            document.body.removeChild(downloadElement); // 下载完成移除元素
            window.URL.revokeObjectURL(href); // 释放blob对象
          }
          setTimeout(() => {
            if (loadingProgess) {
              loadingProgess.close();
            }
          }, 3000);
        }
      });
    })
    .catch(() => {
      // 用户取消输入
    });
};

// 提交公司位置信息
const submitCode = (code: string, name: string) => {
  company.companyAddress = name;
  company.companyArea = parseInt(code);
  editCompany(company).then((res: any) => {
    if (res.code == 200) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 个人签名相关
const handleShowEsgin = () => {
  const text = '是否重写签名？';
  if (signImg.value == '' || signImg.value == undefined) {
    signatureDialogVisible.value = true;
  } else {
    ElMessageBox.confirm(text, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        signatureDialogVisible.value = true;
      })
      .catch(() => {
        signatureDialogVisible.value = false;
      });
  }
};

// 深层签名的图片为透明背景，格式为jpg
const handleSubmitSign = () => {
  signRef.value.handleGenerate().then((path: string) => {
    const data = {
      signature: path,
      userId: user.value.userId
    };
    updateSign(data).then((res: any) => {
      if (res.code == 200) {
        ElMessage.success('签名生成成功');
        signatureDialogVisible.value = false;
        // 统一使用相同的图片URL格式
        const imgUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/${path}?token=${token.value}`;
        signImg.value = imgUrl;
        bigSignImg.value = [imgUrl]; // 直接赋值新数组，而不是push
      } else {
        ElMessage.error(res.msg);
      }
    });
  });
};

// 生成重写
const handleResetSign = () => {
  signRef.value.handleReset();
};

const handleClose = () => {
  signatureDialogVisible.value = false;
  signRef.value.handleReset();
};
</script>

<style lang="scss" scoped>
.profile-base-info-continer {
  .flex-row {
    display: flex;
    justify-content: flex-start;
    align-content: center;
    width: 100%;
    .title {
      width: 10%;
      height: 40px;
      font-size: 14px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #161d26;
      line-height: 40px;
    }
    .content {
      width: 90%;
      height: 40px;
      font-size: 14px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      color: #161d26;
      line-height: 40px;
    }
    .content-signature {
      width: 90%;
      height: 370px;
      font-size: 14px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      color: #161d26;
      line-height: 40px;
    }
    .user-avatar {
      cursor: pointer;
      width: 60px;
      height: 60px;
      border-radius: 50%;
    }
  }
}
.flow-box {
  width: 100%;
  height: 500px;
  overflow: auto;
  background: #f0f2f5;
}
</style>
