<!-- 区域颜色调试页面 -->
<template>
  <div class="debug-container">
    <h2>区域颜色调试</h2>
    
    <div class="controls">
      <el-button @click="testRegionColors">测试区域颜色</el-button>
      <el-button @click="showDebugInfo">显示调试信息</el-button>
      <el-button @click="clearColors">清除颜色</el-button>
    </div>
    
    <div class="chart-wrapper">
      <cpt-chart-geo-scatter 
        :width="800" 
        :height="600" 
        :option="testOption"
        :key="chartKey"
      />
    </div>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <div class="info-item">
        <h4>当前地图: {{ currentMap }}</h4>
        <p>可用区域: {{ availableRegions.join(', ') }}</p>
      </div>
      
      <div class="info-item">
        <h4>区域颜色配置:</h4>
        <pre>{{ JSON.stringify(regionColors, null, 2) }}</pre>
      </div>
      
      <div class="info-item">
        <h4>完整配置:</h4>
        <pre>{{ JSON.stringify(testOption.attribute, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="console-output">
      <h3>控制台输出</h3>
      <div class="logs">
        <div v-for="(log, index) in logs" :key="index" :class="['log', log.type]">
          <span class="time">{{ log.time }}</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';
import * as echarts from 'echarts';

defineOptions({
  name: 'region-color-debug'
});

const currentMap = ref('bijie');
const chartKey = ref(0);
const logs = ref<Array<{type: string, message: string, time: string}>>([]);

const regionColors = ref({
  '七星关区': '#FF6B6B',
  '大方县': '#4ECDC4',
  '黔西市': '#45B7D1',
  '金沙县': '#96CEB4',
  '织金县': '#FFEAA7',
  '纳雍县': '#DDA0DD',
  '威宁县': '#98D8C8',
  '赫章县': '#F7DC6F'
});

const availableRegions = computed(() => {
  const mapData = echarts.getMap(currentMap.value);
  if (!mapData?.geoJson?.features) return [];
  
  return mapData.geoJson.features
    .map((feature: any) => feature.properties?.name)
    .filter(Boolean);
});

const testOption = computed(() => ({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify([
      { name: '七星关区', lng: 105.284852, lat: 27.302085, value: 100 },
      { name: '大方县', lng: 105.609254, lat: 27.143521, value: 80 }
    ])
  },
  attribute: {
    titleText: '毕节市区域颜色测试',
    map: currentMap.value,
    roam: true,
    zoom: 4,
    center: [105.5, 27.0],
    
    showMapLabel: true,
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,
    
    showMapEmphasisLabel: true,
    mapEmphasisLabelColor: '#000',
    mapEmphasisAreaColor: '#e0e0e0',
    mapEmphasisBorderColor: '#666',
    
    seriesName: '测试数据',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 10,
    scatterColor: '#ff6b6b',
    showScatterLabel: true,
    
    // 区域颜色配置 - 关键测试点
    regionColors: regionColors.value,
    regionValues: {}
  }
}));

const addLog = (type: string, message: string) => {
  logs.value.push({
    type,
    message,
    time: new Date().toLocaleTimeString()
  });
  console.log(`[${type.toUpperCase()}] ${message}`);
};

const testRegionColors = () => {
  addLog('info', '开始测试区域颜色...');
  
  // 检查地图是否注册
  const mapData = echarts.getMap(currentMap.value);
  if (!mapData) {
    addLog('error', `地图 ${currentMap.value} 未注册`);
    return;
  }
  
  addLog('success', `地图已注册，包含 ${mapData.geoJson?.features?.length} 个区域`);
  
  // 检查区域名称
  const regions = availableRegions.value;
  addLog('info', `可用区域: ${regions.join(', ')}`);
  
  // 检查颜色配置
  const configuredRegions = Object.keys(regionColors.value);
  addLog('info', `已配置颜色的区域: ${configuredRegions.join(', ')}`);
  
  // 检查匹配情况
  const matchedRegions = configuredRegions.filter(region => regions.includes(region));
  const unmatchedRegions = configuredRegions.filter(region => !regions.includes(region));
  
  addLog('success', `匹配的区域: ${matchedRegions.join(', ')}`);
  if (unmatchedRegions.length > 0) {
    addLog('warning', `未匹配的区域: ${unmatchedRegions.join(', ')}`);
  }
  
  // 强制重新渲染
  chartKey.value++;
  addLog('info', '图表已重新渲染');
};

const showDebugInfo = () => {
  addLog('info', '=== 调试信息 ===');
  addLog('info', `当前地图: ${currentMap.value}`);
  addLog('info', `区域颜色配置: ${JSON.stringify(regionColors.value)}`);
  
  // 检查ECharts地图数据
  const mapData = echarts.getMap(currentMap.value);
  if (mapData) {
    const features = mapData.geoJson?.features || [];
    features.forEach((feature: any, index: number) => {
      const name = feature.properties?.name;
      const color = regionColors.value[name];
      addLog('info', `区域 ${index + 1}: ${name} - 颜色: ${color || '默认'}`);
    });
  }
};

const clearColors = () => {
  regionColors.value = {};
  chartKey.value++;
  addLog('info', '已清除所有区域颜色');
};

// 监听控制台输出
const originalConsoleLog = console.log;
console.log = (...args: any[]) => {
  originalConsoleLog(...args);
  if (args[0] && typeof args[0] === 'string') {
    if (args[0].includes('地图区域数据') || args[0].includes('区域颜色配置')) {
      addLog('console', args.join(' '));
    }
  }
};

onMounted(() => {
  addLog('info', '页面加载完成');
  testRegionColors();
});

onUnmounted(() => {
  console.log = originalConsoleLog;
});
</script>

<style scoped>
.debug-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.debug-info {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.info-item {
  margin: 15px 0;
  padding: 10px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.info-item h4 {
  margin-top: 0;
  color: #333;
}

.info-item pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.console-output {
  background: #f0f0f0;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.logs {
  background: #000;
  color: #fff;
  padding: 15px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log {
  margin: 2px 0;
  display: flex;
  gap: 10px;
}

.log.error { color: #ff6b6b; }
.log.success { color: #51cf66; }
.log.info { color: #74c0fc; }
.log.warning { color: #ffd43b; }
.log.console { color: #adb5bd; }

.time {
  color: #adb5bd;
  min-width: 80px;
}

h2, h3 {
  color: #333;
}
</style>
