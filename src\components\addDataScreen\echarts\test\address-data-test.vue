<!-- 地址数据测试页面 -->
<template>
  <div class="test-container">
    <h2>地址数据散点图测试</h2>

    <div class="controls">
      <el-button @click="loadAddressData">加载地址数据</el-button>
      <el-button @click="loadMixedData">加载混合数据</el-button>
      <el-button @click="clearData">清空数据</el-button>
      <el-button @click="refreshData">刷新数据</el-button>
    </div>

    <div class="chart-wrapper">
      <cpt-chart-geo-scatter :width="800" :height="600" :option="chartOption" :key="chartKey" ref="chartRef" />
    </div>

    <div class="data-info">
      <h3>当前数据 ({{ currentData.length }} 条)</h3>
      <div class="data-preview">
        <div v-for="(item, index) in currentData.slice(0, 3)" :key="index" class="data-item">
          <h4>数据 {{ index + 1 }}</h4>
          <pre>{{ JSON.stringify(item, null, 2) }}</pre>
        </div>
        <div v-if="currentData.length > 3" class="more-data">... 还有 {{ currentData.length - 3 }} 条数据</div>
      </div>
    </div>

    <div class="instructions">
      <h3>测试说明</h3>
      <ul>
        <li><strong>加载地址数据</strong>：使用您提供的地址格式数据</li>
        <li><strong>加载混合数据</strong>：包含地址数据和普通坐标数据</li>
        <li><strong>清空数据</strong>：清除所有散点数据</li>
        <li><strong>刷新数据</strong>：调用组件的刷新方法</li>
      </ul>
      <p><strong>注意</strong>：地址数据会自动转换为坐标，并在tooltip中显示详细信息。</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChartGeoScatter from '../cpt-chart-geo-scatter.vue';

defineOptions({
  name: 'address-data-test'
});

const chartKey = ref(0);
const chartRef = ref();
const currentData = ref<any[]>([]);

// 您提供的地址数据
const addressData = [
  {
    'address': '蜂菠山',
    'town': '东关乡',
    'nation': '中国',
    'city': '毕节市',
    'county_code': '156520521',
    'poi_position': '东北',
    'county': '大方县',
    'city_code': '156520500',
    'address_position': '东北',
    'poi': '蜂菠山',
    'province_code': '156520000',
    'town_code': '156520521200',
    'province': '贵州省',
    'road': 'G321',
    'road_distance': '448',
    'address_distance': '338',
    'poi_distance': '338',
    'detail': '贵州省毕节市大方县竹园彝族苗族乡蜂菠山东北约338米'
  },
  {
    'address': '滥田坝',
    'town': '东关乡',
    'nation': '中国',
    'city': '毕节市',
    'county_code': '156520521',
    'poi_position': '西北',
    'county': '大方县',
    'city_code': '156520500',
    'address_position': '西北',
    'poi': '滥田坝',
    'province_code': '156520000',
    'town_code': '156520521200',
    'province': '贵州省',
    'road': 'G321',
    'road_distance': '289',
    'address_distance': '241',
    'poi_distance': '241',
    'detail': '贵州省毕节市大方县东关乡滥田坝西北约241米'
  },
  {
    'address': '滥田坝',
    'town': '东关乡',
    'nation': '中国',
    'city': '毕节市',
    'county_code': '156520521',
    'poi_position': '西南',
    'county': '大方县',
    'city_code': '156520500',
    'address_position': '西南',
    'poi': '滥田坝',
    'province_code': '156520000',
    'town_code': '156520521200',
    'province': '贵州省',
    'road': 'G321',
    'road_distance': '214',
    'address_distance': '194',
    'poi_distance': '194',
    'detail': '贵州省毕节市大方县东关乡滥田坝西南约194米'
  },
  {
    'address': '滥田坝',
    'town': '东关乡',
    'nation': '中国',
    'city': '毕节市',
    'county_code': '156520521',
    'poi_position': '西南',
    'county': '大方县',
    'city_code': '156520500',
    'address_position': '西南',
    'poi': '滥田坝',
    'province_code': '156520000',
    'town_code': '156520521200',
    'province': '贵州省',
    'road': 'G321',
    'road_distance': '81',
    'address_distance': '478',
    'poi_distance': '478',
    'detail': '贵州省毕节市大方县东关乡滥田坝西南约478米'
  },
  {
    'address': '滥田坝',
    'town': '东关乡',
    'nation': '中国',
    'city': '毕节市',
    'county_code': '156520521',
    'poi_position': '西北',
    'county': '大方县',
    'city_code': '156520500',
    'address_position': '西北',
    'poi': '滥田坝',
    'province_code': '156520000',
    'town_code': '156520521200',
    'province': '贵州省',
    'road': 'G321',
    'road_distance': '394',
    'address_distance': '339',
    'poi_distance': '339',
    'detail': '贵州省毕节市大方县东关乡滥田坝西北约339米'
  }
];

const chartOption = ref({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify([])
  },
  attribute: {
    titleText: '地址数据散点图测试',
    subtext: '自动地理编码',
    titleLeft: 'center',
    titleTop: 10,
    titleFontSize: 18,
    titleColor: '#333',
    subTitleColor: '#666',
    subTitleFontSize: 12,

    map: 'bijie',
    roam: true,
    zoom: 4,
    center: [105.5, 27.0],

    showMapLabel: true,
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0',
    mapBorderColor: '#999',
    mapBorderWidth: 1,

    seriesName: '地址数据',
    scatterType: 'scatter',
    scatterSymbol: 'circle',
    scatterBaseSize: 12,
    scatterSizeMultiplier: 0.1,
    scatterColor: '#ff6b6b',
    scatterOpacity: 0.8,
    scatterBorderWidth: 2,
    scatterBorderColor: '#fff',

    showScatterLabel: true,
    scatterLabelPosition: 'top',
    scatterLabelColor: '#333',
    scatterLabelSize: 10,
    scatterLabelFormat: 'name',

    enableStatusColors: false,
    regionColors: {},
    regionValues: {}
  }
});

const loadAddressData = () => {
  currentData.value = [...addressData];
  chartOption.value.cptDataForm.dataText = JSON.stringify(addressData);
  chartKey.value++;
  console.log('加载地址数据:', addressData.length, '条');
};

const loadMixedData = () => {
  const mixedData = [
    ...addressData,
    { name: '贵阳市', lng: 106.713478, lat: 26.578343, value: 100 },
    { name: '遵义市', lng: 107.297104, lat: 27.725654, value: 150 }
  ];

  currentData.value = [...mixedData];
  chartOption.value.cptDataForm.dataText = JSON.stringify(mixedData);
  chartKey.value++;
  console.log('加载混合数据:', mixedData.length, '条');
};

const clearData = () => {
  currentData.value = [];
  chartOption.value.cptDataForm.dataText = JSON.stringify([]);
  chartKey.value++;
  console.log('清空数据');
};

const refreshData = () => {
  if (chartRef.value && chartRef.value.refreshCptData) {
    chartRef.value.refreshCptData();
    console.log('调用组件刷新方法');
  } else {
    console.warn('组件刷新方法不可用');
  }
};

onMounted(() => {
  console.log('地址数据测试页面加载完成');
  // 默认加载地址数据
  loadAddressData();
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.data-info {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.data-preview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.data-item {
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.data-item h4 {
  margin-top: 0;
  color: #333;
}

.data-item pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 11px;
  max-height: 200px;
  overflow-y: auto;
}

.more-data {
  background: #e6f7ff;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  color: #1890ff;
  font-weight: 500;
}

.instructions {
  background: #f6ffed;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #52c41a;
}

.instructions h3 {
  margin-top: 0;
  color: #52c41a;
}

.instructions ul {
  margin: 15px 0;
}

.instructions li {
  margin: 8px 0;
}

h2,
h3 {
  color: #333;
}
</style>
