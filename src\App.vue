<template>
  <el-config-provider :locale="appStore.locale" :size="appStore.size">
    <router-view />
  </el-config-provider>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/store/modules/settings';
import { handleThemeStyle } from '@/utils/theme';
import { useAppStore } from '@/store/modules/app';
import { useRouter } from 'vue-router';

const appStore = useAppStore();
const router = useRouter();

onMounted(() => {
  // 在这添加是否切换公司的标识。
  // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
  // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
  const isCompany = JSON.parse(sessionStorage.getItem('qiehuan_company') || 'false');

  if (isCompany && router.currentRoute.value.path !== '/index') {
    router.replace('/index');
    sessionStorage.removeItem('qiehuan_company');
  }
  // 禁用右键菜单
  document.oncontextmenu = function () {
    return false;
  };

  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);
  });
});

onBeforeUnmount(() => {
  sessionStorage.removeItem('qiehuan_company');
});
</script>
