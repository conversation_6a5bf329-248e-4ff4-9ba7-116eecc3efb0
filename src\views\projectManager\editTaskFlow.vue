<!-- 流程任务 -->
<template>
  <container-card>
    <div class="handle-title">
      <div class="item" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>返回
      </div>
      <div class="center">{{ title }}</div>
      <el-button type="primary" @click="handleSubmitAddTask">保存</el-button>
    </div>
    <div class="group-item">
      <div class="group-title" @click="showTaskBasics = !showTaskBasics">
        <div class="title">任务基本信息</div>
        <div class="icon">
          <i class="el-icon-arrow-up" v-show="showTaskBasics"></i>
          <i class="el-icon-arrow-down" v-show="!showTaskBasics"></i>
        </div>
      </div>
      <div class="group-content" v-show="showTaskBasics">
        <el-form :model="taskBasics" :rules="taskBasicsRules" ref="taskBasicsRef" label-width="100px" class="demo-ruleForm">
          <div class="flex-content">
            <div class="flex-item">
              <el-form-item label="业务模块" prop="moduleId">
                <el-select
                  v-model="taskBasics.moduleId"
                  :disabled="title == '修改流程任务'"
                  filterable
                  clearable
                  placeholder="请选择业务模块"
                  style="width: 100%"
                  @change="handleChangeModuleTemplate"
                >
                  <el-option v-for="item in appTypeOptions" :key="item.id" :label="getAddTaskLable(item)" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="任务名称" prop="name">
                <el-input v-model="taskBasics.name" placeholder="请输入任务名称" maxlength="30" />
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="任务权限" prop="type">
                <el-select
                  v-model="taskBasics.type"
                  @focus="handleFocusTaskOptType"
                  :disabled="title == '修改流程任务'"
                  placeholder="请选择任务权限"
                  style="width: 100%"
                >
                  <el-option v-for="item in taskOptTypeComputedOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <!-- 任务数据 -->
            <div class="flex-item" v-if="taskBasics.type == 2">
              <el-form-item label="任务数据" prop="parcelNames">
                <el-input v-model="taskBasics.parcelNames" multiple placeholder="请选择任务数据" @focus="handleOpenZDManagementDialog"></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="预计工作量" :rules="[{ required: true, message: '请输入预计工作量', trigger: 'blur' }]">
                <el-input v-model="taskBasics.workload" placeholder="请输入预计工作量" maxlength="100" type="number"></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="任务工期" :rules="[{ required: true, message: '请输入任务工期', trigger: 'blur' }]">
                <el-date-picker
                  v-model="taskBasics.dateRange"
                  type="daterange"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :shortcuts="dateShortcuts"
                  value-format="x"
                  style="width: 100%"
                  @change="handleDateRank"
                />
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="项目负责人" prop="chargeUserId">
                <el-select v-model="taskBasics.chargeUserId" filterable clearable remote placeholder="请选择项目负责人" style="width: 100%">
                  <el-option v-for="item in taskReceiversOptions" :key="item.userId" :label="item.custName" :value="item.userId"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="任务描述" prop="descs">
                <el-input v-model="taskBasics.descs" type="textarea" placeholder="请输入任务描述" maxlength="100"></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="任务备注">
                <el-input v-model="taskBasics.remark" type="textarea" placeholder="请输入任务备注" maxlength="100"></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="默认模板">
                <el-select
                  v-model="taskBasics.templateId"
                  filterable
                  clearable
                  remote
                  placeholder="请选择默认模板"
                  style="width: 100%"
                  @change="handleChangeStepList"
                >
                  <el-option v-for="item in taskList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="大屏分组字段">
                <el-input v-model="taskBasics.groupName" type="textarea" placeholder="请输入乡镇名称" maxlength="100"></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="应采数据量">
                <el-input v-model="taskBasics.area" placeholder="请输入应采集面积（㎡）" maxlength="100" type="number">
                  <template #append> ㎡ </template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <div class="group-item">
      <div class="group-title" @click="showStep = !showStep">
        <div class="title">流程步骤</div>
        <div class="icon">
          <i class="el-icon-arrow-up" v-show="showStep"></i>
          <i class="el-icon-arrow-down" v-show="!showStep"></i>
        </div>
      </div>
      <div class="group-content" v-show="showStep">
        <div class="flow-box" @click="closeHandle">
          <el-input-number
            size="default"
            v-model="scaleValue"
            class="scaled-btn"
            :step="10"
            :min="10"
            :max="150"
            @change="changeScale"
          ></el-input-number>
          <div class="flow-main-content" ref="flowBoxRef">
            <taskFlow
              v-for="(item, index) in flowList"
              :key="index"
              :item="item"
              :index="index"
              :isChild="false"
              :displayLeft="false"
              :displayRight="false"
              @changeShowHandleP="changeShowHandleP"
              @addNode="addNode"
              @delOneStep="delOneStep"
              @closeFlowList="handleCloseFlowList"
              @updateItem="updateItem"
              :flowList="flowList"
              :moduleId="taskBasics.moduleId"
            ></taskFlow>
            <div class="end-div">
              <div class="flow-end">流程结束</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 筛选数据 -->
    <searchData
      :searchDialog="searchDialog"
      @closeSearchDialog="closeSearchDialog"
      @getChooseData="getChooseData"
      :zdList="selectedList"
      :moduleIdPop="String(taskBasics.moduleId)"
      :isManager="false"
      :taskId="String(taskBasics.id || '')"
      :taskName="taskBasics.name"
    ></searchData>
  </container-card>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onActivated } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getModuleList, getPrarcels } from '@/api/modal';
import { listUser } from '@/api/system/user';
import taskFlow from '@/components/taskFlow/index.vue';
import { addTask, getTaskDetail as fetchTaskDetail, getSearchTask } from '@/api/task';
import searchData from '@/components/taskDataSearch/index.vue';

// 类型定义
interface TaskBasics {
  id: string;
  moduleId: string;
  dateRange: string[];
  startTime: number;
  endTime: number;
  workload: string;
  name: string;
  type: string | number;
  chargeUserId: string | number;
  descs: string;
  remark: string;
  scope: number;
  flowType: number;
  templateId?: string | number;
  parcelNames?: string;
  parcelIds?: (string | number)[];
  taskParcelModels?: any[];
  timeTamp?: string;
  area: number;
  groupName: string;
}

interface FlowItem {
  typeName: string;
  stepLevel: number;
  users: any[];
  receiverNames: string;
  stepType: number;
  changeTitle: boolean;
  treeNodes: any[];
  list: FlowItem[];
  charges: any[];
  chargesName: string;
  isShowHandle: boolean;
  ruleId: string;
  id?: string;
  isChild?: boolean;
  parentKey?: string;
  processName?: string;
  processId?: string;
  delFlag?: number;
  chargesAttribution?: {
    list: any[];
  };
}

interface DateShortcut {
  text: string;
  value: () => Date[];
}

// 响应式状态
const router = useRouter();
const route = useRoute();
const title = ref('新增流程任务');
const showTaskBasics = ref(true);
const showStep = ref(true);
const appTypeOptions = ref<any[]>([]);
const taskList = ref<any[]>([]);
const flowList = ref<FlowItem[]>([
  {
    typeName: '步骤1',
    stepLevel: 1,
    users: [],
    receiverNames: '',
    stepType: 1, //流程类型 1步骤 2审批 3分支
    changeTitle: false,
    treeNodes: [], //选择的节点及属性组和属性
    list: [], //分支
    charges: [], //负责人
    chargesName: '',
    isShowHandle: false,
    ruleId: (Math.floor(100000 * Math.random()) + new Date().getTime()).toString(32).slice(0, 8), //用于删除处理
    chargesAttribution: { list: [] }
  }
]);
const scaleValue = ref(100);
const submitFlg = ref(true);
const searchDialog = ref(false);
const selectedList = ref<any[]>([]);
const taskBasicsRef = ref();
const taskReceiversOptions = ref<any[]>([]);

const taskBasics = reactive<TaskBasics>({
  id: '',
  moduleId: '',
  dateRange: [],
  startTime: 0,
  endTime: 0,
  workload: '',
  name: '',
  type: '',
  chargeUserId: '',
  descs: '',
  remark: '',
  scope: 0,
  flowType: 1,
  area: '',
  groupName: ''
});

// 表单校验规则
const taskBasicsRules = {
  moduleId: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (value === '' || value == 0) {
          callback(new Error('请选择业务模块'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择任务权限', trigger: 'change' }],
  remark: [{ required: true, message: '请输入任务备注', trigger: 'blur' }],
  descs: [{ required: true, message: '请输入任务描述', trigger: 'blur' }],
  workload: [{ required: true, message: '请输入工作量', trigger: 'blur' }],
  dateRange: [{ required: true, message: '请选择任务工期', trigger: 'blur' }],
  chargeUserId: [{ required: true, message: '请选择项目负责人', trigger: 'blur' }],
  scope: [{ required: true, message: '请选择任务类型', trigger: 'change' }]
};

// 任务权限选项
const taskOptTypeComputedOptions = [
  { label: '新增', value: 1 },
  { label: '修改', value: 2 }
];

// 任务权限焦点处理
const handleFocusTaskOptType = () => {
  if (!taskBasics.moduleId) {
    taskBasicsRef.value?.validateField('moduleId');
    return false;
  }
};

// 打开选择宗地的弹框
const handleOpenZDManagementDialog = () => {
  if (!taskBasics.moduleId) {
    taskBasicsRef.value?.validateField('moduleId');
    return false;
  } else {
    searchDialog.value = true;
  }
};

/**
 * 任务量校验
 * @param val 值
 */
const handleInput = (val: string) => {
  if (val) {
    taskBasics.workload = val.replace(/^0+(\d)/, '$1').replace(/[^\d]/g, '');
  }
};
/**
 * 处理选中的事件
 * @param val 值
 */
const handleDateRank = (val: any) => {
  if (val) {
    taskBasics.startTime = Number(val[0]);
    taskBasics.endTime = Number(val[1]);
  }
};

/**
 * 改变流程操作框
 * @param item 流程步骤
 */
const changeShowHandleP = (item: FlowItem) => {
  setCloseHandle(flowList.value);
  item.isShowHandle = true;
};

// 关闭操作框
const closeHandle = () => {
  setCloseHandle(flowList.value);
};

/**
 * 设置关闭操作框
 * @param list 流程步骤
 */
const setCloseHandle = (list: FlowItem[]) => {
  list.forEach((item) => {
    item.isShowHandle = false;
    if (item.list && item.list.length > 0) {
      setCloseHandle(item.list);
    }
  });
};

/**
 * 删除指定的数据
 * @param item 流程步骤
 */
const delOneStep = (item: FlowItem) => {
  let key: string | null = null;
  let childKey: string | null = null; //该值是分支的时候用 当分支数据大于两个时，是可以删除一个的

  if (item.isChild) {
    //表示是删除分支的每条节点内容
    // 删除分支的某条节点内容的话需要增加判断 如果分支数据只有两个分支内容，删除分支的时候就会把分支直接删除
    key = item.parentKey || null;
    childKey = item.id ? item.id : item.ruleId;
  } else {
    //删除正常内容
    if (item.id) {
      key = item.id;
    } else {
      key = item.ruleId;
    }
  }
  iterationDelStep(flowList.value, key, childKey);
};

/**
 * 迭代删除步骤
 * @param list 流程步骤
 * @param key 键
 * @param childKey 子键
 */
const iterationDelStep = (list: FlowItem[], key: string | null, childKey: string | null) => {
  for (let i = 0; i < list.length; i++) {
    if ((list[i].id && list[i].id == key) || (list[i].ruleId && list[i].ruleId == key)) {
      // 找到对应的步骤了 但是有可能是常规步骤或者分支步骤
      if (list[i].stepType == 3) {
        //分支
        // 如果是分支 需要先判断该分支是否只有两条内容 如果只有两条内容就需要把分支删除 如果是大于两条 则通过childKey删除分支下对应的内容
        if (list[i].list.length > 2) {
          for (let j = 0; j < list[i].list.length; j++) {
            if ((list[i].list[j].id && list[i].list[j].id == childKey) || (list[i].list[j].ruleId && list[i].list[j].ruleId == childKey)) {
              if (list[i].list[j].id) {
                list[i].list[j].delFlag = 1;
              } else {
                list[i].list[j].delFlag = 1;
              }
              handleDelStepInUserAndCharge(list, i, j);
              break;
            }
          }
        } else {
          //直接删除整个分支
          if (list[i].id) {
            list[i].delFlag = 1;
          } else {
            list.splice(i, 1);
          }
          handleDelStepInUserAndCharge(list, i);
          break;
        }
      } else {
        if (list[i].id) {
          list[i].delFlag = 1;
        } else {
          list.splice(i, 1);
        }
        handleDelStepInUserAndCharge(list, i);
        break;
      }
    } else if (list[i].list && list[i].list.length != 0) {
      iterationDelStep(list[i].list, key, childKey);
    }
  }
};

/**
 * 中间增加的步骤 需要把后面的步骤等级+1
 * @param list 流程步骤
 */
const editStepLevel = (list: FlowItem[]) => {
  list.forEach((v) => {
    v.stepLevel = v.stepLevel + 1;
    if (v.list && v.list.length != 0) {
      editStepLevel(v.list);
    }
  });
};

// 改变缩放等级
const changeScale = () => {
  const scale = (scaleValue.value / 100).toFixed(2);
  const flowBoxRef = document.querySelector('.flow-main-content') as HTMLElement;
  if (flowBoxRef) {
    flowBoxRef.style.transform = `scale(${scale})`;
    flowBoxRef.style.transformOrigin = '50% 0px 0px';
  }
};
/**
 * 删除负责人和采集人员
 * @param stepList 流程步骤
 * @param i 索引
 * @param j 索引
 */
//  stepList[i]代表是那个流程节点   j 代表进入删除多个（3个或者3个以上）分支 中的指定一个
const handleDelStepInUserAndCharge = (stepList: FlowItem[], i: number, j?: number) => {
  //  进入分支的条件下 需要删除当前数组 stepList 等同于 == this.flowList 的第i个节点（这里一定是分支，所以分支下面还有list）中第i个节点下面的负责人 和采集人员
  // 也就是需要把 users = [] 、receiverNames = '' 、charges = [] 和 chargesName = '' 这四个值置为空
  if (j !== undefined) {
    // j  默认值为undefinde  如果j 有值 就代表只删除其中的一个值
    const item = stepList[i].list[j];
    item.delFlag = 1;
    item.users = [];
    item.receiverNames = '';
    item.charges = [];
    item.chargesName = '';
    if (item.chargesAttribution) {
      item.chargesAttribution.list = [];
    }
  } else {
    if (stepList[i]?.list && stepList[i]?.list.length > 0) {
      stepList[i].list.map((item) => {
        item.users = [];
        item.delFlag = 1;
        item.receiverNames = '';
        item.charges = [];
        item.chargesName = '';
        if (item.chargesAttribution) {
          item.chargesAttribution.list = [];
        }
        return item;
      });
    }
  }
};

// API调用方法
const getModelList = async () => {
  try {
    const res = await getModuleList([1, 8]);
    if (res.code == 200) {
      appTypeOptions.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

const getUserList = async () => {
  try {
    const res = await listUser({
      pageNum: 1,
      pageSize: 10000000
    });
    if (res.code == 200) {
      taskReceiversOptions.value = res.rows;
    }
  } catch (error) {}
};

// 任务名称处理
const getAddTaskLable = (item: any): string => {
  return item.status == 8 ? `${item.moduleName}(测试)` : item.moduleName;
};

// 表单验证和提交
const handleSubmitAddTask = () => {
  // 使用普通的Promise方式处理表单验证
  taskBasicsRef.value?.validate((valid) => {
    if (valid) {
      try {
        // 重置提交状态
        submitFlg.value = true;

        // 深拷贝 flowList 以避免直接修改原始数据
        const taskStepModels = JSON.parse(JSON.stringify(flowList.value));

        // 初始化步骤模型
        initGetTaskStepModels(taskStepModels, 0, false);

        // 验证每个步骤是否都有必要的内容
        verifyForm(taskStepModels);

        if (!submitFlg.value) {
          ElMessage.error('您有步骤未设置采集人员或采集内容！！！');
          return;
        }

        // 创建一个新的副本用于处理
        const ite_taskStepModels = JSON.parse(JSON.stringify(taskStepModels));
        setTaskStepModels(ite_taskStepModels);

        // 确保日期范围存在且正确设置
        if (!taskBasics.dateRange || taskBasics.dateRange.length < 2) {
          ElMessage.error('请选择正确的任务工期');
          return;
        }

        // 准备提交数据
        const data = {
          ...taskBasics,
          startTime: Number(taskBasics.dateRange[0]),
          endTime: Number(taskBasics.dateRange[1]),
          taskStepModels: ite_taskStepModels,
          id: taskBasics.id || undefined,
          timeTamp: taskBasics.timeTamp || '',
          area: taskBasics.area === '' ? 0 : Number(taskBasics.area),
          groupName: taskBasics.groupName
        };

        // 调用保存API
        addTask(data)
          .then((res) => {
            if (res.code === 200) {
              ElMessage.success('保存成功');
              // 重置流程步骤
              flowList.value = [
                {
                  typeName: '步骤1',
                  stepLevel: 1,
                  users: [],
                  receiverNames: '',
                  stepType: 1,
                  changeTitle: false,
                  treeNodes: [],
                  list: [],
                  charges: [],
                  chargesName: '',
                  isShowHandle: false,
                  ruleId: (Math.floor(100000 * Math.random()) + new Date().getTime()).toString(32).slice(0, 8),
                  chargesAttribution: { list: [] }
                }
              ];
              // 清空选择的数据
              selectedList.value = [];
              // 重置表单
              taskBasicsRef.value?.resetFields();
              //预计工作量
              taskBasics.workload = '';
              //任务工期
              taskBasics.dateRange = [];
              //任务备注
              taskBasics.remark = '';
              //任务数据
              // flowList.value = [];
              //业务模块
              taskBasics.moduleId = '';
              //任务权限
              taskBasics.taskAuth = '';
              //大屏分组字段
              taskBasics.groupName = '';
              //采集
              taskBasics.area = '';
              goBack();
            } else {
              ElMessage.error(res.msg || '数据已在其他任务中选择，请重新选择');
            }
          })
          .catch((error) => {
            console.error('提交表单时发生错误:', error);
            ElMessage.error('提交表单失败');
          });
      } catch (error) {
        console.error('处理数据时发生错误:', error);
        ElMessage.error('数据处理失败');
      }
    }
  });
};

// 修改验证函数
const verifyForm = (list) => {
  if (!list || !Array.isArray(list)) {
    submitFlg.value = false;
    return;
  }

  for (const item of list) {
    // 跳过已删除的步骤
    if (item.delFlag === 1) {
      continue;
    }

    // 检查步骤类型
    switch (item.stepType) {
      case 1: // 普通步骤
        // 检查是否有采集人员
        if (!Array.isArray(item.users) || item.users.length === 0) {
          submitFlg.value = false;
          return;
        }

        // 检查是否有采集内容
        if (!Array.isArray(item.treeNodes) || item.treeNodes.length === 0) {
          submitFlg.value = false;
          return;
        }

        // 检查是否有负责人
        if (!item.chargesAttribution?.list?.length) {
          submitFlg.value = false;
          return;
        }

        // 检查treeNodes的完整性
        for (const node of item.treeNodes) {
          if (!node.fieldGroupModelList || node.fieldGroupModelList.length === 0) {
            submitFlg.value = false;
            return;
          }
        }
        break;

      case 2: // 审批步骤
        if (!item.processId) {
          submitFlg.value = false;
          return;
        }
        break;

      case 3: // 分支步骤
        if (Array.isArray(item.list) && item.list.length > 0) {
          verifyForm(item.list);
          if (!submitFlg.value) {
            return;
          }
        }
        break;
    }

    // 递归验证子列表
    if (Array.isArray(item.list) && item.list.length > 0) {
      verifyForm(item.list);
      if (!submitFlg.value) {
        return;
      }
    }
  }
};

// 初始化步骤模型
const initGetTaskStepModels = (list, parentKey, isChild) => {
  if (!Array.isArray(list)) {
    return;
  }

  list.forEach((item, index) => {
    // 深拷贝处理，避免引用问题
    const itemCopy = JSON.parse(JSON.stringify(item));

    // 设置基本属性
    itemCopy.parentKey = parentKey;
    itemCopy.isChild = isChild;
    itemCopy.key = parentKey === 0 ? index : `${parentKey}-${index}`;

    // 处理子列表
    if (Array.isArray(itemCopy.list) && itemCopy.list.length > 0) {
      initGetTaskStepModels(itemCopy.list, itemCopy.key, true);
    }

    // 更新原始数据
    Object.assign(item, itemCopy);
  });
};

/**
 * 节点操作
 * @param item 流程步骤
 * @param type 类型
 */
const addNode = (item: FlowItem, type: number) => {
  addNodeToFlow(flowList.value, item, type);
};

/**
 * 添加节点到流程
 * @param list 流程步骤
 * @param item 流程步骤
 * @param type 类型
 */
const addNodeToFlow = (list: FlowItem[], item: FlowItem, type: number) => {
  for (let i = 0; i < list.length; i++) {
    if ((item.id && list[i].id == item.id) || (item.ruleId && list[i].ruleId == item.ruleId)) {
      let obj: FlowItem;
      const stepLevel = list[i].stepLevel + 1;

      if (type == 1) {
        //采集
        obj = {
          typeName: '步骤',
          stepLevel: stepLevel,
          users: [],
          receiverNames: '',
          stepType: 1, //流程类型 1步骤 2审批 3分支
          changeTitle: false,
          treeNodes: [], //选择的节点及属性组和属性
          list: [], //分支
          charges: [], //负责人
          chargesName: '',
          isShowHandle: false,
          ruleId: (Math.floor(100000 * Math.random()) + new Date().getTime()).toString(32).slice(0, 8), //用于删除处理
          chargesAttribution: { list: [] }
        };
      } else if (type == 2) {
        //分支
        obj = {
          typeName: '分支',
          stepLevel: stepLevel,
          users: [],
          receiverNames: '',
          stepType: 3, //流程类型 1步骤 2审批 3分支
          changeTitle: false,
          treeNodes: [], //选择的节点及属性组和属性
          charges: [], //负责人
          chargesName: '',
          ruleId: (Math.floor(100000 * Math.random()) + new Date().getTime()).toString(32).slice(0, 8), //用于删除处理
          list: [],
          isShowHandle: false,
          chargesAttribution: { list: [] }
        };
        const ite_obj1: FlowItem = {
          typeName: '步骤',
          stepLevel: stepLevel,
          users: [],
          receiverNames: '',
          stepType: 1, //流程类型 1步骤 2审批 3分支
          changeTitle: false,
          treeNodes: [], //选择的节点及属性组和属性
          charges: [], //负责人
          chargesName: '',
          list: [],
          isShowHandle: false,
          ruleId: (Math.floor(100000 * Math.random()) + new Date().getTime()).toString(32).slice(0, 8), //用于删除处理
          isChild: true,
          parentKey: obj.ruleId,
          chargesAttribution: { list: [] }
        };
        obj.list.push(ite_obj1);
        const ite_obj2: FlowItem = {
          typeName: '步骤',
          stepLevel: stepLevel,
          users: [],
          receiverNames: '',
          stepType: 1, //流程类型 1步骤 2审批 3分支
          changeTitle: false,
          treeNodes: [], //选择的节点及属性组和属性
          charges: [], //负责人
          chargesName: '',
          list: [],
          isShowHandle: false,
          ruleId: (Math.floor(100000 * Math.random()) + new Date().getTime()).toString(32).slice(0, 8), //用于删除处理
          isChild: true,
          parentKey: obj.ruleId,
          chargesAttribution: { list: [] }
        };
        obj.list.push(ite_obj2);
      } else if (type == 3) {
        //审批
        obj = {
          typeName: '审批',
          stepLevel: stepLevel,
          users: [],
          receiverNames: '',
          stepType: 2, //流程类型 1步骤 2审批 3分支
          changeTitle: false,
          treeNodes: [], //选择的节点及属性组和属性
          list: [], //分支
          charges: [], //负责人
          chargesName: '',
          isShowHandle: false,
          processName: '',
          processId: '',
          ruleId: (Math.floor(100000 * Math.random()) + new Date().getTime()).toString(32).slice(0, 8), //用于删除处理
          chargesAttribution: { list: [] }
        };
      } else {
        return;
      }

      if (item.isChild) {
        //分支下的内容
        // 需要先判断list[i]下面
        obj.stepLevel = obj.stepLevel - 1;
        obj.isChild = true;
        if (list[i].list.length == 0) {
          list[i].list.push(obj);
        } else {
          list[i].list.unshift(obj);
        }
      } else {
        //正常流程
        list.splice(i + 1, 0, obj);
        // 正常流程需要判断当前添加的位置下面是否还有内容，如果有内容需要把下面的所有内容的stepLevel+1
        // if (list.length-1>i+1) {
        //   for (let j = i+1; j < list.length; j++) {
        //     editStepLevel([list[j]])
        //   }
        // }
      }
      // 强制更新视图
      flowList.value = [...flowList.value];
      break;
    } else {
      if (list[i].list && list[i].list.length != 0) {
        addNodeToFlow(list[i].list, item, type);
      }
    }
  }
};

// 路由操作
const goBack = () => {
  router.go(-1);
};

// 生命周期钩子
onMounted(() => {
  const taskId = route.params.id;
  if (taskId && taskId !== '0') {
    title.value = '修改任务';
    getTaskDetail(Array.isArray(taskId) ? taskId[0] : taskId);
  } else {
  }
  getModelList();
  getUserList();
});

// 添加onActivated钩子
onActivated(() => {
  const taskId = route.params.id;
  if (taskId && taskId !== '0') {
    title.value = '修改任务';
    getTaskDetail(Array.isArray(taskId) ? taskId[0] : taskId);
  }
  getModelList();
  getUserList();
});

const setTaskStepModels = (list: FlowItem[]) => {
  list.forEach((item) => {
    if (item.stepType === 1) {
      // 设置接收者名称
      item.receiverNames = item.users.map((user) => user.nickName).join(',');

      // 确保每个用户都有 stepAttribution
      item.users.forEach((user) => {
        if (!user.stepAttribution && item.treeNodes && item.treeNodes.length > 0) {
          user.stepAttribution = {
            treeNodes: item.treeNodes
          };
        }
      });
    }

    // 递归处理子列表
    if (item.list && item.list.length > 0) {
      setTaskStepModels(item.list);
    }
  });
};

/**
 * 获取接收者名称
 * @param users 用户列表
 */
const getReceiverNames = (users: any[]): string => {
  if (!users || users.length === 0) return '';
  return users.map((item) => item.custName || item.nickName).join(',');
};

/**
 * 获取任务详情
 * @param id 任务ID
 */
const getTaskDetail = async (id: string | number) => {
  try {
    const res = await fetchTaskDetail(id);
    if (res.code == 200) {
      title.value = '修改流程任务';
      const data = res.data;
      // 组装基本数据
      taskBasics.name = data.name; // 任务名字
      taskBasics.moduleId = data.moduleId; // 业务模块
      taskBasics.type = data.type; // 任务类型 1新增 2修改 3补充
      taskBasics.workload = data.workload; // 预估工作量
      taskBasics.dateRange = [data.startTime, data.endTime]; // 任务工期时间段
      taskBasics.chargeUserId = data.chargeUserId; // 项目负责人
      taskBasics.descs = data.descs; // 任务描述
      taskBasics.remark = data.remark; // 任务备注
      taskBasics.scope = data.scope;
      taskBasics.flowType = 1;
      taskBasics.id = data.id;
      taskBasics.timeTamp = data.timeTamp;
      taskBasics.taskParcelModels = data.taskParcelModels;
      taskBasics.area = data.area;
      taskBasics.groupName = data.groupName;
      // 处理指派任务的宗地数据
      if (data.type == 2 || data.type == 0) {
        // 代表是指派任务 有宗地
        const parcelNames: string[] = [];
        const ids: (string | number)[] = [];

        if (taskBasics.taskParcelModels && Array.isArray(taskBasics.taskParcelModels)) {
          taskBasics.taskParcelModels.forEach((v) => {
            ids.push(v.parcelId);
            parcelNames.push(v.parcelName);
          });

          taskBasics.parcelNames = parcelNames.join(',');
          selectedList.value = taskBasics.taskParcelModels;
        }
      }

      // 组装步骤
      setInfo(data.taskStepModels, false);
      flowList.value = data.taskStepModels;

      // 获取模板数据
      handleChangeModuleTemplate();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取任务详情失败:', error);
    ElMessage.error('获取任务详情失败');
  }
};
/**
 * 反显步骤
 * @param list 步骤列表
 * @param isBranch 是否为分支
 * @param parentKey 父级key
 */
const setInfo = (list: any[], isBranch = false, parentKey?: string | number) => {
  list.forEach((v) => {
    // 设置接收者名称
    v.receiverNames = getReceiverNames(v.users);
    // 初始化基本属性
    v.isShowHandle = false;
    v.changeTitle = false;

    // 处理采集步骤
    if (v.stepType == 1) {
      console.log('完整步骤数据:', v);
      console.log('用户数据:', v.users[0]);
      // 分支不需要执行该步骤，分支没有绑定用户
      if (v.users?.[0]?.stepAttribution?.treeNodes) {
        v.treeNodes = v.users[0].stepAttribution.treeNodes;
      }
      v.charges = v.chargesAttribution?.list || [];
      v.chargesName = getReceiverNames(v.chargesAttribution?.list || []);
    }

    // 处理分支数据
    if (isBranch) {
      v.isChild = true;
      v.parentKey = parentKey;
    }

    // 递归处理子步骤
    if (v.list && v.list.length > 0) {
      if (v.stepType == 3) {
        // 分支
        setInfo(v.list, true, v.id);
      } else {
        setInfo(v.list, false);
      }
    }
  });
};
/**
 * 流程列表初始化
 * @param list 流程步骤
 */
const initFlowList = (list: FlowItem[]) => {
  list.forEach((item) => {
    // 保留id，确保在修改模式下能正确追踪节点
    // 不要删除id: delete item.id;
    // 添加chargesAttribution如果不存在
    if (!item.chargesAttribution) {
      item.chargesAttribution = { list: [] };
    } else if (!item.chargesAttribution.list) {
      item.chargesAttribution.list = [];
    }
    // 确保流程步骤中的子流程也被正确处理
    if (item.list && item.list.length > 0) {
      initFlowList(item.list);
    }
  });
};

/**
 * 根据业务模块查询默认流程模板
 */
const handleChangeModuleTemplate = async () => {
  try {
    const type = 3; // type  1:我创建的  2：我收到的 3:全部
    const params = {
      moduleId: taskBasics.moduleId,
      defaultFlag: true,
      pageType: type
    };

    const res = await getSearchTask(params, 1);

    if (res.code == 200) {
      taskList.value = res.data.list;
      // 以下注释的代码保留作为参考
      // total.value = res.data.total;
      // queryParams.value.pageSize = res.data.pageSize;
      // queryParams.value.pageNum = res.data.pageNum;
      // loading.value = false;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('查询默认模板失败:', error);
    ElMessage.error('查询默认模板失败');
  }
};

/**
 * 根据业务模块选择的任务模板更新步骤列表
 * @param val 任务ID
 */
const handleChangeStepList = async (val: string | number) => {
  try {
    const res = await fetchTaskDetail(val);

    if (res.code == 200) {
      const data = res.data;
      if (data.taskStepModels) {
        // 先初始化数据
        setInfo(data.taskStepModels, false);
        // 更新流程列表
        flowList.value = data.taskStepModels;
        initFlowList(flowList.value);
        console.log('流程列表:', flowList.value);
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取任务模板失败:', error);
    ElMessage.error('获取任务模板失败');
  }
};

// 关闭筛选数据弹窗
const closeSearchDialog = () => {
  // selectedList.value = [];
  searchDialog.value = false;
};

/**
 * 得到筛选要素数据
 * @param list 列表
 */
const getChooseData = (list: any[]) => {
  selectedList.value = [];
  list.forEach((v) => {
    const obj: any = {
      geomArcgis: v.geomArcgis,
      custName: v.custName || '',
      lockNum: v.lockNum || 0,
      parcelId: v.id,
      parcelName: v.parcelName,
      taskId: v.taskId || null,
      userId: v.userId || null
    };
    if (v.geomWkb || v.isNew) {
      //代表是后台反的数据
      obj.id = v.relationId || null;
    } else {
      obj.id = v.id;
    }
    selectedList.value.push(obj);
  });

  taskBasics.taskParcelModels = selectedList.value;
  const parcelNames: string[] = [];
  const parcelIds: (string | number)[] = [];
  list.forEach((item) => {
    parcelNames.push(item.parcelName);
    parcelIds.push(item.id);
  });

  taskBasics.parcelNames = parcelNames.join(',');
  taskBasics.parcelIds = parcelIds;

  if (taskBasics.parcelNames && taskBasics.parcelNames !== '') {
    taskBasicsRef.value?.clearValidate('parcelNames');
  }
  searchDialog.value = false;
};

const handleCloseFlowList = (item: FlowItem) => {
  flowList.value.map((ite) => {
    if (ite.ruleId == item.ruleId) {
      ite.receiverNames = '';
    }
    return ite;
  });
};

/**
 * 添加处理子组件更新的方法
 * @param updatedItem 更新项
 */
const updateItem = (updatedItem: FlowItem) => {
  // 在flowList中查找并更新对应项
  const updateFlowItem = (list: FlowItem[]) => {
    for (let i = 0; i < list.length; i++) {
      if ((updatedItem.ruleId && list[i].ruleId === updatedItem.ruleId) || (updatedItem.id && list[i].id === updatedItem.id)) {
        // 找到匹配项，更新它
        list[i] = { ...list[i], ...updatedItem };
        return true;
      }

      // 递归检查子列表
      if (list[i].list && list[i].list.length > 0) {
        if (updateFlowItem(list[i].list)) {
          return true;
        }
      }
    }
    return false;
  };

  updateFlowItem(flowList.value);
};

// 在 setup 中添加 dateShortcuts
const dateShortcuts: DateShortcut[] = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  }
];
</script>

<style lang="scss" scoped>
.handle-title {
  display: flex;
  padding-bottom: 10px;
  border-bottom: #d3d3d3 solid 2px;
  margin-bottom: 10px;
  justify-content: space-between;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
  .item {
    cursor: pointer;
  }
  .center {
    color: rgba(0, 0, 0, 1);
    font-size: 16px;
    font-weight: 600;
  }
}
.content-box {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .flex-item {
    width: calc(100% / 4 - 10px);
    margin-right: 10px;
  }
}
.group-item {
  margin-bottom: 28px;
  border: 1px solid #ededed;
  .group-title {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0px 16px;
    background: #f8f8f8;
    justify-content: space-between;
    cursor: pointer;
    .title {
      color: rgba(0, 0, 0, 0.6);
      font-size: 16px;
      font-weight: 600;
    }
    .icon {
      i {
        font-size: 18px;
        color: #999;
      }
    }
  }
  .group-content {
    padding: 16px;
    .flex-content {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      .flex-one {
        flex: 100%;
        margin-right: 16px;
      }
      .flex-item {
        flex: 0 0 calc(33.33% - 16px); /* 25% width minus the gap */
        margin-right: 16px; /* Right margin for the gap */
        // margin-bottom: 16px; /* Bottom margin for the gap */
        box-sizing: border-box; /* Include padding and border in the width */
      }
      .flex-row {
        width: calc(100% - 16px);
      }
    }
  }
}
.flow-box {
  width: 100%;
  background: #f0f2f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: auto;
  position: relative;
  .scaled-btn {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  .flow-main-content {
    padding: 32px;
    min-width: min-content;
    .end-div {
      display: flex;
      align-items: center;
      justify-content: center;
      .flow-end {
        height: 36px;
        border-radius: 24px;
        padding: 7px 24px;
        background: rgba(23, 26, 29, 0.03);
        color: rgba(25, 31, 37, 0.4);
        margin-top: 10px;
      }
    }
  }
}
.reset-btn {
  text-align: right;
  cursor: pointer;
  font-weight: 600;
}
.reset-btn:hover {
  color: red;
}
</style>
