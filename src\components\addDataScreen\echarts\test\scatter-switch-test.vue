<!-- 散点开关测试页面 -->
<template>
  <div class="test-container">
    <h2>散点开关功能测试</h2>
    
    <div class="controls">
      <el-button @click="toggleScatter" :type="showScatter ? 'primary' : 'default'">
        {{ showScatter ? '隐藏散点' : '显示散点' }}
      </el-button>
      <span class="status">当前状态: {{ showScatter ? '显示' : '隐藏' }}</span>
    </div>
    
    <div class="chart-wrapper">
      <cpt-chart-3dmap 
        :width="800" 
        :height="600" 
        :option="chartOption" 
      />
    </div>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <pre>{{ JSON.stringify(chartOption.attribute, null, 2) }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CptChart3dmap from '../cpt-chart-3dmap.vue';

defineOptions({
  name: 'scatter-switch-test'
});

const showScatter = ref(true);

const chartOption = computed(() => ({
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify({
      mapData: [
        { 'name': '毕节市', 'value': 105 },
        { 'name': '遵义市', 'value': 41 },
        { 'name': '铜仁市', 'value': 30 },
        { 'name': '贵阳市', 'value': 52 }
      ],
      scatterData: [
        { 'name': '贵阳数据中心', 'lng': 106.713478, 'lat': 26.578343, 'value': 150 },
        { 'name': '遵义分站', 'lng': 107.297104, 'lat': 27.725654, 'value': 89 },
        { 'name': '毕节监测点', 'lng': 105.300492, 'lat': 27.302612, 'value': 76 }
      ]
    })
  },
  attribute: {
    map: 'areaGuizhou',
    titleText: '散点开关测试',
    mapHeight: 4,
    distance: 120,
    alpha: 45,
    beta: -20,
    isShowLabel: true,
    labelColor: '#fff',
    fontSize: 12,
    mapBackgroundColor: '#4389ED',
    borderWidth: 2,
    borderColor: '#61CFF8',
    isShowLed: true,
    ledColor: '#fff',
    mapLedColor: '#007EE8',
    mapLedBorderWidth: 3,
    mapLedBorderColor: '#6BECF5',
    
    // 散点配置 - 关键测试点
    showScatter: showScatter.value,
    scatterName: '测试散点',
    scatterSymbol: 'circle',
    scatterSize: [10, 30],
    scatterColor: '#FF6B6B',
    scatterOpacity: 0.9,
    scatterEmphasisColor: '#FF4757',
    showScatterLabel: true,
    scatterLabelColor: '#fff',
    scatterLabelSize: 12
  }
}));

const toggleScatter = () => {
  showScatter.value = !showScatter.value;
  console.log('散点开关切换:', showScatter.value);
  console.log('当前配置:', chartOption.value.attribute.showScatter);
};

// 监听变化
watch(showScatter, (newVal) => {
  console.log('showScatter 变化:', newVal);
}, { immediate: true });

watch(() => chartOption.value.attribute.showScatter, (newVal) => {
  console.log('chartOption.attribute.showScatter 变化:', newVal);
}, { immediate: true });
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.status {
  font-weight: bold;
  color: #409eff;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #f5f5f5;
}

.debug-info {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
  margin-top: 20px;
}

.debug-info h3 {
  margin-top: 0;
  color: #333;
}

.debug-info pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}
</style>
