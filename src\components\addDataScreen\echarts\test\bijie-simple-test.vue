<!-- 毕节地图简单测试 -->
<template>
  <div class="test-container">
    <h2>毕节地图简单测试</h2>
    
    <div class="status">
      <p><strong>地图状态:</strong> <span :class="mapStatus.class">{{ mapStatus.text }}</span></p>
      <p><strong>特征数量:</strong> {{ mapStatus.features }}</p>
      <p><strong>测试数据:</strong> {{ testData.length }} 个点</p>
    </div>
    
    <div class="controls">
      <el-button @click="useRealData">使用真实坐标</el-button>
      <el-button @click="useTestData">使用测试坐标</el-button>
      <el-button @click="showMapInfo">显示地图信息</el-button>
    </div>
    
    <div class="chart-wrapper">
      <div id="bijie-test-chart" style="width: 100%; height: 100%;"></div>
    </div>
    
    <div class="data-info">
      <h3>当前测试数据</h3>
      <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
    </div>
    
    <div class="console-output">
      <h3>控制台输出</h3>
      <div class="logs">
        <div v-for="(log, index) in logs" :key="index" :class="['log', log.type]">
          {{ log.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';

defineOptions({
  name: 'bijie-simple-test'
});

let chart: any = null;
const logs = ref<Array<{type: string, message: string}>>([]);

// 真实的毕节区县坐标
const realBijieData = [
  { name: '七星关区', lng: 105.284852, lat: 27.302085, value: 100 },
  { name: '大方县', lng: 105.609254, lat: 27.143521, value: 80 },
  { name: '黔西市', lng: 106.038299, lat: 27.024923, value: 90 },
  { name: '金沙县', lng: 106.222103, lat: 27.459693, value: 70 },
  { name: '织金县', lng: 105.768997, lat: 26.668497, value: 85 },
  { name: '纳雍县', lng: 105.375322, lat: 26.769875, value: 75 },
  { name: '威宁县', lng: 104.286523, lat: 26.859099, value: 95 },
  { name: '赫章县', lng: 104.726438, lat: 27.119243, value: 65 }
];

// 测试坐标（确保在毕节范围内）
const testBijieData = [
  { name: '测试点1', lng: 105.5, lat: 27.0, value: 100 },
  { name: '测试点2', lng: 105.8, lat: 27.2, value: 120 },
  { name: '测试点3', lng: 106.0, lat: 27.1, value: 80 }
];

const testData = ref(realBijieData);

const mapStatus = computed(() => {
  const mapData = echarts.getMap('bijie');
  if (mapData) {
    return {
      class: 'success',
      text: '已注册',
      features: mapData.geoJson?.features?.length || 0
    };
  } else {
    return {
      class: 'error', 
      text: '未注册',
      features: 0
    };
  }
});

const addLog = (type: string, message: string) => {
  logs.value.push({ type, message });
  console.log(`[${type.toUpperCase()}] ${message}`);
};

const loadChart = () => {
  if (!chart) return;
  
  addLog('info', '开始加载毕节地图...');
  
  const option = {
    title: {
      text: '毕节市散点图',
      left: 'center',
      textStyle: {
        color: '#333',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        if (params.seriesType === 'scatter') {
          return `${params.name}<br/>经度: ${params.value[0]}<br/>纬度: ${params.value[1]}<br/>数值: ${params.value[2]}`;
        }
        return `${params.name}`;
      }
    },
    geo: {
      map: 'bijie',
      roam: true,
      zoom: 1.2,
      center: [105.5, 27.0], // 毕节市中心
      label: {
        show: true,
        fontSize: 12,
        color: '#333'
      },
      itemStyle: {
        areaColor: '#f0f0f0',
        borderColor: '#999',
        borderWidth: 1
      },
      emphasis: {
        label: {
          show: true,
          color: '#000'
        },
        itemStyle: {
          areaColor: '#e0e0e0'
        }
      }
    },
    series: [
      {
        name: '数据点',
        type: 'scatter',
        coordinateSystem: 'geo',
        symbol: 'circle',
        symbolSize: 12,
        itemStyle: {
          color: '#ff6b6b',
          opacity: 0.8,
          borderWidth: 2,
          borderColor: '#fff'
        },
        label: {
          show: true,
          position: 'top',
          color: '#333',
          fontSize: 12,
          formatter: (params: any) => params.name
        },
        emphasis: {
          itemStyle: {
            color: '#ff4757',
            opacity: 1,
            shadowBlur: 10,
            shadowColor: '#ff4757'
          }
        },
        data: testData.value.map(item => ({
          name: item.name,
          value: [item.lng, item.lat, item.value]
        }))
      }
    ]
  };
  
  try {
    chart.clear();
    chart.setOption(option);
    addLog('success', `地图加载成功，显示 ${testData.value.length} 个数据点`);
  } catch (error) {
    addLog('error', `地图加载失败: ${error}`);
  }
};

const useRealData = () => {
  testData.value = realBijieData;
  addLog('info', '切换到真实坐标数据');
  loadChart();
};

const useTestData = () => {
  testData.value = testBijieData;
  addLog('info', '切换到测试坐标数据');
  loadChart();
};

const showMapInfo = () => {
  const mapData = echarts.getMap('bijie');
  if (mapData) {
    addLog('info', `毕节地图信息: ${mapData.geoJson?.features?.length} 个区县`);
    
    const features = mapData.geoJson?.features || [];
    const districts = features.map((f: any) => f.properties?.name).filter(Boolean);
    addLog('info', `区县列表: ${districts.join(', ')}`);
    
    // 计算坐标范围
    let minLng = Infinity, maxLng = -Infinity;
    let minLat = Infinity, maxLat = -Infinity;
    
    features.forEach((feature: any) => {
      const center = feature.properties?.center;
      if (center && Array.isArray(center)) {
        minLng = Math.min(minLng, center[0]);
        maxLng = Math.max(maxLng, center[0]);
        minLat = Math.min(minLat, center[1]);
        maxLat = Math.max(maxLat, center[1]);
      }
    });
    
    addLog('info', `坐标范围: 经度 ${minLng.toFixed(3)} ~ ${maxLng.toFixed(3)}, 纬度 ${minLat.toFixed(3)} ~ ${maxLat.toFixed(3)}`);
  } else {
    addLog('error', '毕节地图未注册');
  }
};

onMounted(() => {
  chart = echarts.init(document.getElementById('bijie-test-chart'));
  
  addLog('info', '页面初始化完成');
  showMapInfo();
  loadChart();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.status {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.status .success {
  color: #52c41a;
  font-weight: bold;
}

.status .error {
  color: #ff4d4f;
  font-weight: bold;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.data-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.data-info pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.console-output {
  background: #f0f0f0;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.logs {
  background: #000;
  color: #fff;
  padding: 15px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log {
  margin: 2px 0;
}

.log.error {
  color: #ff6b6b;
}

.log.success {
  color: #51cf66;
}

.log.info {
  color: #74c0fc;
}

h2, h3 {
  color: #333;
}
</style>
