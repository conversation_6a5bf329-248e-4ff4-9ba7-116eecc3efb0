# GEO SVG 散点图组件使用说明

## 概述

GEO SVG 散点图组件是一个基于 ECharts 的可拖拽地图散点图组件，支持多种地图底图切换、散点样式配置和交互功能。

## 功能特性

- ✅ **多地图支持**: 支持中国、省市级地图切换
- ✅ **散点类型**: 支持普通散点和涟漪效果散点
- ✅ **动态大小**: 散点大小可根据数值动态调整
- ✅ **视觉映射**: 支持颜色渐变映射数值
- ✅ **交互功能**: 支持地图缩放、平移、悬停提示
- ✅ **标签配置**: 支持散点标签显示和格式化
- ✅ **样式定制**: 丰富的样式配置选项
- ✅ **数据格式**: 支持多种数据格式输入

## 组件信息

- **组件名称**: `cpt-chart-geo-scatter`
- **文件位置**: `src/components/addDataScreen/echarts/cpt-chart-geo-scatter.vue`
- **配置文件**: `src/components/addDataScreen/echarts/attr/cpt-chart-geo-scatter-option.ts`
- **配置界面**: `src/components/addDataScreen/echarts/options/cpt-chart-geo-scatter-option.vue`

## 数据格式

### 支持的数据格式

#### 格式1: 经纬度分离
```json
[
  { "name": "北京", "lng": 116.46, "lat": 39.92, "value": 150 },
  { "name": "上海", "lng": 121.48, "lat": 31.22, "value": 200 }
]
```

#### 格式2: 坐标数组
```json
[
  { "name": "北京", "coord": [116.46, 39.92], "value": 150 },
  { "name": "上海", "coord": [121.48, 31.22], "value": 200 }
]
```

#### 格式3: 值数组
```json
[
  { "name": "北京", "value": [116.46, 39.92, 150] },
  { "name": "上海", "value": [121.48, 31.22, 200] }
]
```

## 配置选项

### 标题配置
- `titleText`: 主标题文本
- `subtext`: 副标题文本
- `titleLeft`: 标题位置 ('left' | 'center' | 'right')
- `titleColor`: 标题颜色
- `titleFontSize`: 标题字体大小

### 地图配置
- `map`: 地图类型 (支持已注册的地图名称)
- `roam`: 是否允许缩放和平移
- `zoom`: 初始缩放比例
- `center`: 地图中心点 [经度, 纬度]
- `mapAreaColor`: 地图区域颜色
- `mapBorderColor`: 地图边界颜色
- `mapBorderWidth`: 边界线宽度

### 散点配置
- `seriesName`: 系列名称
- `scatterType`: 散点类型 ('scatter' | 'effectScatter')
- `scatterSymbol`: 散点符号 ('circle' | 'rect' | 'triangle' | 'diamond' | 'pin' | 'arrow')
- `scatterBaseSize`: 基础大小
- `scatterSizeMultiplier`: 大小倍数
- `scatterColor`: 散点颜色
- `scatterOpacity`: 散点透明度

### 标签配置
- `showScatterLabel`: 是否显示标签
- `scatterLabelPosition`: 标签位置 ('top' | 'bottom' | 'left' | 'right' | 'inside')
- `scatterLabelFormat`: 标签格式 ('name' | 'value' | 'both')
- `scatterLabelColor`: 标签颜色
- `scatterLabelSize`: 标签字体大小

### 涟漪效果配置
- `rippleBrushType`: 涟漪类型 ('stroke' | 'fill')
- `rippleScale`: 涟漪缩放倍数
- `ripplePeriod`: 涟漪动画周期

### 视觉映射配置
- `showVisualMap`: 是否显示视觉映射
- `visualMapMin`: 最小值
- `visualMapMax`: 最大值
- `visualMapMinColor`: 最小值颜色
- `visualMapMaxColor`: 最大值颜色

## 使用示例

### 基础用法
```vue
<template>
  <cpt-chart-geo-scatter 
    :width="800" 
    :height="600" 
    :option="chartOption" 
  />
</template>

<script setup>
const chartOption = {
  cptDataForm: {
    dataSource: 1,
    dataText: JSON.stringify([
      { name: '北京', lng: 116.46, lat: 39.92, value: 150 },
      { name: '上海', lng: 121.48, lat: 31.22, value: 200 }
    ])
  },
  attribute: {
    titleText: '全国数据分布',
    map: 'china',
    scatterType: 'scatter',
    showScatterLabel: true
  }
}
</script>
```

### 涟漪散点效果
```javascript
const chartOption = {
  attribute: {
    scatterType: 'effectScatter',
    rippleBrushType: 'stroke',
    rippleScale: 3,
    ripplePeriod: 4
  }
}
```

### 视觉映射
```javascript
const chartOption = {
  attribute: {
    showVisualMap: true,
    visualMapMin: 0,
    visualMapMax: 200,
    visualMapMinColor: '#50a3ba',
    visualMapMaxColor: '#eac736'
  }
}
```

## 支持的地图

### 2D地图
- `china` - 中国地图
- `guizhou` - 贵州省地图
- `changZhou` - 常州市地图
- `sihui` - 四会市地图

### 3D地图 (兼容)
- `areaChina` - 中国3D地图
- `areaGuizhou` - 贵州省3D地图
- `areaGuiYang` - 贵阳市3D地图
- `areaBiJie` - 毕节市3D地图

## 拖拽使用

1. **组件已注册**: 组件已自动注册到系统中
2. **拖拽到画布**: 从组件库拖拽到数据大屏画布
3. **配置数据**: 在右侧配置面板设置数据源和样式
4. **实时预览**: 配置修改后实时预览效果

## 注意事项

1. **坐标系统**: 使用WGS84坐标系统（经纬度）
2. **性能考虑**: 建议散点数量控制在500个以内
3. **地图注册**: 确保使用的地图已在 `RegisterMap.ts` 中注册
4. **数据验证**: 组件会自动过滤无效的坐标数据

## 常见问题

### Q: 散点位置不准确？
A: 检查经纬度数据是否正确，确保使用WGS84坐标系统。

### Q: 地图不显示？
A: 检查地图名称是否正确，确保地图已在 `RegisterMap.ts` 中注册。

### Q: 涟漪效果不显示？
A: 确保 `scatterType` 设置为 `'effectScatter'`。

### Q: 如何自定义散点大小？
A: 调整 `scatterBaseSize` 和 `scatterSizeMultiplier` 参数。

## 更新日志

- v1.0.0: 初始版本，支持基础散点图功能
- v1.1.0: 添加涟漪效果和视觉映射
- v1.2.0: 支持多种数据格式和地图切换
