<!-- 硬编码区域颜色测试 -->
<template>
  <div class="test-container">
    <h2>硬编码区域颜色测试</h2>
    
    <div class="controls">
      <el-button @click="refreshChart">刷新图表</el-button>
      <el-button @click="showEChartsOption">显示ECharts配置</el-button>
    </div>
    
    <div class="chart-wrapper">
      <div id="hardcode-test-chart" style="width: 100%; height: 100%;"></div>
    </div>
    
    <div class="debug-info">
      <h3>ECharts配置</h3>
      <pre>{{ echartsOptionDisplay }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';

defineOptions({
  name: 'hardcode-region-test'
});

let chart: any = null;
const echartsOptionDisplay = ref('');

const createChart = () => {
  if (!chart) return;
  
  console.log('=== 硬编码区域颜色测试 ===');
  
  const option = {
    title: {
      text: '毕节市硬编码区域颜色测试',
      left: 'center',
      textStyle: {
        color: '#333',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        if (params.seriesType === 'scatter') {
          return `${params.name}<br/>经度: ${params.value[0]}<br/>纬度: ${params.value[1]}<br/>数值: ${params.value[2]}`;
        }
        return params.name;
      }
    },
    geo: {
      map: 'bijie',
      roam: true,
      zoom: 4,
      center: [105.5, 27.0],
      label: {
        show: true,
        fontSize: 12,
        color: '#333'
      },
      itemStyle: {
        areaColor: '#f0f0f0',
        borderColor: '#999',
        borderWidth: 1
      },
      emphasis: {
        itemStyle: {
          areaColor: '#e0e0e0'
        }
      },
      // 硬编码区域颜色
      regions: [
        {
          name: '七星关区',
          itemStyle: {
            areaColor: '#FF6B6B',
            borderColor: '#999',
            borderWidth: 1
          }
        },
        {
          name: '大方县',
          itemStyle: {
            areaColor: '#4ECDC4',
            borderColor: '#999',
            borderWidth: 1
          }
        },
        {
          name: '黔西市',
          itemStyle: {
            areaColor: '#45B7D1',
            borderColor: '#999',
            borderWidth: 1
          }
        },
        {
          name: '金沙县',
          itemStyle: {
            areaColor: '#96CEB4',
            borderColor: '#999',
            borderWidth: 1
          }
        },
        {
          name: '织金县',
          itemStyle: {
            areaColor: '#FFEAA7',
            borderColor: '#999',
            borderWidth: 1
          }
        },
        {
          name: '纳雍县',
          itemStyle: {
            areaColor: '#DDA0DD',
            borderColor: '#999',
            borderWidth: 1
          }
        },
        {
          name: '威宁彝族回族苗族自治县',
          itemStyle: {
            areaColor: '#98D8C8',
            borderColor: '#999',
            borderWidth: 1
          }
        },
        {
          name: '赫章县',
          itemStyle: {
            areaColor: '#F7DC6F',
            borderColor: '#999',
            borderWidth: 1
          }
        }
      ]
    },
    series: [
      {
        name: '测试数据',
        type: 'scatter',
        coordinateSystem: 'geo',
        symbol: 'circle',
        symbolSize: 15,
        itemStyle: {
          color: '#ff6b6b',
          opacity: 0.8,
          borderWidth: 2,
          borderColor: '#fff'
        },
        label: {
          show: true,
          position: 'top',
          color: '#333',
          fontSize: 12,
          formatter: (params: any) => params.name
        },
        data: [
          { name: '七星关区', value: [105.284852, 27.302085, 100] },
          { name: '大方县', value: [105.609254, 27.143521, 80] }
        ]
      }
    ]
  };
  
  try {
    chart.clear();
    chart.setOption(option);
    console.log('硬编码区域颜色图表渲染成功');
    
    // 显示配置
    echartsOptionDisplay.value = JSON.stringify(option, null, 2);
  } catch (error) {
    console.error('图表渲染失败:', error);
  }
};

const refreshChart = () => {
  createChart();
};

const showEChartsOption = () => {
  console.log('当前ECharts配置:', chart.getOption());
};

onMounted(() => {
  chart = echarts.init(document.getElementById('hardcode-test-chart'));
  
  console.log('硬编码区域颜色测试页面加载完成');
  createChart();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.chart-wrapper {
  width: 800px;
  height: 600px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  background: #fff;
}

.debug-info {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.debug-info pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 10px;
  max-height: 400px;
  overflow-y: auto;
}

h2, h3 {
  color: #333;
}
</style>
