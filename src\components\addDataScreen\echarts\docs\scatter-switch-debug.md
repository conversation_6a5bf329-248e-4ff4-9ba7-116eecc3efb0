# 散点开关问题排查指南

## 问题描述
散点显示开关 `showScatter` 没有生效，散点始终显示或始终不显示。

## 排查步骤

### 1. 检查控制台日志
打开浏览器开发者工具，查看控制台是否有以下调试信息：

```
散点检查: {
  hasScatterData: true/false,
  scatterDataLength: 数字,
  showScatter: true/false,
  showScatterType: "boolean"
}
```

### 2. 检查数据加载
确认是否看到以下日志：
- `JSON散点数据加载: [...]` - 表示散点数据已加载
- `API散点数据加载: [...]` - 表示从API加载的散点数据

### 3. 检查配置传递
在使用组件的地方，确认配置是否正确：

```javascript
// 正确的配置
const chartOption = {
  attribute: {
    showScatter: true, // 必须明确设置为 true
    // 其他配置...
  }
}
```

### 4. 检查响应式更新
如果使用 `computed` 或 `ref`，确保响应式更新正常：

```javascript
// 使用 computed
const chartOption = computed(() => ({
  attribute: {
    showScatter: showScatterRef.value, // 确保响应式
  }
}));

// 或使用 reactive
const config = reactive({
  attribute: {
    showScatter: true
  }
});
```

## 常见问题及解决方案

### 问题1: showScatter 值不是布尔类型
**现象**: 控制台显示 `showScatterType: "string"` 或其他类型
**解决**: 确保传入的是布尔值 `true/false`，不是字符串 `"true"/"false"`

### 问题2: 散点数据为空
**现象**: 控制台显示 `hasScatterData: false`
**解决**: 检查数据格式和数据源

```javascript
// 正确的数据格式
{
  mapData: [...],
  scatterData: [
    { name: '点1', lng: 106.7, lat: 26.5, value: 100 },
    // 更多散点...
  ]
}
```

### 问题3: 组件未重新渲染
**现象**: 配置改变但图表未更新
**解决**: 检查 watch 监听是否正常

```javascript
// 确保深度监听
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true }
);
```

### 问题4: 配置界面开关无效
**现象**: 点击开关但图表无变化
**解决**: 检查双向绑定

```vue
<!-- 确保正确绑定 -->
<el-switch v-model="attributeCopy.showScatter" />

<!-- 确保 attributeCopy 是响应式的 -->
<script>
const attributeCopy = computed(() => props.attribute);
</script>
```

## 测试方法

### 方法1: 使用测试页面
使用 `scatter-switch-test.vue` 测试页面：
1. 打开测试页面
2. 点击"显示/隐藏散点"按钮
3. 观察图表变化和控制台日志

### 方法2: 手动测试
在现有页面中添加临时按钮：

```vue
<template>
  <div>
    <button @click="toggleScatter">切换散点</button>
    <cpt-chart-3dmap :option="chartOption" />
  </div>
</template>

<script>
const showScatter = ref(true);

const toggleScatter = () => {
  showScatter.value = !showScatter.value;
  console.log('散点开关:', showScatter.value);
};

const chartOption = computed(() => ({
  attribute: {
    showScatter: showScatter.value,
    // 其他配置...
  }
}));
</script>
```

## 调试技巧

### 1. 添加临时日志
在组件中添加更多调试信息：

```javascript
console.log('loadChart 调用:', {
  showScatter: attribute.showScatter,
  scatterDataExists: !!scatterData.value,
  scatterDataLength: scatterData.value?.length
});
```

### 2. 检查 ECharts 配置
在 `chart?.setOption(chartOption)` 之前添加：

```javascript
console.log('ECharts 配置:', JSON.stringify(chartOption, null, 2));
```

### 3. 强制重新渲染
如果怀疑是渲染问题，可以尝试：

```javascript
chart?.clear();
chart?.setOption(chartOption);
```

## 预期行为

- `showScatter: true` - 显示散点
- `showScatter: false` - 不显示散点
- 切换开关时图表应立即更新
- 控制台应显示相应的调试信息

## 如果问题仍然存在

1. 提供控制台的完整日志
2. 提供当前的配置对象
3. 说明具体的问题现象
4. 提供复现步骤
