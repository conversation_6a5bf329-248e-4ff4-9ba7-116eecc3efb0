<!-- 图形属性 -->
<template>
  <div class="msgInfo-main" v-loading="loading">
    <div class="title">
      <div class="title-right" @click="closeInfo">
        <el-icon><Close /></el-icon>
      </div>
      <div class="title-num">
        <el-dropdown @command="handleCommandYZ" v-show="showVerify">
          <span class="el-dropdown-link">
            <el-icon color="white"><Setting /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="1">表达式验证</el-dropdown-item>
              <el-dropdown-item command="6">表达式验证(文件)</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <div v-show="!route.query.isAZF && !route.query.iscq">
          id：{{ checkedNodeId }}
          <span style="margin-left: 10px">{{ checkedLinyeCopy.parcelCode }}</span>
        </div>
      </div>
    </div>
    <!-- 正常显示属性 -->
    <div class="info-select-tab">
      <div class="info-left">
        <div class="menu-box">
          <div v-for="(item, index) in selectTitleList" :key="index">
            <div class="menu-item" :class="{ 'menu-active': item.linkId == activeMenuLinkId }" @click="changeTab(item)">
              {{ item.typeName }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 执行 拆迁户安置房或者现金相关 -->
    <template v-if="activeMenu.isChooseAZF">
      <el-button type="primary" size="small" icon="el-icon-plus" style="width: 80px; margin: 0px 10px 10px 10px" @click="handleAddAZF"
        >新增</el-button
      >
      <el-table :data="activeMenu.zxAzfList" style="width: 100%" :height="cqazHeight" border>
        <el-table-column label="序号" type="index"></el-table-column>
        <el-table-column :label="field.label" v-for="(field, fdx) in activeMenu.fieldList" :key="fdx">
          <template #default="scope">
            <template v-if="field.type == 8">
              <div v-for="(ite, idx) in scope.row.fieldList[fdx].value" :key="idx" class="fj-box">
                <div class="fj-item" @click="showFjDialog(ite)">
                  <el-icon><Document /></el-icon>
                  <span style="margin-left: 5px">{{ spanRuleCenter(ite.title, 18) }}</span>
                </div>
              </div>
            </template>
            <template v-else>
              {{ scope.row.fieldList[fdx].value }}
            </template>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template v-else>
      <template v-if="!showBigDeg">
        <div class="content" :class="{ 'content-padding': allowEdit }">
          <template v-if="!showBigDeg && !nowChecked.isChild">
            <div class="content-normal">
              <!-- 正常走属性 -->
              <div class="grid-row" v-for="(zditem, zindex) in nowChecked.fieldList" :key="zindex">
                <template v-if="zditem.label != '超期过渡费表格'">
                  <div class="label" :title="zditem.field ? zditem.field.fieldName : ''" @contextmenu.prevent="copyText(zditem)">
                    <span
                      v-show="zditem.field && zditem.field.attribution.expression"
                      :title="zditem.field && zditem.field.attribution.expression"
                      style="color: red"
                    >
                      <el-icon><InfoFilled /></el-icon>
                    </span>
                    {{ zditem.label }}
                  </div>
                  <div class="hr"></div>
                  <div class="right-content" :title="zditem.value" v-if="zditem.type == 1 && zditem.type !== 7">
                    <!-- 在正常数据中区分当前数组是不是数字类型的 -->
                    <div style="width: 100%" v-if="zditem.field && zditem.field.valueMethod == 'number'">
                      <div
                        v-if="
                          isEditAttr &&
                          zditem.field &&
                          (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                        "
                        style="width: 100%"
                      >
                        <el-input
                          v-model="zditem.value"
                          :placeholder="zditem.field.inputHint"
                          :maxlength="getNumberMaxLength(zditem)"
                          style="width: 100%"
                          @input="handleNumberInput($event, zditem.field, zditem)"
                        ></el-input>
                      </div>
                      <div v-else>{{ zditem.value }}</div>
                    </div>
                    <!-- 日期区间 type等于1 -->
                    <div style="width: 100%" v-else-if="zditem.field && zditem.field.valueMethod == 'date-range'">
                      <div
                        v-if="
                          isEditAttr &&
                          zditem.field &&
                          (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                        "
                        style="width: 100%"
                      >
                        <el-date-picker
                          :modelValue="formatDateRangeValue(zditem.value)"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          value-format="YYYY-MM-DD"
                          @update:modelValue="handleDateRangeUpdate($event, zditem)"
                        >
                        </el-date-picker>
                      </div>
                      <div v-else>{{ zditem.value }}</div>
                    </div>
                    <!-- 下拉选择 -->
                    <div v-else-if="zditem.field && zditem.field.valueMethod == 'select'" style="width: 100%">
                      <div
                        v-if="
                          isEditAttr &&
                          zditem.field &&
                          (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                        "
                        style="width: 100%"
                      >
                        <el-select v-model="zditem.value" :placeholder="zditem.field.inputHint" clearable filterable style="width: 100%">
                          <el-option v-for="item in zditem.field.attribution.options" :key="item.value" :label="item.label" :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div v-else :style="filterOptionFun(zditem.value, zditem.field.attribution.options)">
                        {{ filterOption(zditem.value, zditem.field.attribution.options) }}
                      </div>
                    </div>
                    <!-- 单选radio -->
                    <div v-else-if="zditem.field && zditem.field.valueMethod == 'radio'">
                      <div
                        v-if="
                          isEditAttr &&
                          zditem.field &&
                          (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                        "
                        style="width: 100%"
                      >
                        <el-radio-group v-model="zditem.value">
                          <el-radio :value="item.value" v-for="(item, index) in zditem.field.attribution.options" :key="index">{{
                            item.label
                          }}</el-radio>
                        </el-radio-group>
                      </div>
                      <div v-else>
                        {{ filterOption(zditem.value, zditem.field.attribution.options) }}
                      </div>
                    </div>
                    <!-- 多选框 -->
                    <div v-else-if="zditem.field && zditem.field.valueMethod == 'checkbox'">
                      <div
                        v-if="
                          isEditAttr &&
                          zditem.field &&
                          (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                        "
                        style="width: 100%"
                      >
                        <el-checkbox-group v-model="zditem.value">
                          <el-checkbox
                            :value="item.value"
                            v-for="(item, index) in zditem.field.attribution.options"
                            :key="index"
                            @change="handleCheckBoxValue($event, zditem.field, zditem)"
                            >{{ item.label }}</el-checkbox
                          >
                        </el-checkbox-group>
                      </div>
                      <div v-else>{{ handleArrArrayShow(zditem.value) }}</div>
                    </div>
                    <!-- 非数字之外的其他值 -->
                    <div style="width: 100%" v-else>
                      <div
                        v-if="
                          isEditAttr &&
                          zditem.field &&
                          (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                        "
                        style="width: 100%"
                      >
                        <el-input
                          v-model="zditem.value"
                          :placeholder="zditem.field.inputHint"
                          :maxlength="getNumberMaxLength(zditem)"
                          style="width: 100%"
                          v-if="zditem.field.fieldName == 'AZFFW'"
                          readonly
                        ></el-input>
                        <el-input
                          v-model="zditem.value"
                          :placeholder="zditem.field.inputHint"
                          :maxlength="zditem.field.attribution.maxLength"
                          style="width: 100%"
                          v-else
                        ></el-input>
                      </div>
                      <div v-else>{{ zditem.value }}</div>
                    </div>
                  </div>
                  <!-- 日期类型 -->
                  <div class="right-content" v-if="zditem.type == 3 && zditem.type !== 7">
                    <div
                      v-if="
                        isEditAttr &&
                        zditem.field &&
                        (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                      "
                      style="width: 100%"
                    >
                      <el-date-picker
                        v-model="zditem.value"
                        :type="zditem.field.attribution.type"
                        value-format="x"
                        :placeholder="zditem.field.inputHint"
                        style="width: 100%"
                      >
                      </el-date-picker>
                    </div>
                    <div v-else>
                      <span v-if="zditem.field.attribution.type == 'date'">{{ formatDateType(zditem.value) }}</span>
                      <span v-else-if="zditem.field.attribution.type == 'month'">{{ formatDateTypeMonth(zditem.value) }}</span>
                      <span v-else-if="zditem.field.attribution.type == 'year'">{{ formatDateTypeYear(zditem.value) }}</span>
                      <span v-else>{{ formatDateType(zditem.value) }}</span>
                    </div>
                  </div>
                  <!-- time类型 -->
                  <div class="right-content" v-if="zditem.type == 4 && zditem.type !== 7">
                    <div
                      v-if="
                        isEditAttr &&
                        zditem.field &&
                        (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                      "
                      style="width: 100%"
                    >
                      <el-time-picker
                        v-model="zditem.value"
                        :picker-options="{
                          selectableRange: '00:00:00 - 23:59:59'
                        }"
                        value-format="HH:mm:ss"
                        :placeholder="zditem.field.inputHint"
                        style="width: 100%"
                      >
                      </el-time-picker>
                    </div>
                    <div v-else>
                      {{ zditem.value }}
                    </div>
                  </div>
                  <!-- 图片类型 -->
                  <div v-if="zditem.type == 2 && zditem.type !== 7" class="right-content">
                    <template
                      v-if="
                        isEditAttr &&
                        zditem.field &&
                        (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                      "
                    >
                      <el-upload
                        :action="`${base}/qjt/file/multi/upload`"
                        :headers="headers"
                        name="files"
                        accept=".jpg,.png"
                        list-type="picture-card"
                        :file-list="zditem.fieldList"
                        :limit="zditem.field.attribution.picNum || 1"
                        :on-success="(response) => handleSuccessTP(response, zditem)"
                        :on-remove="(file) => handleRemoveTP(file, zditem)"
                        :on-exceed="(files, fileList) => handleExceed(files, fileList, zditem)"
                      >
                        <el-icon><Plus /></el-icon>
                      </el-upload>
                    </template>
                    <template v-else>
                      <div class="content-img">
                        <div class="img-box" v-for="(ite, idx) in zditem.value" :key="idx">
                          <el-image
                            v-if="ite.url"
                            :class="{ 'active-img': ite.active }"
                            class="img-box"
                            :src="getSrc(ite)"
                            :fit="`cover`"
                            @click="
                              showDeg(
                                `${base}/qjt/file/otherDownload/${ite.url}?bucketName=${ite.bucketName}&token=${token}`,
                                ite.fwj,
                                ite.wzxx,
                                ite,
                                idx,
                                zditem.value.length,
                                zditem.value
                              )
                            "
                            @mouseenter="handleEnter(ite)"
                            @mouseleave="handleLeave(ite)"
                          >
                          </el-image>
                          <div v-else class="no-img">暂无数据</div>
                        </div>
                      </div>
                    </template>
                  </div>
                  <!-- 动植物识别 -->
                  <div class="right-content" v-if="zditem.type == 5 && zditem.type !== 7">
                    <div
                      v-if="
                        isEditAttr &&
                        zditem.field &&
                        (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                      "
                      style="width: 100%"
                    >
                      <span style="color: rgb(0, 0, 0, 0.4)">暂不支持</span>
                    </div>

                    <div v-else class="discern-box" v-for="(dis, dix) in zditem.discernList" :key="dix" @click="showSpe(dis)">
                      <authImg
                        v-if="dis.picUrl"
                        :authSrc="`${baseUrl}${dis.picUrl}?att=1`"
                        :show-preview="false"
                        :attrItem="{}"
                        :width="'140px'"
                        :height="'110px'"
                      />
                      <div v-else class="no-img">暂无数据</div>
                      <div class="footer-div">
                        {{ dis.animalPlantName }}
                      </div>
                    </div>
                  </div>
                  <!-- 视频 -->
                  <div class="right-content" v-if="zditem.type == 6 && zditem.type !== 7">
                    <div
                      v-if="
                        isEditAttr &&
                        zditem.field &&
                        (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                      "
                      style="width: 100%"
                    >
                      <span style="color: rgb(0, 0, 0, 0.4)">暂不支持</span>
                    </div>
                    <div v-else>
                      <div v-for="(ite, idx) in zditem.value" :key="idx" class="img-box">
                        <videoDefault
                          v-if="ite"
                          :authSrc="`${baseUrl}${ite.firstPicUrl}?att=1`"
                          :videoSrc="`${baseUrl}${ite.videoUrl}?att=1`"
                          :videoId="`${ite.videoUrl}`"
                          :width="'100%'"
                          :height="'100%'"
                          @showVideo="showVideo(ite.videoUrl)"
                        />
                        <div v-else class="no-img">暂无数据</div>
                      </div>
                    </div>
                  </div>
                  <!-- 表格 -->
                  <div class="right-content" v-if="zditem.type == 7">
                    <el-link type="primary" @click="showTable(zditem.value, zditem.label)">查看详情</el-link>
                  </div>
                  <!-- 附件 -->
                  <div v-if="zditem.type == 8 && zditem.type !== 7" class="right-content">
                    <div
                      v-if="
                        isEditAttr &&
                        zditem.field &&
                        (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                      "
                      style="width: 100%"
                    >
                      <el-upload
                        class="upload-demo"
                        :headers="headers"
                        :action="`${base}/qjt/file/multi/upload`"
                        :on-success="(response) => handleSuccessFJ(response, zditem.field, zditem)"
                        :on-remove="(file) => handleRemoveFJ(file, zditem.field, zditem)"
                        multiple
                        name="files"
                        :before-upload="(file) => beforeAvatarUpload(file, zditem.field)"
                        :limit="zditem.field.attribution.picNum"
                        :on-exceed="(files, fileList) => handleExceed(files, fileList, zditem.field)"
                        :file-list="zditem.value || []"
                      >
                        <el-button size="small" type="primary">点击上传</el-button>

                        <template #tip>
                          <div class="el-upload__tip" style="color: red">只能上传{{ zditem.field.attribution.acceptType.join(',') }}文件</div>
                        </template>

                        <template #file="{ file }">
                          <div class="fj-row">
                            {{ file['title'] }}
                            <div style="cursor: pointer" @click="handleRemove(file, zditem)">×</div>
                          </div>
                        </template>
                      </el-upload>
                    </div>
                    <div v-else>
                      <div v-for="(ite, idx) in zditem.value" :key="idx" class="fj-item" @click="showFjDialog(ite)">
                        <el-icon style="color: #409eff; cursor: pointer"><Document /></el-icon>
                        <span style="margin-left: 5px; color: #409eff; cursor: pointer">{{ spanRuleCenter(ite.title, 18) }}</span>
                      </div>
                    </div>
                  </div>
                  <!-- 级联选择 -->
                  <div v-if="zditem.type == 9 && zditem.type !== 7" class="right-content">
                    <div
                      v-if="
                        isEditAttr &&
                        zditem.field &&
                        (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                      "
                      style="width: 100%"
                    >
                      <el-cascader
                        v-model="zditem.value"
                        :options="zditem.field.attribution.options"
                        :props="{ checkStrictly: true }"
                        @change="handleChange($event, zditem)"
                        style="width: 100%"
                      ></el-cascader>
                    </div>
                    <div v-else>{{ handleArrArrayShow(zditem.value) }}</div>
                  </div>
                  <!-- 单选 -->
                  <div v-if="zditem.type == 10 && zditem.type !== 7" class="right-content">
                    <!-- radio -->
                    <div
                      v-if="
                        isEditAttr &&
                        zditem.field &&
                        (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                      "
                      style="width: 100%"
                    >
                      <el-radio-group v-model="zditem.value">
                        <el-radio v-for="it in zditem.field.attribution.options" :key="it.value" :value="it.value">{{ it.label }}</el-radio>
                      </el-radio-group>
                    </div>
                    <div v-else>
                      {{ filterOption(zditem.value, zditem.field.attribution.options) }}
                    </div>
                  </div>
                  <!-- 行政区划 -->
                  <div v-if="zditem.type == 11" class="right-content">
                    <div
                      v-if="
                        isEditAttr &&
                        zditem.field &&
                        (!zditem.field.attribution.expression || (zditem.field.attribution.expression && editExpressPower))
                      "
                      style="width: 100%"
                    >
                      <span style="color: rgb(0, 0, 0, 0.4)">暂不支持</span>
                    </div>
                    <div v-else>
                      {{ getAreaCode(zditem.value) }}
                    </div>
                  </div>
                </template>
              </div>

              <!-- 表格在下方展示在最后 -->
              <div class="grid-table-main" v-if="addCqgdfTableList.length != 0 && showCQGDFTable">
                <div class="table-list">
                  <div class="table-title">
                    <span>超期过渡费表格</span>
                    <span style="padding-right: 8px">
                      <el-date-picker
                        v-model="cqgdfRangDate"
                        type="monthrange"
                        range-separator="至"
                        start-placeholder="开始月份"
                        value-format="x"
                        end-placeholder="结束月份"
                        @change="changecqgdfRangDate"
                      >
                      </el-date-picker>
                    </span>
                  </div>
                  <el-table :data="addCqgdfTableList" style="width: 100%" border>
                    <el-table-column v-for="(item, index) in cqgdfField.attribution.children" :key="index" :label="item.fieldCn">
                      <template #default="scope">
                        <template v-if="item.fieldCn == '月份选择'">
                          {{ formatDateTypeMonth(scope.row[item.fieldName]) }}
                        </template>
                        <template v-else-if="item.fieldCn == '过渡补偿价格'">
                          {{ scope.row[item.fieldName] }}
                        </template>
                        <template v-else>
                          <template v-if="item.valueMethod == 'input'">
                            <el-input v-model="scope.row[item.fieldName]" :placeholder="item.inputHint"></el-input>
                          </template>
                          <template v-if="item.valueMethod == 'radio'">
                            <span v-if="scope.row[item.fieldName] == '是'" style="color: #67c23a">已付款</span>
                            <span v-else style="color: #f56c6c">未付款</span>
                          </template>
                          <template v-if="item.valueMethod == 'select'">
                            <el-select v-model="scope.row[item.fieldName]" :placeholder="item.inputHint">
                              <el-option v-for="(ite, idx) in item.attribution.options" :key="idx" :label="item.label" :value="item.value">
                              </el-option>
                            </el-select>
                          </template>
                        </template>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </template>
          <!-- 多个权利人、多个界址点或界址线 -->
          <template v-if="!showBigDeg && nowChecked.isChild">
            <el-card class="box-card" v-for="(item, index) in nowChecked.list" :key="index" style="margin: 8px">
              <template #header>
                <div class="clearfix">
                  <span>{{ item.label }}</span>
                  <div style="float: right; display: flex">
                    <template v-if="item.fieldList.length > 4">
                      <el-link
                        type="danger"
                        style="margin-right: 10px"
                        v-show="item.isShow && !item.isEdit && item.linkType in linkTypeList"
                        @click="delChild(item, index)"
                        >删除</el-link
                      >
                      <el-link
                        type="primary"
                        style="margin-right: 10px"
                        v-show="item.isShow && !item.isEdit && item.linkType != 5 && item.linkType != 6 && !isIfrem"
                        @click="editChild(item)"
                        >编辑</el-link
                      >
                      <el-link
                        type="primary"
                        style="margin-right: 10px"
                        v-show="item.isShow && item.isEdit && item.linkType != 5 && item.linkType != 6"
                        @click="saveChild(item, index)"
                        >保存</el-link
                      >
                      <span style="cursor: pointer" v-show="!item.isShow" @click="changeShow(item, 1)"
                        >展开 <el-icon><ArrowDown /></el-icon
                      ></span>
                      <span style="cursor: pointer" v-show="item.isShow" @click="changeShow(item, 2)"
                        >收起 <el-icon><ArrowUp /></el-icon
                      ></span>
                    </template>
                    <template v-else>
                      <el-link
                        type="danger"
                        style="margin-right: 10px"
                        v-show="!item.isEdit && item.linkType in linkTypeList"
                        @click="delChild(item, index)"
                        >删除</el-link
                      >
                      <el-link
                        type="primary"
                        style="margin-right: 10px"
                        v-show="!item.isEdit && !isIfrem && item.linkType != 5 && item.linkType != 6"
                        @click="editChild(item)"
                        >编辑</el-link
                      >
                      <el-link
                        type="primary"
                        style="margin-right: 10px"
                        v-show="item.isEdit && item.linkType != 5 && item.linkType != 6"
                        @click="saveChild(item, index)"
                        >保存</el-link
                      >
                    </template>
                  </div>
                </div>
              </template>

              <div class="conten-box">
                <div class="child-row" v-for="(ite, idx) in item.fieldList" :key="idx" v-show="item.isShow || (!item.isShow && idx < 4)">
                  <div class="child-item child-left" :title="ite.field ? ite.field.fieldName : ''">
                    <span
                      v-show="ite.field && ite.field.attribution.expression"
                      :title="ite.field && ite.field.attribution.expression"
                      style="color: red; margin-top: 2px"
                    >
                      <el-icon><InfoFilled /></el-icon
                    ></span>
                    {{ ite.label }}
                  </div>
                  <div class="hr"></div>
                  <div class="child-item child-right" :title="ite.value" v-if="ite.type == 1">
                    <!-- 下拉选择 -->
                    <div v-if="ite.field && ite.field.valueMethod == 'select'" style="width: 100%">
                      <div v-if="item.isEdit">
                        <el-select v-model="ite.value" :placeholder="ite.field.inputHint" clearable filterable style="width: 100%">
                          <el-option v-for="it in ite.field.attribution.options" :key="it.value" :label="it.label" :value="it.value"> </el-option>
                        </el-select>
                      </div>
                      <div v-else :style="filterOptionFun(ite.value, ite.field.attribution.options)">
                        {{ filterOption(ite.value, ite.field.attribution.options) }}
                      </div>
                    </div>
                    <div v-else-if="ite.field && ite.field.valueMethod == 'date-range'">
                      <div v-if="item.isEdit" style="width: 100%">
                        <el-date-picker
                          v-model="ite.value"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          style="width: 100%"
                        >
                        </el-date-picker>
                      </div>
                      <div v-else>{{ ite.value }}</div>
                    </div>
                    <div v-else-if="ite.field && ite.field.valueMethod == 'checkbox'">
                      <div v-if="item.isEdit" style="width: 100%">
                        <el-checkbox-group v-model="ite.value" @change="handleCheckBoxValue($event, ite.field, ite)">
                          <el-checkbox :value="it.value" v-for="(it, index) in ite.field.attribution.options" :key="index">{{
                            it.label
                          }}</el-checkbox>
                        </el-checkbox-group>
                      </div>
                      <div v-else>{{ ite.value }}</div>
                    </div>

                    <div v-else>
                      <div v-if="item.isEdit">
                        <el-input v-model="ite.value" :placeholder="ite.field ? ite.field.inputHint : ''"></el-input>
                      </div>
                      <div v-else>{{ ite.value }}</div>
                    </div>
                  </div>
                  <!-- 日期类型 -->
                  <div class="child-item child-right" v-if="ite.type == 3">
                    <div v-if="item.isEdit">
                      <el-date-picker
                        v-model="ite.value"
                        :type="ite.field.attribution.type"
                        value-format="x"
                        :placeholder="ite.field.inputHint"
                        style="width: 100%"
                      >
                      </el-date-picker>
                    </div>

                    <div v-else>
                      <span v-if="ite.field.attribution.type == 'date'">{{ formatDateType(ite.value) }}</span>
                      <span v-else-if="ite.field.attribution.type == 'month'">{{ formatDateTypeMonth(ite.value) }}</span>
                      <span v-else-if="ite.field.attribution.type == 'year'">{{ formatDateTypeYear(ite.value) }}</span>
                      <span v-else>{{ formatDateType(ite.value) }}</span>
                    </div>
                  </div>
                  <!-- radio单选 -->
                  <div class="child-item child-right" v-if="ite.type == 10">
                    <div v-if="item.isEdit">
                      <el-radio-group v-model="ite.value">
                        <el-radio v-for="it in ite.field.attribution.options" :key="it.value" :value="it.value">{{ it.label }}</el-radio>
                      </el-radio-group>
                    </div>
                    <div v-else>
                      {{ filterOption(ite.value, ite.field.attribution.options) }}
                    </div>
                  </div>
                  <!-- time类型 -->
                  <div class="child-item child-right" v-if="ite.type == 4">
                    <div v-if="item.isEdit">
                      <el-time-picker
                        v-model="ite.value"
                        :picker-options="{
                          selectableRange: '00:00:00 - 23:59:59'
                        }"
                        value-format="HH:mm:ss"
                        :placeholder="ite.field.inputHint"
                        style="width: 100%"
                      >
                      </el-time-picker>
                    </div>
                    <div v-else>
                      {{ formatTime(ite.value) }}
                    </div>
                  </div>
                  <!-- 图片类型 -->
                  <div v-if="ite.type == 2" style="flex: 1; padding: 0px 10px">
                    <template v-if="!item.isEdit">
                      <div class="content-img" style="margin-left: 10px">
                        <div class="img-box" v-for="(o, odx) in ite.value" :key="odx">
                          <el-image
                            v-if="o.url"
                            class="img-box"
                            :class="{ 'active-img': o.active }"
                            :src="getSrc(o)"
                            :preview-src-list="getPreview(ite.value)"
                            :fit="`cover`"
                            @click="
                              showDeg(
                                `${base}/qjt/file/otherDownload/${o.url}?bucketName=${o.bucketName}&token=${token}`,
                                o.fwj,
                                o.wzxx,
                                o,
                                odx,
                                ite.value.length,
                                ite.value
                              )
                            "
                            @mouseenter="handleEnter(o)"
                            @mouseleave="handleLeave(o)"
                          >
                          </el-image>
                          <div v-else class="no-img">暂无数据</div>
                        </div>
                      </div>
                    </template>
                    <template v-else>
                      <el-upload
                        :action="`${base}/qjt/file/multi/upload`"
                        :headers="headers"
                        name="files"
                        accept=".jpg,.png"
                        list-type="picture-card"
                        :file-list="ite.fieldList"
                        :limit="ite.field.attribution.picNum || 1"
                        :on-success="(response) => handleSuccessTP(response, ite)"
                        :on-remove="(file) => handleRemoveTP(file, ite)"
                        :on-exceed="(files, fileList) => handleExceed(files, fileList, ite)"
                      >
                        <el-icon><Plus /></el-icon>
                      </el-upload>
                    </template>
                  </div>
                  <!-- 动植物识别 -->
                  <div class="content-spe" v-if="ite.type == 5">
                    <div class="discern-box" v-for="(dis, dix) in ite.discernList" :key="dix" @click="showSpe(dis)">
                      <authImg v-if="dis.picUrl" :authSrc="`${baseUrl}${dis.picUrl}?att=1`" :width="'100%'" :height="'133px'" />
                      <div v-else class="no-img">暂无数据</div>
                      <div class="footer-div">
                        {{ dis.animalPlantName }}
                      </div>
                    </div>
                  </div>
                  <!-- 视频 -->
                  <div class="content-img" style="margin-left: 10px" v-if="ite.type == 6">
                    <div v-for="(o, odx) in ite.value" :key="odx" class="img-box">
                      <videoDefault
                        :authSrc="`${baseUrl}${o.firstPicUrl}?att=1`"
                        :videoSrc="`${baseUrl}${o.videoUrl}?att=1`"
                        :videoId="`${o.videoUrl}`"
                        :width="'100%'"
                        :height="'50px'"
                        @showVideo="showVideo"
                        v-if="o"
                      />
                      <div v-else class="no-img">暂无数据</div>
                    </div>
                  </div>
                  <!-- 表格 -->
                  <div v-if="ite.type == 7" style="width: 100%">
                    <el-link type="primary" @click="showTable(ite.value, ite.label)">查看详情</el-link>
                  </div>
                  <!-- 附件 -->
                  <div v-if="ite.type == 8" class="fj-content">
                    <div v-for="(o, odx) in ite.value" :key="odx" class="fj-item" @click="showFjDialog(o)">
                      <!-- <i class="el-icon-document"></i> -->
                      <el-icon><Document /></el-icon>
                      <span style="margin-left: 5px">{{ spanRuleCenter(o.title, 18) }}</span>
                    </div>
                  </div>
                  <!-- 级联选择 -->
                  <div v-if="ite.type == 9" class="child-item">
                    <div v-if="item.isEdit" style="width: 100%">
                      <el-cascader
                        v-model="ite.value"
                        :options="ite.field.attribution.options"
                        :props="{ checkStrictly: true }"
                        @change="handleChange($event, ite)"
                        style="width: 100%"
                      ></el-cascader>
                    </div>
                    <div v-else>
                      {{ ite.value }}
                    </div>
                  </div>
                  <!-- 行政区划 -->
                  <div v-if="ite.type == 11" class="right-content">{{ getAreaCode(ite.value) }}</div>
                </div>
              </div>
            </el-card>
          </template>

          <!-- 操作日志 -->
          <div v-show="nowChecked.isLog">
            <logList :dataId="nowChecked.linkId" ref="logListRef" :ruleTree="ruleTree"></logList>
          </div>
          <!-- 拓扑检查日志 -->
          <div v-show="nowChecked.isTuopuLog">
            <tuopuCheck :dataId="nowChecked.linkId" ref="tuopuCheckRef"></tuopuCheck>
          </div>
          <!-- 表达式刷新异常信息 -->
          <div v-if="nowChecked.isExpressLog" style="padding-top: 10px">
            <el-button type="primary" size="small" @click="refExpress" style="margin: 0px 0px 10px 10px">刷新表达式</el-button>
            <template v-if="checkedLinye.flushExpressMessage">
              <div class="express-row">
                <div class="express-label">创建时间：</div>
                <div class="express-con">{{ formatDateYmdhm(checkedLinye.flushExpressMessage.createTime) }}</div>
              </div>
              <div class="express-row">
                <div class="express-label">更新时间：</div>
                <div class="express-con">{{ formatDateYmdhm(checkedLinye.flushExpressMessage.updateTime) }}</div>
              </div>
              <div class="express-row">
                <div class="express-label">异常内容：</div>
                <div class="express-con">{{ checkedLinye.flushExpressMessage.remark }}</div>
              </div>
            </template>
            <template v-else>
              <el-empty description="暂无表达式属性异常" />
            </template>
          </div>
        </div>
        <div class="info-bottom" v-if="nowChecked.fieldList && nowChecked.fieldList.length > 0">
          <el-button type="primary" size="small" @click="editAtrr" v-show="!isEditAttr && !isIfrem">编辑</el-button>
          <el-link type="info" @click="cancatEditAttr" v-show="isEditAttr" style="margin-right: 10px">取消</el-link>
          <el-link type="primary" v-show="isEditAttr" @click="saveEditAttr">保存</el-link>
        </div>
      </template>
      <!-- 特殊 方位角大图展示 -->
      <template v-else>
        <div class="deg-box">
          <div class="handle-div">
            <div @click="closeDeg">
              <el-button type="primary" size="small">
                <!-- <i class="el-icon-arrow-left"></i> -->
                <el-icon><ArrowLeft /></el-icon>
                返回</el-button
              >
            </div>
            <div style="color: #fff">{{ syImgIndex + 1 }}/{{ syLength }}</div>
            <div>
              <el-button class="min-btn" type="primary" size="small" :disabled="syImgIndex == 0" @click="syBtn(1)">上一张</el-button>
              <el-button type="primary" size="small" :disabled="syImgIndex == syLength - 1" @click="syBtn(2)">下一张</el-button>
            </div>
          </div>
          <div class="setting" v-show="Object.keys(showImgAttr.fwj).length != 0">
            <div @click="showImgAttrSyCheckboox = !showImgAttrSyCheckboox" style="margin-bottom: 5px">
              水印
              <el-icon v-show="!showImgAttrSyCheckboox"><CaretBottom /></el-icon>
              <el-icon v-show="showImgAttrSyCheckboox"><CaretTop /></el-icon>
            </div>
            <div v-show="showImgAttrSyCheckboox">
              <el-checkbox v-model="showImgAttr.pssb.isSy" class="checked-row">拍摄设备</el-checkbox>
              <el-checkbox v-model="showImgAttr.pssj.isSy" class="checked-row">拍摄时间</el-checkbox>
              <el-checkbox v-model="showImgAttr.fwj.isSy" class="checked-row">方位角</el-checkbox>
              <el-checkbox v-model="showImgAttr.wzxx.isSy" class="checked-row">位置信息</el-checkbox>
              <el-checkbox v-model="showImgAttr.psdz.isSy" class="checked-row">拍摄地址</el-checkbox>
              <el-checkbox v-model="showImgAttr.psry.isSy" class="checked-row">拍摄人员</el-checkbox>
            </div>
          </div>
          <el-image class="deg-img" :src="degUrl" :fit="fit as any" ref="speImgRef" :preview-src-list="[degUrl]"></el-image>
          <div class="sy-div">
            <div class="sy-item" v-show="showImgAttr.pssb && showImgAttr.pssb.isSy">
              {{ showImgAttr.pssb.value }}
            </div>
            <div class="sy-item" v-show="showImgAttr.pssj && showImgAttr.pssj.isSy">
              {{ formatDateYmdhm(showImgAttr.pssj.value) }}
            </div>
            <div class="sy-item" v-show="showImgAttr.fwj && showImgAttr.fwj.isSy">
              {{ getDirection(showImgAttr.fwj.value) }}
            </div>
            <div class="sy-item" v-show="showImgAttr.wzxx && showImgAttr.wzxx.isSy">
              {{ getLonAndLat(showImgAttr.wzxx.value) }}
            </div>
            <div class="sy-item" v-show="showImgAttr.psdz && showImgAttr.psdz.isSy">
              {{ showImgAttr.psdz.value }}
            </div>
            <div class="sy-item" v-show="showImgAttr.psry && showImgAttr.psry.isSy">
              {{ showImgAttr.psry.value }}
            </div>
          </div>
        </div>
      </template>
    </template>
    <!-- 特殊数据弹窗 -->
    <el-dialog :title="speTitle" v-model="speMsgDialog" width="800px" :before-close="handleClose">
      <div class="spe-content">
        <div class="left-img">
          <authImg :authSrc="`${baseUrl}${nowSpeMsg.picUrl}?att=1`" :width="'100%'" :height="'auto'" v-if="nowSpeMsg.picUrl" :radios="'8px'" />
          <div v-else class="no-img">暂无数据</div>
        </div>
        <div class="right-content">
          <div class="spe-title">{{ nowSpeMsg.animalPlantName }}</div>
          <div v-if="nowSpeMsg.intro">{{ nowSpeMsg.intro }}</div>
          <div v-else>暂无数据</div>
        </div>
      </div>
    </el-dialog>
    <videoTemp v-show="videoDialog" :checkedVideId="checkedVideId" :videoDialog="videoDialog" @closeVideoDialog="closeVideoDialog"></videoTemp>

    <!-- 表格弹窗 -->
    <el-dialog :title="tableDialogTitle" v-model="tableDialog" width="700px" :close-on-click-modal="false" :before-close="handleCloseTable">
      <div class="dialog-table">
        <table border="1" width="100%" class="table-class">
          <tr class="th-class">
            <td v-for="(th, thx) in tableValue.tableTh" :key="thx" class="td">
              {{ th.fieldCn }}
            </td>
          </tr>
          <tr v-if="tableValue.trs.length == 0">
            <td :colspan="tableValue.tableTh.length">暂无数据</td>
          </tr>
          <tr v-for="(tr, trx) in tableValue.trs" :key="trx" class="tr-class">
            <td v-for="(td, tdx) in tr" :key="tdx" class="td">
              <!-- 图片 -->
              <template v-if="td.valueMethod == 'upload'">
                <el-link type="primary" @click="showDetail(td)">查看详情</el-link>
              </template>
              <!-- 签名、指纹 -->
              <template v-else-if="td.valueMethod == 'xtqm' || td.valueMethod == 'xtzw'">
                <el-link type="primary" @click="showDetail(td)">查看详情</el-link>
              </template>
              <!-- 视频 -->
              <template v-else-if="td.valueMethod == 'xtvideo'">
                <el-link type="primary" @click="showDetail(td)">查看详情</el-link>
              </template>
              <template v-else-if="td.valueMethod == 'xtfj'">
                <el-link type="primary" @click="showDetail(td)">查看详情</el-link>
              </template>
              <!-- 日期 -->
              <template v-else-if="td.valueMethod == 'date'">
                <template v-if="td.type == 'month'">
                  {{ formatDateTypeMonth(td.value) }}
                </template>
                <template v-else-if="td.type == 'year'">
                  {{ formatDateTypeYear(td.value) }}
                </template>
                <template v-else>
                  {{ formatDateType(td.value) }}
                </template>
              </template>
              <!-- 其他正常情况 -->
              <template v-else>
                {{ td.value }}
              </template>
            </td>
          </tr>
        </table>
      </div>
    </el-dialog>
    <!-- 表格弹窗 -->
    <el-dialog title="详情" v-model="detailDialog" width="400px" :close-on-click-modal="false" :before-close="handleCloseTableDetail">
      <template v-if="!detailValue.value">暂无数据</template>
      <template v-if="detailValue.valueMethod == 'upload'">
        <div class="content-img">
          <div v-for="(o, odx) in detailValue.value" :key="odx" class="img-box">
            <el-image style="width: 100%; height: 100%" :src="getSrc(o)" :fit="`cover`" :preview-src-list="[getSrc(o)]" v-if="o.url"></el-image>
            <div v-else class="no-img">暂无数据</div>
          </div>
        </div>
      </template>
      <template v-else-if="detailValue.valueMethod == 'xtqm' || detailValue.valueMethod == 'xtzw'">
        <div class="content-img" v-if="detailValue.value">
          <div class="img-box">
            <el-image
              style="width: 100%; height: 100%"
              :src="getSrc(detailValue)"
              :fit="`contain`"
              :preview-src-list="[getSrc(detailValue)]"
            ></el-image>
          </div>
        </div>
      </template>
      <template v-else-if="detailValue.valueMethod == 'xtvideo'">
        <div class="content-img" style="margin-left: 10px">
          <div v-for="(o, odx) in detailValue.value" :key="odx" class="img-box">
            <videoDefault
              :authSrc="`${baseUrl}${o.firstPicUrl}?att=1`"
              :videoSrc="`${baseUrl}${o.videoUrl}?att=1`"
              :width="'100%'"
              :height="'100%'"
              @showVideo="showVideo(o.videoUrl)"
              v-if="o"
            />
            <div v-else class="no-img">暂无数据</div>
          </div>
        </div>
      </template>
      <template v-else-if="detailValue.valueMethod == 'xtfj'">
        <div class="fj-content" style="color: #606266">
          <div v-for="(o, odx) in detailValue.value" :key="odx" class="fj-item" @click="showFjDialog(o)">
            <!-- <i class="el-icon-document"></i> -->
            <el-icon><Document /></el-icon>
            <span style="margin-left: 5px">{{ spanRuleCenter(o.title, 18) }}</span>
          </div>
        </div>
      </template>
    </el-dialog>
    <!-- 查看附件 -->
    <el-dialog title="查看" v-model="fjDialog" :before-close="handleCloseFj" :close-on-click-modal="false" width="90%">
      <div v-show="fileType == 1" style="width: 100%" :style="{ height: windowHeight }" v-html="vHtml"></div>
      <div v-show="fileType == 2">
        <el-table
          :data="excelData"
          border
          stripe
          style="width: 100%; overflow: auto"
          :style="{ height: windowHeight }"
          :header-cell-style="{ background: '#F5F4F7' }"
        >
          <el-table-column type="index" label="序号" width="60" :resizable="false" align="center" />
          <template v-if="excelData && excelData.length > 0">
            <el-table-column v-for="(header, index) in Object.keys(excelData[0])" :key="index" :prop="header" :label="header" />
          </template>
        </el-table>
      </div>
      <div v-show="fileType == 3" style="width: 100%">
        <iframe :src="fjUrl" frameborder="0" width="100%" :height="windowHeight"></iframe>
      </div>
    </el-dialog>
    <!-- 数据纠错弹窗 -->
    <el-dialog title="数据纠错" v-model="errorDialog" :before-close="handleCloseErr" :close-on-click-modal="false" width="870px">
      <div class="dialog-row">
        <div class="dialog-item">
          <div class="lable">属性组：</div>
          <el-select v-model="errorEdit.groupId" size="small" placeholder="请选择" class="flex-content">
            <el-option v-for="item in checkedLinyeCopy.fieldGroupModels" :key="item.id" :label="item.typeName" :value="item.id"> </el-option>
          </el-select>
        </div>
        <div class="dialog-item" style="margin-left: 10px">
          <el-button type="primary" size="small" @click="editField">查询</el-button>
        </div>
      </div>
      <div class="dialog-row" v-show="localThList.length != 0">
        <div class="dialog-item">
          <el-select v-model="localIndex" size="small" placeholder="请选择" class="flex-content">
            <el-option v-for="(item, index) in localThList" :key="item.label" :label="item.label" :value="index"> </el-option>
          </el-select>
        </div>
        <div class="dialog-item" style="margin-left: 10px" v-if="localIndex != null">
          <el-input
            v-model="localKeyWord"
            :placeholder="`请输入${localThList[localIndex].label}`"
            size="small"
            clearable
            @clear="localSearch"
          ></el-input>
        </div>
        <div class="dialog-item" style="margin-left: 10px">
          <el-button type="primary" size="small" @click="localSearch">本地搜索</el-button>
        </div>
      </div>
      <el-table :data="errorList" style="width: 100%" height="500" border>
        <el-table-column v-for="(item, index) in thList" :key="index" :label="item.label">
          <template #default="scope">
            <template v-if="scope.row.fieldList[index].type == 1">{{ scope.row.fieldList[index].value }}</template>
            <template v-if="scope.row.fieldList[index].type == 2">
              <div class="content-img-table">
                <div v-for="(ite, idx) in scope.row.fieldList[index].value" :key="idx" class="img-box">
                  <authImg v-if="ite.url" :authSrc="`${baseUrl}${ite.url}?att=1`" :width="'100%'" :height="'40px'" />
                  <div v-else class="no-img">暂无数据</div>
                </div>
              </div>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-link type="primary" @click="updateAttr(scope.row)">更新</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="errorEdit.pageNum"
        :page-sizes="[10, 20, 100, 200]"
        :page-size="errorEdit.pageSize"
        layout="total, sizes, prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </el-dialog>
    <!-- 上传文件验证表达式 -->
    <el-dialog title="上传验证文件" v-model="uploadCheckedDesignFile" width="500px" :before-close="handleCloseDesign" :close-on-click-modal="false">
      <el-upload
        style="width: 100%"
        class="upload-demo"
        ref="desginRef"
        accept=".doc,.docx,.xls,.xlsx"
        :action="`${base}/qjt/file/multi/upload`"
        :headers="headers"
        name="files"
        :on-success="handleSuccessDesgin"
        :auto-upload="false"
        drag
      >
        <!-- <i class="el-icon-upload"></i> -->
        <el-icon><Upload /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDesign">取 消</el-button>
          <el-button type="primary" @click="submitDesign">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 超期过渡费新增 -->
    <addCQGDFComponents
      :cqgdfDialog="cqgdfDialog"
      @closeCQGDF="closeCQGDF"
      @submitCQGDF="submitCQGDF"
      :cqgdfField="cqgdfField"
      :nowChecked="nowChecked"
      :cqgdfValue="cqgdfValue"
    ></addCQGDFComponents>

    <!-- 安置房执行新增 -->
    <addAZF
      :afzDialog="afzDialog"
      :activeMenu="activeMenu"
      :nowQLR="nowQLR"
      :nowNodeMsg="nowNodeMsg"
      @handleCloseAfzDialog="handleCloseAfzDialog"
      @handleSubmitAddAzf="handleSubmitAddAzf"
    ></addAZF>

    <!-- 回退数据弹窗 -->
    <dataBack :dataBackDialog="dataBackDialog" @handleCloseDataBack="handleCloseDataBack" :ruleTree="ruleTree"></dataBack>

    <!-- 界址线找回 -->
    <jzxBack :jzxBackDialog="jzxBackDialog" @handleCloseJzxBack="handleCloseJzxBack" :ruleTree="ruleTree"></jzxBack>
  </div>
</template>

<script setup lang="ts">
// 组件部分
import authImg from '@/components/authImg/index.vue';
import logList from '@/components/logList/index.vue';
import videoTemp from '@/components/videoTemp/index.vue';
import videoDefault from '@/components/videoDefault/index.vue';
import tuopuCheck from '@/components/tuopuCheck/index.vue';
import addCQGDFComponents from '@/components/addCQGDF/index.vue';
import addAZF from '@/components/addAZF/index.vue';
import dataBack from './dataBack.vue';
import jzxBack from './jzxBack.vue';
// 其他
import { getFileType } from '@/utils/publicFun';
import { getToken } from '@/utils/auth';
import { initAttr, isArray } from '@/utils/validate';
import { previewFile } from '@/api/esign';
import Axios from 'axios';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import { h } from 'vue';
import {
  spanRuleCenter,
  filterOption,
  formatDateTypeMonth,
  formatDateType,
  formatDateTypeYear,
  formatTime,
  formatDateYmdhm,
  getDirection,
  getLonAndLat
} from '@/utils/filters';

// 接口部分
import {
  selectParcelField,
  insertInstance,
  verificationFM,
  verificationFile,
  transitionOldKJ,
  findAsyncMsg as findAsyncMsgApi,
  findAsyncFileBrowser
} from '@/api/project';
import { saveSimple, selectIfOrder } from '@/api/project';
import { updateParcelFromMain } from '@/api/modal';
import { hasProperty } from '@/utils/index';
import { useUserStore } from '@/store/modules/user';
import { useProjectStore } from '@/store/modules/project';
import { useRoute } from 'vue-router';
const route = useRoute();
const userStore = useUserStore();
const projectStore = useProjectStore();
// --- 定义props --
const props = defineProps<{
  checkedLinye: any;
  attrbutionList: any;
  isMain: boolean;
  mainId: any;
  mainHeight: any;
  checkedNodeId: any;
  ruleTree: any;
  isIfrem: boolean;
}>();
const checkedLinyeCopy = computed(() => {
  return props.checkedLinye;
});
//  ---定义emit---
const emit = defineEmits<{
  (e: 'clearDeg'): void;
  (e: 'initDeg', point: any, fwj: any, url: any): void;
  (e: 'closeLinyeInfo'): void;
  (e: 'changeGraph', node: any): void;
  (e: 'speHightGrc', url: any): void;
  (e: 'clearSpeHightGrc'): void;
}>();

// --- 定义变量部分 --
const currentValue = ref(1);
const showTitle = ref('图形信息');

const baseUrl = import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/';
const base = import.meta.env.VITE_APP_BASE_API;
const nowChecked: any = ref({}); //当前选中的属性组
const nowSpeMsg: any = ref({}); //需要弹窗显示的特殊数据
const speMsgDialog = ref(false); //特殊数据弹窗
const speTitle = ref(''); //特殊数据弹窗标题
const videoDialog = ref(false); //视频播放弹窗
const checkedVideoUrl = ref(''); //当前选中的视频地址
const checkedVideId = ref(''); // 当前选中的视频id
const tableValue: any = ref({
  trs: [],
  tableTh: []
}); //表格弹窗需要显示的内容
const tableDialog = ref(false); //表格弹窗
const tableDialogTitle = ref(''); //表格弹窗标题
const detailValue: any = ref({}); //表格详情弹窗
const detailDialog = ref(false); //表格详情弹窗
const isActiveLog = ref(false); //是否选中宗地日志
const showBigDeg = ref(false); //是否展示方位角大图
const degUrl = ref(''); //方位角大图url
const fit = ref('contain'); //方位角大图fit
const loading = ref(false); //方位角大图loading
const showImgAttr: any = ref({
  pssb: {},
  pssj: {},
  fwj: {},
  wzxx: {},
  psdz: {},
  psry: {}
}); //图片属性
const showImgAttrSyCheckboox = ref(false); //展示水印选项
const fjDialog = ref(false); //查看附件弹窗
const fjUrl = ref(''); //查看附件url
const windowHeight = ref('600px'); //窗口高度
const docFile = ref(false); //是否是doc或者excel
const fileType = ref(1); // 1doc 2excel 3pdf或者txt
const vHtml = ref(''); //html内容
const workbook: any = ref({}); //工作簿
const excelData = ref([]); //excel数据
const errorDialog = ref(false); //纠错弹窗
const errorEdit = ref({
  groupId: '',
  time: 0,
  pageSize: 10,
  pageNum: 1
}); //数据纠错弹窗
const errorList = ref([]); //数据纠错列表
const thList = ref([]); //表头列表
const total = ref(0); //数据纠错总条数
const localIndex = ref(null); //本地筛选 字段下标(表头下标)
const localKeyWord = ref(''); //本地搜索key
const oldErrorList = ref([]); //请求的纠错数据 深拷贝
const oldTotal = ref(0); //数据纠错总条数
const localThList = ref([]); //本地允许筛选的列表 图片不让筛选
const isShowBigImg = ref(false); //点击图片是否能查看大图
const srcList = ref([]); //正常图片流列表
const syImgIndex = ref(0); //水印下标
const syLength = ref(0); //总图片数量
const fwjSrcList = ref([]); //方位角的图片流列表
const moduleId = ref(0); //模块id
const isEditAttr = ref(false); //是否编辑属性
const checkedAtt = ref(null); //字段对应的属性组
const allowEdit = ref(false); //是否允许编辑
const logName = ref('操作日志'); //日志类型名称 操作日志、拓扑检查日志
const headers = {
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
};
const token = getToken();
const active = ref(false); //图片边框
const verificationList = ref([]); //刷新表达式的列表
const expressList = ref([]); //表达式列表
const treeExpressList = ref([]); //树的表达式列表
const publicPlanDialog = ref(false); //刷新表达式进度弹窗
const publicMsg = ref(''); // 公共进度弹框的描述
const publicPrecentage = ref(0); // 公共弹框的进度值
const publicExpressList = ref([]); //通过后台得到的最终表达式顺序 所有数据都需要安装该顺序进行排序
const uploadSuccessCount = ref(0);
const linkTypeList = ref([1, 2, 3, 4]); //允许删除的属性组类型
const uploadCheckedDesignFile = ref(false); //上传表达式验证的弹框
const downLoadId = ref(undefined); //下载的数据id
const showVerify = ref(true); //展示验证弹框
const activeMenuLinkId = ref(null); //展示更多菜单选项
const showMoreMenu = ref(false); //展示更多菜单选项
const selectTitleList = ref([]);
const editExpressPower = ref(false); //编辑表达式字段权限 现只允许湖北交投编辑
const cqgdfDialog = ref(false); //超期过渡费设置弹窗
const cqgdfField = ref(null); //超期过渡费绑定的表格字段
const cqgdfValue = ref([]); //超期过渡费结果
const cqgdfTableList = ref([]); //超期过渡费展示 表格中
const cqgdfRangDate = ref([]); //新增超期过渡费的筛选时间
const addCqgdfTableList = ref([]); //新增超期过渡费的表格
const isAZF: any = ref(false); //是否是安置房
const activeMenu: any = ref({
  isChooseAZF: false
}); //当前显示的属性组
const visibleAddCQGDF = ref(false); //新增超期过渡费
const addCQGDFMsg = ref({}); //超期过渡费数据
const cqazHeight = ref(window.innerHeight - 300);
const afzDialog = ref(false); //新增安置房（执行相关）
const nowQLR = ref(null); //当前拆迁户权利人信息
const nowNodeMsg = ref(null); //当前选择节点信息
const cqgdfZfzd = ref([]); //超期过渡费支付的账单信息 用于比对
const showCQGDFTable = ref(false); //是否显示超期过渡费表格 用于判断切换的时候
const dataBackDialog = ref(false); //数据回退弹窗
const jzxBackDialog = ref(false); //界址线找回弹窗
const downLoadFileName = ref(); //下载的文件名
const logListRef = ref(null); //日志ref
const tuopuCheckRef = ref(null); //拓扑检查ref
const desginRef = ref(null); //上传设计文件ref
// --- computed --
const dataAttrLeft = computed(() => {
  return localStorage.getItem('dataAttrLeft') || 'null';
});
const dataAttrTop = computed(() => {
  return localStorage.getItem('dataAttrTop') || '24px';
});
const tableHeightMsg = computed(() => {
  return props.mainHeight - 260;
});

// 自动计算当前数字输入宽的可输入的长度
const getNumberMaxLength = computed(() => {
  return (zdItem: any) => {
    let intMax = 32;
    if (zdItem.field.attribution.numberType && (zdItem.field.attribution.numberType === 1 || zdItem.field.attribution.numberType === 2)) {
      intMax = zdItem.field.attribution.accuracy ? zdItem.field.attribution.accuracy : 32;
    } else if (zdItem.field.attribution.numberType && (zdItem.field.attribution.numberType === 3 || zdItem.field.attribution.numberType === 4)) {
      // 浮点数和双精度 的精度位数不包括小数点，我这里需要去精度位数+1
      intMax = zdItem.field.attribution.accuracy ? zdItem.field.attribution.accuracy + 1 : 32;
    }
    return intMax;
  };
});

// --- 定义方法 --

/**
 * 处理值是数组时显示
 * @param {any} value  值
 */
const handleArrArrayShow = (value: any) => {
  if (!value) return '';

  if (Object.prototype.toString.call(value) === '[object Array]') {
    const temp = value.length > 0 ? value.join(',') : '';
    return temp;
  }
  return value;
};

/**
 * 初始格式化时间选择
 * @param {string | null} value  值
 */
const formatDateRangeValue = (value: string | null) => {
  if (!value) return null;
  const dates = value.split('至');
  return dates.length === 2 ? dates : null;
};

/**
 * 格式时间
 * @param {string | null} value  值
 * @param {any} item
 */
const handleDateRangeUpdate = (value: string[], item: any) => {
  if (!value) {
    item.value = '';
    return;
  }
  item.value = value.join('至');
};
/**
 * 初始化赋值
 * @param attrbutionList 属性组列表
 */
const setSelectTitleList = (attrbutionList: any) => {
  const list = [];
  if (attrbutionList.length != 0) {
    const groupedArray = attrbutionList.reduce((accumulator, currentValue) => {
      accumulator[currentValue.linkId] = accumulator[currentValue.linkId] || [];
      accumulator[currentValue.linkId].push(currentValue);
      return accumulator;
    }, []);
    groupedArray.forEach((v) => {
      // 这里需要判断一下是不是安置房过来的 并且是isIfrem嵌入的 这时候代表是拆迁户选择安置房 需要把权利人带过来 初始化显示
      if (isAZF.value && props.isIfrem && localStorage.getItem('nowQLR') && v[0].typeName == '权利人') {
        const nowQLR = JSON.parse(localStorage.getItem('nowQLR'));
        list.push(nowQLR);
      } else {
        const item = {
          typeName: v[0].typeName,
          linkId: v[0].linkId,
          timeStamp: v[0].timeStamp,
          createTime: v[0].createTime,
          parcelLinkId: v[0].parcelLinkId,
          isCQGDFGroup: v[0].isCQGDFGroup || false,
          isChooseAZF: v[0].isChooseAZF || false,
          linkAttribution: v[0].linkAttribution,
          linkTimeStamp: v[0].linkTimeStamp,
          active: false,
          isChild: false,
          list: [],
          fieldList: []
        };
        item.active = false;
        if (v.length > 1) {
          item.isChild = true;
          const elist = [];
          v.forEach((k, kdx) => {
            const obj = {
              label: `${k.typeName}${kdx + 1}`,
              fieldList: k.fieldList,
              parcelLinkId: k.parcelLinkId,
              linkTimeStamp: k.linkTimeStamp,
              linkType: k.linkType,
              linkAttribution: k.linkAttribution
            };
            elist.push(obj);
          });
          item.list = elist;
        } else {
          item.isChild = false;
          item.fieldList = v[0].fieldList;
        }
        list.push(item);
      }
    });
    list.forEach((v) => {
      if (v.fieldList) {
        v.fieldList.forEach((k) => {
          if (k.type == 5) {
            //动植物识别
            const discernList = [];
            if (k.value) {
              // 先判断是不是数组 如果不是数组需要把json解析
              if (!isArray(k.value)) {
                k.value = JSON.parse(k.value);
              }
              k.value.forEach((o) => {
                const olist = Object.keys(o).sort();
                const obj: any = {};
                olist.forEach((q) => {
                  const index = q.lastIndexOf('_');
                  const ite = +q.substring(index + 1, q.length);
                  if (ite == 0) {
                    obj.animalPlantName = o[q];
                  } else if (ite == 1) {
                    obj.picUrl = o[q];
                  } else if (ite == 2) {
                    obj.type = o[q];
                  } else if (ite == 3) {
                    obj.intro = o[q];
                  }
                });
                discernList.push(obj);
              });
            }
            k.discernList = discernList;
          }
        });
      }
    });

    list.push({ typeName: '操作日志', linkId: -1, isLog: true });
    if (!route.query.isAZF && !route.query.iscq) {
      list.push({ typeName: '拓扑检查日志', linkId: -2, isLog: true });
    }
    list.push({ typeName: '表达式刷新异常', linkId: -3, isLog: true });
    showTitle.value = list[0].typeName;
    if (!list[0].isChild) {
      //不是子要素
      initDeg(list[0]);
    } else {
      //子要素处理
      list[0].list.forEach((v) => {
        initDeg(v);
      });
    }
    nowChecked.value = list[0];
    currentValue.value = 1;
    isActiveLog.value = false;
  }
  // 得到list 之后需要特殊处理一下超期过渡费 把超期过渡费放到最后面 然后单独弄成一行显示
  cqgdfTableList.value = [];
  list.forEach((v) => {
    if (v.fieldList && v.fieldList.length > 0) {
      v.fieldList.forEach((k) => {
        if (k.field && k.field.valueMethod == 'xttable' && k.field.attribution && k.field.attribution.openMonthArea) {
          //	 这里代表有超期过渡费
          cqgdfTableList.value.push(k);
          tableValue.value = k.value;
          // 组装trs
          const trs = [];
          if (k.value.tableTr) {
            k.value.tableTr.forEach((v) => {
              const tr = [];
              k.value.tableTh.forEach((k) => {
                const item = {
                  valueMethod: k.valueMethod,
                  value: v[k.fieldName],
                  type: undefined
                };
                if (k.valueMethod == 'date') {
                  //日期的还需要知道是什么类型 是年还是年月还是年月日
                  item.type = k.attribution.type;
                }
                tr.push(item);
              });
              trs.push(tr);
            });
          }
          tableValue.value.trs = trs;
          //  这俩不是新增超期过渡费需要的字段内容
          cqgdfField.value = k.field;
          cqgdfValue.value = k.value.tableTr;
        }
      });
    }
    // 得到超期过渡费支付账单数据
    if (v.typeName == '过渡费支付') {
      // 多采属性组 也需要判断是一个还是有多个的情况
      cqgdfZfzd.value = [];
      if (v.isChild) {
        //多个
        v.list.forEach((k) => {
          for (let i = 0; i < k.fieldList.length; i++) {
            if (k.fieldList[i].label == '账期') {
              cqgdfZfzd.value.push(k.fieldList[i].value);
              break;
            }
          }
        });
      } else {
        for (let i = 0; i < v.fieldList.length; i++) {
          if (v.fieldList[i].label == '账期') {
            if (v.fieldList[i].value) {
              cqgdfZfzd.value.push(v.fieldList[i].value);
            }
            break;
          }
        }
      }
    }
  });

  if (list.length != 0) {
    activeMenuLinkId.value = list[0].linkId;
    activeMenu.value = list[0];
    if (activeMenu.value.typeName == '超期过渡费') {
      showCQGDFTable.value = true;
    }
  }
  selectTitleList.value = list;

  selectTitleList.value.forEach((v) => {
    if (v.fieldList && isArray(v.fieldList)) {
      v.fieldList.forEach((k) => {
        if (k.type == 2) {
          //图片转译
          if (k.value && isArray(k.value)) {
            k.value.forEach((o) => {
              if (o.url && o.url.includes('http://123.60.37.168:9981')) {
                const list = o.url.split('/');
                const bucketName = list[list.length - 2];
                const sort_url = list[list.length - 1];
                o.url = `minio/${sort_url}`;
                o.bucketName = bucketName;
              }
            });
          }
        }
      });
    }
  });
  // 如果是超期过渡费需要从其他属性组拿到超期过渡费计算参数
  if (activeMenu.value.isCQGDFGroup) {
    setParameterToCQGDF(activeMenu.value);
  } else if (activeMenu.value.isChooseAZF) {
    //执行安置房的时候需要组装安置房数据
    setAZFData();
  }
};

/**
 * 获取图片地址
 * @param item
 * @returns
 */
const getSrc = (item: any) => {
  if (item.bucketName) {
    return `${base}/qjt/file/otherDownload/${item.url}?bucketName=${item.bucketName}&token=${token}`;
  } else {
    if (hasProperty(item, 'value')) {
      return `${base}/qjt/file/otherDownload/${item.value}?token=${token}`;
    }
    if (hasProperty(item, 'url')) {
      return `${base}/qjt/file/otherDownload/${item.url}?token=${token}`;
    }
  }
};

// 组装执行安置操作
const setAZFData = () => {
  // 首先判断 activeMenu.parcelLinkId 是否有数据 有数据代表是已有数据了 需要装起来
  const list = [];

  // 需要先判断是不是已经多采数据了
  if (activeMenu.value.isChild) {
    //已经多采了
    activeMenu.value.list.forEach((v) => {
      list.push({
        parcelLinkId: v.parcelLinkId,
        linkTimeStamp: v.linkTimeStamp,
        timeStamp: activeMenu.value.timeStamp,
        fieldList: v.fieldList,
        linkId: activeMenu.value.linkId,
        typeName: activeMenu.value.typeName
      });
    });
    // 还需要强行在外面配置一个fieldList 用于循环el-table-column
    activeMenu.value.fieldList = activeMenu.value.list[0].fieldList;
  } else {
    //还未多采
    if (activeMenu.value.parcelLinkId) {
      list.push({
        parcelLinkId: activeMenu.value.parcelLinkId,
        linkTimeStamp: this.activeMenu.linkTimeStamp,
        timeStamp: activeMenu.value.timeStamp,
        fieldList: activeMenu.value.fieldList,
        linkId: activeMenu.value.linkId,
        typeName: activeMenu.value.typeName
      });
    }
  }
  activeMenu.value.zxAzfList = list;
  // 需要先判断是否存了权利人信息
  for (let i = 0; i < selectTitleList.value.length; i++) {
    if (selectTitleList.value[i].typeName == '权利人') {
      nowQLR.value = selectTitleList.value[i];
      break;
    }
  }
};
/**
 * 切换属性组的时候初始化方位角
 * @param item
 */
const initDeg = (item: any) => {
  // 先清空 方位角信息
  emit('clearDeg');
  // 然后看是否有值，进行绘制
  item.fieldList.forEach((v: any) => {
    if (Array.isArray(v.value)) {
      v.value.forEach((k) => {
        if (k.fwj && k.fwj.value && k.wzxx && k.wzxx.value) {
          const point = k.wzxx.value.split(',');
          emit('initDeg', [point[1], point[0]], k.fwj.value, k.url);
        }
      });
    }
  });
};

/**
 * 把内容置空
 */
const setEmpty = () => {
  nowChecked.value = {};
};

/**
 * 关闭图形信息
 */
const closeInfo = () => {
  emit('closeLinyeInfo');
};
/**
 * 从其他属性组拿到超期过渡费需要的参数  【超期过渡费基本信息】属性组写死的
 * @param item
 */
const setParameterToCQGDF = (item: any) => {
  if (!item.parameOk) {
    //参数未组装完成需要组装
    const parame = {
      FWYSRQ: null, //房屋验收日期
      GDMJ: 0, //过渡面积
      GDQX: 0 //过渡期限
    };
    for (let i = 0; i < selectTitleList.value.length; i++) {
      if (selectTitleList.value[i].typeName.includes('超期过渡费')) {
        selectTitleList.value[i].fieldList.forEach((v) => {
          if (v.field.fieldName == 'FWYSRQ') {
            parame.FWYSRQ = v.value;
          }
          if (v.field.fieldName == 'GDMJ') {
            parame.GDMJ = v.value;
          }
          if (v.field.fieldName == 'GDQX') {
            parame.GDQX = v.value;
          }
        });
        break;
      }
    }
    item.parame = parame;
    item.parameOk = true;
  }
};
/**
 * 切换属性组
 * @param item
 */
const changeTab = (item: any) => {
  activeMenuLinkId.value = item.linkId;
  activeMenu.value = item;
  if (activeMenu.value.typeName == '超期过渡费') {
    showCQGDFTable.value = true;
  } else {
    showCQGDFTable.value = false;
  }
  // 如果是超期过渡费需要从其他属性组拿到超期过渡费计算参数
  if (item.isCQGDFGroup) {
    setParameterToCQGDF(item);
  } else if (item.isChooseAZF) {
    //执行安置房的时候需要组装安置房数据
    setAZFData();
  }
  showBigDeg.value = false;
  if (item.isLog) {
    //操作日志
    if (item.typeName == '操作日志') {
      logName.value = '操作日志';
      showLog();
    } else if (item.typeName == '拓扑检查日志') {
      //拓扑检查
      logName.value = '拓扑检查日志';
      showTuopuLog();
    } else if (item.typeName == '表达式刷新异常') {
      //表达式刷新异常
      logName.value = '表达式刷新异常';
      showRefreshLog();
    }
  } else {
    selectTitleList.value.forEach((v, idx) => {
      if (v.linkId == item.linkId) {
        nowChecked.value = v;
        if (v.fieldList) {
          v.fieldList.forEach((k) => {
            if (k.field && k.field.valueMethod == 'xttable' && k.field.attribution && k.field.attribution.openMonthArea) {
              //  这俩不是新增超期过渡费需要的字段内容
              cqgdfField.value = k.field;
              cqgdfValue.value = k.value.tableTr;
              initAddCqgdfTableList();
            }
          });
        }
        currentValue.value = idx + 1;
        if (!v.isChild) {
          //不是子要素
          initDeg(v);
        } else {
          //子要素
          v.list.forEach((k) => {
            initDeg(k);
          });
        }
      }
    });
    // 初始化编辑
    isEditAttr.value = false;
    isActiveLog.value = false;
    showBigDeg.value = false;
  }
};

/**
 * 切换属性
 * @param command
 */
const handleCommand = (command: any) => {
  selectTitleList.value.forEach((v, idx) => {
    if (v.linkId == command) {
      nowChecked.value.value = v;
      currentValue.value = idx + 1;
      if (!v.isChild) {
        //不是子要素
        initDeg(v);
      } else {
        //子要素
        v.list.forEach((k) => {
          initDeg(k);
        });
      }
      showTitle.value = v.typeName;
    }
  });
  // 初始化编辑
  isEditAttr.value = false;
  isActiveLog.value = false;
  showBigDeg.value = false;
};

/**
 * 上一个、下一个
 * @param val
 */
const jump = (val: any) => {
  if (val == 1) {
    currentValue.value--;
  } else {
    currentValue.value++;
  }
  nowChecked.value = selectTitleList.value[currentValue.value - 1];
  showTitle.value = selectTitleList.value[currentValue.value - 1].typeName;
  if (!selectTitleList.value[currentValue.value - 1].isChild) {
    //不是子要素
    initDeg(selectTitleList.value[currentValue.value - 1]);
  } else {
    //子要素
    selectTitleList.value[currentValue.value - 1].list.forEach((k) => {
      initDeg(k);
    });
  }
  isActiveLog.value = false;
  showBigDeg.value = false;
};

/**
 * 选中节点
 * @param node
 */
const checkedNode = (node: any) => {
  emit('changeGraph', node);
};

/**
 * 展开收起
 * @param item
 * @param type
 */
const changeShow = (item: any, type: any) => {
  if (type == 1) {
    //展开
    item.isShow = true;
    item.isEdit = false;
  } else {
    //收起
    item.isShow = false;
    item.isEdit = false;
  }
};

/**
 * 显示动物植物识别
 * @param item
 */
const showSpe = (item: any) => {
  nowSpeMsg.value = item;
  speTitle.value = item.animalPlantName;
  speMsgDialog.value = true;
};
/**
 * 关闭动物植物识别
 */
const handleClose = () => {
  speMsgDialog.value = false;
};

/**
 * 弹窗显示视频播放
 * @param url
 */
const showVideo = (url: any) => {
  checkedVideoUrl.value = url;
  checkedVideId.value = url;
  videoDialog.value = true;
};

/**
 * 关闭视频弹窗
 */
const closeVideoDialog = () => {
  videoDialog.value = false;
};

/**
 * 显示表格弹窗
 * @param val
 * @param title
 */
const showTable = (val: any, title: any) => {
  tableValue.value = val;
  tableDialogTitle.value = `${title}`;
  // 组装trs
  const trs = [];
  if (val.tableTr) {
    val.tableTr.forEach((v) => {
      const tr = [];
      val.tableTh.forEach((k) => {
        const item = {
          valueMethod: k.valueMethod,
          value: v[k.fieldName],
          type: undefined
        };
        if (k.valueMethod == 'date') {
          //日期的还需要知道是什么类型 是年还是年月还是年月日
          item.type = k.attribution.type;
        }
        tr.push(item);
      });
      trs.push(tr);
    });
  }
  tableValue.value.trs = trs;
  tableDialog.value = true;
};
/**
 * 关闭表格弹窗
 */
const handleCloseTable = () => {
  tableDialog.value = false;
};

/**
 * 得到具体应该展示的td内容
 * @param tr
 * @param tdx
 */
const getTr = (tr: any, tdx: any) => {
  // tr 对应行数据 tdx 对应对应的key下标
  const type = tableValue.value.tableTh[tdx].valueMethod;
  if (type == 'date') {
    //日期
    return formatDateType(tr[tableValue.value.tableTh[tdx].fieldName]);
  } else if (type == 'upload') {
    //图片
    return tr[tableValue.value.tableTh[tdx].fieldName];
  } else {
    return tr[tableValue.value.tableTh[tdx].fieldName];
  }
};
/**
 * 表格弹窗 图片、视频、指纹、签名等需要弹窗显示
 * @param val
 */
const showDetail = (val: any) => {
  if (val.valueMethod == 'xtfj' && Array.isArray(val.value)) {
    const list = [];
    val.value.forEach((v) => {
      const values = Object.values(v);
      const fileName = values[1] as string;
      const fjTypeIndex = fileName.lastIndexOf('.');
      const fjobj = {
        title: fileName,
        url: values[0],
        type: fileName.slice(fjTypeIndex + 1)
      };
      list.push(fjobj);
    });
    detailValue.value = {
      valueMethod: val.valueMethod,
      value: list
    };
  } else {
    if (val.valueMethod == 'upload') {
      detailValue.value = val;
      if (typeof detailValue.value == 'string' && detailValue.value != '') {
        detailValue.value = JSON.parse(detailValue.value);
      }
    } else {
      detailValue.value = val;
    }
  }
  detailDialog.value = true;
};

const handleCloseTableDetail = () => {
  detailDialog.value = false;
};
const showLog = () => {
  nowChecked.value = {
    typeName: '操作日志',
    linkId: props.mainId,
    isLog: true
  };

  isActiveLog.value = true;
  showBigDeg.value = false;
  logListRef.value.getLog(props.checkedNodeId);
  emit('clearDeg');
};
// 显示拓扑检查
const showTuopuLog = () => {
  nowChecked.value = {
    typeName: '拓扑检查日志',
    linkId: props.checkedNodeId,
    isTuopuLog: true
  };
  tuopuCheckRef.value.getLog(props.checkedNodeId);
};
/**
 * 显示表达式刷新异常
 */
const showRefreshLog = () => {
  nowChecked.value = {
    typeName: '表达式刷新异常',
    linkId: props.checkedNodeId,
    isExpressLog: true
  };
};
// 给父组件调用的
const showForMain = (val: any) => {
  nextTick(() => {
    nowChecked.value = {
      typeName: '操作日志',
      linkId: props.mainId,
      isLog: true
    };
  });
  isActiveLog.value = true;
  showBigDeg.value = false;
  logListRef.value.getLog(val);
  emit('clearDeg');
};
/**
 * 方位角图片特殊展示
 * @param url
 * @param fwj
 * @param wzxx
 * @param obj
 * @param idx
 * @param num
 */
const showDeg = (url: any, fwj: any, wzxx: any, obj: any, idx: any, num: any, list: any) => {
  // url地址重置下
  if (obj.bucketName) {
    url = `${base}/qjt/file/otherDownload/${obj.url}?bucketName=${obj.bucketName}&token=${token}`;
  } else {
    url = `${base}/qjt/file/otherDownload/${obj.url}?token=${token}`;
  }
  // url:地址 flg:是否是特殊的 idx下标 num总数量 list是当前图片列表
  syImgIndex.value = idx; //水印下标
  syLength.value = num; //总图片数量
  fwjSrcList.value = [];
  list.forEach((v) => {
    const item = {
      shortUrl: v.url,
      url: `${base}/qjt/file/otherDownload/${v.url}?token=${token}`,
      attr: v
    };
    fwjSrcList.value.push(item);
  });
  if (fwj && fwj.value && wzxx && wzxx.value) {
    degUrl.value = url;
    showBigDeg.value = true;
    emit('speHightGrc', obj.url);
    showImgAttr.value = obj;
  } else {
    degUrl.value = url;
    showBigDeg.value = true;
  }
};
const closeDeg = () => {
  showBigDeg.value = false;
  emit('clearSpeHightGrc');
};
/**
 * 查看附件dialog
 * @param obj
 */
const showFjDialog = (obj: any) => {
  if (!obj.type) {
    //现新增的然后 查看就没有类型 需要把类型先得出来
    let type = '';
    const index = obj.title.indexOf('.');
    type = obj.title.substring(index + 1, obj.title.length);
    obj.type = type;
  }
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  const windowHeight = window.innerHeight - 150 + 'px';
  if (obj.type == 'txt' || obj.type == 'pdf') {
    Axios({
      method: 'get',
      url: `${baseUrl}${obj.url}?att=1`,
      headers: {
        Authorization: 'Bearer ' + getToken(),
        'Access-Control-Allow-Origin': '*'
      },
      responseType: 'blob'
    }).then((res) => {
      let type = '';
      switch (obj.type) {
        case 'txt':
          type = 'text/plain';
          break;
        case 'xls':
          type = 'application/vnd.ms-excel';
          break;
        case 'xlsx':
          type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case 'doc':
          type = 'application/msword';
          break;
        case 'docx':
          type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          break;
        case 'pdf':
          type = 'application/pdf';
          break;
        default:
          break;
      }
      loading.close();
      fileType.value = 3;
      const blob = new Blob([res.data], { type: type });
      fjUrl.value = `${window.URL.createObjectURL(blob)}`;
      fjDialog.value = true;
    });
  } else if (obj.type == 'doc') {
    // 使用 previewFile 接口获取预览地址
    previewFile(obj.url, 'pdf')
      .then((res) => {
        if (res.status === 200) {
          loading.close();
          fileType.value = 3; // 统一使用 iframe 预览
          const blob = new Blob([res.data], { type: 'application/pdf' });
          fjUrl.value = URL.createObjectURL(blob);
          fjDialog.value = true;
        } else {
          loading.close();
          ElMessage.error(res.msg || '预览失败');
        }
      })
      .catch((error) => {
        loading.close();
        ElMessage.error('预览失败：' + error.message);
      });
  } else if (obj.type == 'docx') {
    const xhr = new XMLHttpRequest();
    xhr.open('get', `${baseUrl}${obj.url}?att=1`, true);
    xhr.responseType = 'arraybuffer';
    xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());
    xhr.onload = function () {
      if (xhr.status == 200) {
        let flg = true;
        if (xhr.response.byteLength == 0) {
          //空文件 word 不允许打开
          ElMessageBox.alert('您上传的word文件为空文件，不允许打开！！！', '错误信息', {
            confirmButtonText: '确定',
            callback: (action) => {
              loading.close();
              flg = false;
            }
          });
        }
        if (!flg) {
          return;
        }
        mammoth.convertToHtml({ arrayBuffer: new Uint8Array(xhr.response) }).then(function (resultObject) {
          nextTick(() => {
            fileType.value = 1;
            loading.close();
            fjDialog.value = true;
            vHtml.value = resultObject.value;
          });
        });
      }
    };
    xhr.send();
  } else {
    const xhr = new XMLHttpRequest();
    xhr.open('get', `${baseUrl}${obj.url}?att=1`, true);
    xhr.responseType = 'arraybuffer';
    xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());
    xhr.onload = function () {
      if (xhr.status == 200) {
        fileType.value = 2;
        const data = new Uint8Array(xhr.response);
        try {
          const workbookTemp = XLSX.read(data, { type: 'array' });
          loading.close();
          const sheetNames = workbookTemp.SheetNames; // 工作表名称集合
          workbook.value = workbookTemp;
          getTable(sheetNames[0]);
        } catch (error) {
          loading.close();
          ElMessageBox.alert('【文件存在异常，无法打开】', '错误信息', {
            confirmButtonText: '确定',
            callback: (action) => {}
          });
        }
      }
    };
    xhr.send();
  }
};

const handleCloseFj = () => {
  fjDialog.value = false;
};
const getTable = (sheetName: any) => {
  const worksheet = workbook.value.Sheets[sheetName];
  // 设置header: 1表示将第一行作为表头
  excelData.value = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  // 分离表头和数据
  if (excelData.value.length > 0) {
    const headers = excelData.value[0]; // 获取第一行作为表头
    const rows = excelData.value.slice(1); // 获取剩余行作为数据
    // 将数据转换为对象数组
    excelData.value = rows.map((row) => {
      const obj = {};
      headers.forEach((header: string, index: number) => {
        if (row[index]) {
          obj[header] = row[index];
        }
      });
      return obj;
    });
  }
  fjDialog.value = true;
};

/**
 * 数据纠错
 */
const editData = () => {
  errorList.value = [];
  thList.value = [];
  localThList.value = [];
  errorDialog.value = true;
};
const handleCloseErr = () => {
  errorDialog.value = false;
};
const editField = () => {
  errorEdit.value.pageNum = 1;
  errorEdit.value.pageSize = 10;
  getErrorData();
};
const getErrorData = () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  selectParcelField(errorEdit.value).then((res) => {
    loading.close();
    if (res.code == 200) {
      if (res.data.list.length != 0) {
        const list = initAttr(res.data.list, checkedLinyeCopy.value.fieldGroupModels);
        thList.value = [];
        localThList.value = [];
        list[0].fieldList.forEach((v) => {
          thList.value.push({ label: v.label });
          if (v.type == 1) {
            localThList.value.push({ label: v.label });
          }
        });
        errorList.value = list;
        oldErrorList.value = JSON.parse(JSON.stringify(list));
        total.value = res.data.total;
        oldTotal.value = res.data.total;
        localKeyWord.value = '';
        localIndex.value = null;
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 更新属性组
 * @param row
 */
const updateAttr = (row: any) => {
  ElMessageBox.confirm('确定要更新为该属性组吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const parmas = {
        instanceId: row.id,
        linkId: row.linkId,
        parcelId: checkedLinyeCopy.value.id,
        timeStamp: row.timeStamp
      };
      const loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      insertInstance(parmas).then((res) => {
        loading.close();
        if (res.code == 200) {
          ElMessage({
            type: 'success',
            message: '更新成功'
          });
          errorDialog.value = false;
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};
const handleSizeChange = (val: any) => {
  errorEdit.value.pageNum = 1;
  errorEdit.value.pageSize = val;
  getErrorData();
};
const handleCurrentChange = (val: any) => {
  errorEdit.value.pageNum = val;
  getErrorData();
};
/**
 * 本地搜索
 */
const localSearch = () => {
  if (localIndex.value == null && localIndex.value != 0) {
    ElMessage.error('请选择需要筛选的字段');
    return;
  }
  const itemList = JSON.parse(JSON.stringify(oldErrorList.value));
  const endList = [];
  itemList.forEach((v) => {
    const flg = v.fieldList[localIndex.value].value.includes(localKeyWord.value);
    if (flg) {
      endList.push(v);
    }
  });
  errorList.value = endList;
  if (localKeyWord.value) {
    total.value = endList.length;
  } else {
    total.value = oldTotal.value;
  }
};
// 表达式验证
const verification = () => {
  ElMessageBox.prompt('请输入验证的表达式', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  })
    .then(({ value }) => {
      if (value) {
        const parmas = {
          expression: value.trim(),
          parcelId: checkedLinyeCopy.value.id,
          positionId: props.checkedNodeId
        };
        verificationFM(parmas).then((res) => {
          if (res.code == 200) {
            ElNotification({
              title: '验证结果',
              message: res.data,
              duration: 0
            });
          } else {
            ElNotification({
              title: '验证结果',
              message: res.msg,
              duration: 0
            });
          }
        });
      }
    })
    .catch(() => {});
};
/**
 * 图片组件传过来的图片
 * @param type
 * @param url
 * @param idx
 * @param obj
 * @param shortUrl
 */
const changeSrcList = (type: any, url: any, idx: any, obj: any, shortUrl: any) => {
  //type 1代表是需要看大图 2代表是有位置信息那种 需要清除大图列表 idx对应下标
  if (type == 1) {
    srcList.value.splice(idx, 0, url);
    fwjSrcList.value = [];
  } else if (type == 2) {
    const item = {
      shortUrl: shortUrl,
      url: url,
      attr: obj
    };
    fwjSrcList.value.splice(idx, 0, item);
    srcList.value = [];
  }
};

/**
 * 切换有方位角图片上下一张
 * @param type
 */
const syBtn = (type: any) => {
  //1上一张 2下一张
  if (type == 1) {
    //上一张
    syImgIndex.value--;
  } else {
    //下一张
    syImgIndex.value++;
  }
  degUrl.value = fwjSrcList.value[syImgIndex.value].url;
  if (fwjSrcList.value[syImgIndex.value].attr.fwj) {
    //有方位角的
    emit('speHightGrc', fwjSrcList.value[syImgIndex.value].shortUrl);
    showImgAttr.value = fwjSrcList.value[syImgIndex.value].attr;
  }
};
/**
 * 图片组件传过来需要高亮的方位角
 */
const highlightFWJ = (url: any) => {
  emit('speHightGrc', url);
};

/**
 * 转换老勘界
 */
const transitionKJ = () => {
  transitionOldKJ(checkedLinyeCopy.value.id).then((res) => {
    if (res.code == 200) {
      ElMessage({
        type: 'success',
        message: '转换成功'
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const handleCommandYZ = (command: any) => {
  if (command == 1) {
    verification();
  } else if (command == 2) {
    transitionKJ();
  } else if (command == 3) {
    //标注
  } else if (command == 4) {
    //编辑属性
    // 需要通过nowChecked的linkid找到原详情里面的属性组，然后再fieldGroupModels里面通过linkid找原属性组 组装修改数据内容
    isEditAttr.value = true;
  } else if (command == 5) {
    //刷新表达式
    refreshExpression();
  } else if (command == 6) {
    // 上传的表达式验证的方式为文件验证
    uploadCheckedDesignFile.value = true;
  } else if (command == 7) {
    // 数据回退
    dataBackDialog.value = true; //数据回退
  } else if (command == 8) {
    // 界址线找回
    jzxBackDialog.value = true; //界址线找回
  }
};
const handleCloseDataBack = () => {
  dataBackDialog.value = false;
};

// 关闭上传设计文件
const handleCloseDesign = () => {
  uploadCheckedDesignFile.value = false;
};
// 上传设计文件
const submitDesign = () => {
  desginRef.value.submit();
};
//循环获取进度条，当进度条==1的时候停止调用
const findAsyncMsg = (id: any) => {
  const parmas = {
    id: id
  };
  findAsyncMsgApi(parmas).then((res) => {
    if (res.code == 200) {
      if (!res.data) {
        ElMessageBox.alert('数据异常，请重试！！！', '错误提示', {
          confirmButtonText: '确定'
        });
        return;
      }
      if (parseFloat(res.data.progress) != 1) {
        if (res.data.status == -1) {
          ElMessage.error(res.data.result);
        } else {
          setTimeout(() => {
            findAsyncMsg(id);
          }, 1000);
        }
      } else if (parseFloat(res.data.progress) == 1) {
        if (res.data.path) {
          downLoadFileName.value = res.data.path;
        }
        generalDown();
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};
//通用下载
const generalDown = () => {
  const index = downLoadFileName.value.lastIndexOf('.');
  const name = downLoadFileName.value.substring(0, index);
  const parmas = {
    id: downLoadId.value,
    fileName: `${name}.zip`
  };
  findAsyncFileBrowser(parmas).then((res) => {
    if (res.data.type == 'application/json') {
      //导出异常
      const read = new FileReader();
      read.readAsText(res.data, 'utf-8');
      let bugMsg = '';
      read.onload = (data) => {
        bugMsg = JSON.parse(data.currentTarget['result']).msg;
        ElMessage.error(JSON.parse(data.currentTarget['result']).msg);
        if (!bugMsg) {
          bugMsg = '未知异常';
        }
      };
    } else {
      //导出正常
      const name = decodeURI(res.headers['content-disposition']);
      const index = name.indexOf('=');
      const endFileName = name.substring(index + 1, name.length) || downLoadFileName.value;
      const blob = new Blob([res.data], { type: 'aplication/zip' });
      // 针对ie浏览器
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveOrOpenBlob(blob, downLoadFileName.value);
      } else {
        //非ie浏览器
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob); //常见下载的链接
        downloadElement.href = href;
        downloadElement.download = endFileName; //下载后文件名
        document.body.appendChild(downloadElement);
        downloadElement.click(); //点击下载
        document.body.removeChild(downloadElement); //下载完成移除元素
        window.URL.revokeObjectURL(href); //释放blob对象
      }
    }
  });
};
/**
 *  编辑要素
 */
const handleSuccessDesgin = (response: any, file: any, fileList: any) => {
  const path = response.data[0].path;
  const name = response.data[0].name;
  const parmas = { parcelId: checkedLinyeCopy.value.id, positionId: props.checkedNodeId, obsPath: path, fileName: name };
  verificationFile(parmas).then((res) => {
    if (res.code == 200) {
      downLoadId.value = res.data.id;
      downLoadFileName.value = file.name;
      findAsyncMsg(res.data.id);
    } else {
      ElNotification({
        title: '验证结果',
        message: res.msg,
        duration: 0
      });
    }
  });
  desginRef.value.clearFiles();
  uploadCheckedDesignFile.value = false;
};
const editAtrr = async () => {
  const code = await getAllowUpdate();
  if (code < 0) {
    ElMessageBox.alert('请对表达式进行排序后再修改数据！！！', '提示', {
      confirmButtonText: '确定',
      callback: (action: any) => {}
    });
    return;
  }
  nowChecked.value.fieldList.forEach((v, vdx) => {
    if (v.field) {
      if (
        ['upload', 'xtqm', 'xtzw', 'xtsjjt'].includes(v.field.valueMethod) ||
        (v.field.valueMethod == 'idCardScan' && (v.field_index == '8' || v.field_index == '9'))
      ) {
        //图片类型或者身份证识别的正反面图片 并且没有处理过才执行
        if (Array.isArray(v.value)) {
          v.fieldList = [];
          v.value.forEach((k) => {
            if (k.url) {
              v.fieldList.push({
                url: `${base}/qjt/file/otherDownload/${k.url}?token=${token}`,
                oldUrl: k.url
              });
            }
          });
        }
      } else if (v.field.valueMethod == 'checkbox' || v.field.valueMethod == 'cascader') {
        //多选框处理
        if (v.value && !isArray(v.value)) {
          v.value = v.value.split(',');
        }
      }
    }
  });

  isEditAttr.value = true;
};
/**
 * 取消编辑
 */
const cancatEditAttr = () => {
  //  这里取消需要处理值---
  isEditAttr.value = false;
};
/**
 * 保存编辑的属性组
 */
const saveEditAttr = () => {
  const item = getJDData([checkedLinyeCopy.value], props.checkedNodeId);
  const fieldInstance: any = {};
  for (let i = 0; i < item.fieldGroupModels.length; i++) {
    if (item.fieldGroupModels[i].linkId == nowChecked.value.linkId) {
      fieldInstance.attribution = {};
      fieldInstance.groupId = item.fieldGroupModels[i].id;
      if (nowChecked.value.parcelLinkId) {
        fieldInstance.parcelLinkId = nowChecked.value.parcelLinkId;
      }
      fieldInstance.linkId = item.fieldGroupModels[i].linkId;
      fieldInstance.timeStamp = nowChecked.value.timeStamp || 0;
      fieldInstance.appId = 0;
      fieldInstance.linkTimeStamp = nowChecked.value.linkTimeStamp || undefined;
      break;
    }
  }
  nowChecked.value.fieldList.forEach((v: any) => {
    if (v.field) {
      if (v.field.valueMethod == 'upload' && Array.isArray(v.value)) {
        //图片需要处理方位角等信息
        const value: any = [];
        v.value.forEach((k) => {
          const ite: any = { url: k.url };
          if (k.fwj) {
            ite.fwj = k.fwj.value;
          }
          if (k.psdz) {
            ite.psdz = k.psdz.value;
          }
          if (k.psry) {
            ite.psry = k.psry.value;
          }
          if (k.pssb) {
            ite.pssb = k.pssb.value;
          }
          if (k.pssj) {
            ite.pssj = k.pssj.value;
          }
          if (k.wzxx) {
            ite.wzxx = k.wzxx.value;
          }
          value.push(ite);
        });
        fieldInstance.attribution[v.field.fieldName] = value;
      } else if (v.field.valueMethod == 'idCardScan') {
        //处理权利人
        if (v.field_index == '8' || v.field_index == '9') {
          //身份证正反面需要处理
          if (Array.isArray(v.value) && v.value.length != 0) {
            fieldInstance.attribution[`${v.field.fieldName}`] = v.value[0].url;
          }
        } else {
          fieldInstance.attribution[`${v.field.fieldName}`] = v.value;
        }
      } else if (v.field.valueMethod == 'select') {
        //下拉的需要反编译为value传给数据库
        // value 可能是vlaue也可能是label
        let ite_value = v.value;
        for (let i = 0; i < v.field.attribution.options.length; i++) {
          if (v.field.attribution.options[i].label == v.value) {
            ite_value = v.field.attribution.options[i].value;
            break;
          }
        }
        if (ite_value !== undefined && ite_value !== null && ite_value !== '') {
          fieldInstance.attribution[v.field.fieldName] = ite_value;
        } else {
          fieldInstance.attribution[v.field.fieldName] = undefined;
        }
      } else if (v.field.valueMethod == 'xtqm' || v.field.valueMethod == 'xtzw' || v.field.valueMethod == 'xtsjjt') {
        //签名指纹 特殊处理
        let value = undefined;
        if (Array.isArray(v.value) && v.value.length != 0) {
          value = v.value[0].url;
        }
        fieldInstance.attribution[v.field.fieldName] = value;
      } else if (v.field.valueMethod == 'checkbox' || v.field.valueMethod == 'cascader') {
        //多选处理
        let ite_value = undefined;
        if (Array.isArray(v.value) && v.value.length != 0) {
          const list = JSON.parse(JSON.stringify(v.value));
          ite_value = list.join(',');
          v.value = list.join(',');
        }
        fieldInstance.attribution[v.field.fieldName] = ite_value;
      } else if (v.field.valueMethod == 'xttable') {
        //表格处理
        fieldInstance.attribution[v.field.fieldName] = JSON.stringify(v.value.tableTr);
      } else if (v.field.valueMethod == 'xtfj') {
        //附件也需要特殊处理
        const value = [];
        if (v.value && isArray(v.value)) {
          v.value.forEach((k) => {
            const it = {};
            it[`${v.field.fieldName}_0`] = k.url;
            it[`${v.field.fieldName}_1`] = k.title;
            value.push(it);
          });
          fieldInstance.attribution[v.field.fieldName] = value;
        }
      } else {
        if (v.value !== undefined && v.value !== null && v.value !== '') {
          fieldInstance.attribution[v.field.fieldName] = v.value;
        } else {
          fieldInstance.attribution[v.field.fieldName] = undefined;
        }
      }
    }
  });
  // 如果 linkType=3 代表是线共享 需要绑定之前的选择的线数据linkAttribution
  fieldInstance.linkAttribution = nowChecked.value.linkAttribution;
  item.timeTamp = getTimeStamp([checkedLinyeCopy.value]) || 0;
  item.fieldInstanceModels = [fieldInstance];
  delete item.fieldGroupModels;
  saveSimple([item]).then((res) => {
    if (res.code == 200) {
      nowChecked.value.parcelLinkId = res.data[0].fieldInstanceModels[0].parcelLinkId;
      nowChecked.value.timeStamp = res.data[0].fieldInstanceModels[0].timeStamp;
      setNodeTimeStamp([checkedLinyeCopy.value], res.data[0].timeTamp, res.data[0].id);
      isEditAttr.value = false;
      // 需要查询接口用于判断是否后台会刷新表达式
      selectIfOrder({ moduleId: moduleId.value }).then((res) => {
        if (res.code == 200) {
          if (res.data == 1) {
            //-1 ： 需要排序 0：不用弹窗  1：刷新表达式中，请稍后查看
            ElMessageBox.alert('刷新请求已发送成功（不刷新子要素），请稍后查看', '提示', {
              confirmButtonText: '确定',
              callback: (action: any) => {
                // setTimeout(() => {
                //   // 在这添加是否切换公司的标识。
                //   // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
                //   // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
                //   sessionStorage.setItem('qiehuan_company', false);
                //   location.reload();
                // }, 1000);
              }
            });
          } else if (res.data == -1) {
            ElMessageBox.alert('您有表达式字段，但是未排序，所以不会对表达式字段进行更新！！！', '提示', {
              confirmButtonText: '确定',
              callback: (action: any) => {}
            });
          }
        } else {
          ElMessage.error(res.msg);
        }
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 通过实例id在得到的详情数据里面找到对应节点的对象
 * @param tree 详情数据
 * @param ruleId 实例id
 * @returns 对应节点的对象
 */
const getJDData = (tree: any, ruleId: any) => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id === ruleId) {
      // 找到匹配的节点，返回 name
      const item = {
        appType: 2,
        parcelName: tree[i].parcelName,
        id: ruleId,
        ruleId: tree[i].ruleId,
        fieldInstanceModels: [],
        ruleAttribution: tree[i].ruleAttribution || null,
        fieldGroupModels: getFieldGroupModels(props.ruleTree, tree[i].ruleId) // 需要去规则树里面找属性组列表 实例数据已经不反悔属性组列表了
        // tree[i].fieldGroupModels
      };
      return item;
    }
    if (tree[i].list && tree[i].list.length > 0) {
      // 递归查找子节点
      const result = getJDData(tree[i].list, ruleId);
      if (result) {
        // 如果在子节点中找到，返回结果
        return result;
      }
    }
  }
  // 如果没有找到匹配的节点，返回 null 或其他默认值
  return;
};

const handleCommandRZ = (command: any) => {
  if (command == 1) {
    logName.value = '操作日志';
    showLog();
  } else if (command == 2) {
    //拓扑检查
    logName.value = '拓扑检查日志';
    showTuopuLog();
  }
};

// 图片上传成功事件
const handleSuccessTP = (response: any, item: any) => {
  if (!item.value) {
    item.value = [];
  }
  item.value.push({
    url: response.data[0].path,
    name: response.data[0].name
  });
  item.fieldList.push({
    oldUrl: response.data[0].path,
    url: `${base}/qjt/file/otherDownload/${response.data[0].path}?token=${token}`
  });
};

// 图片上传移除事件
const handleRemoveTP = (file: any, item: any) => {
  let num = 0;
  for (let index = 0; index < item.value.length; index++) {
    if (file.response) {
      //代表是新上传的图片
      if (file.response.data[0].path == item.value[index].url) {
        num = index;
        break;
      }
    } else {
      //代表是老图片
      if (file.oldUrl == item.value[index].url) {
        num = index;
        break;
      }
    }
  }
  item.value.splice(num, 1);
  item.fieldList.splice(num, 1);
};
/**
 * 图片上传超过最大限制提示
 * @param files 文件列表
 * @param fileList 文件列表
 * @param item 当前项
 */
const handleExceed = (files: any, fileList: any, item: any) => {
  if (item.field.valueMethod == 'idCardScan') {
    //身份证识别的图片单独处理
    ElMessage.error('身份证识别正反面只允许上传一张！！！');
  } else {
    if (fileList.length >= item.field.attribution.picNum) {
      ElMessage.error(`${item.field.fieldCn}最多允许上传${item.field.attribution.picNum}`);
    }
  }
};

// 获取大图列表
const getPreview = (list: any) => {
  if (Array.isArray(list)) {
    const item = [];
    list.forEach((v) => {
      if (!v.fwj || !v.fwj.isSy) {
        //没有方位角才加放大图片
        item.push(`${base}/qjt/file/otherDownload/${v.url}?token=${token}`);
      }
    });
    return item;
  }
  return [];
};
/**
 * 有方位角 位置信息的图片鼠标移入事件
 * @param ite 当前项
 */
const handleEnter = (ite: any) => {
  if (ite.fwj && ite.fwj.value && ite.wzxx && ite.wzxx.value) {
    emit('speHightGrc', ite.url);
    ite.active = true;
  }
};

/**
 * 鼠标离开事件
 * @param ite 当前项
 */
const handleLeave = (ite: any) => {
  ite.active = false;
};
/**
 * 处理数字输入框中的内容--使用正则来匹配内容
 * @param val 当前值
 * @param field 当前项
 * @param zditem 当前项
 */
const handleNumberInput = (val: any, field: any, zditem: any) => {
  // 正则表达式，匹配最多5位整数和2位小数
  const intMax = field.attribution.accuracy ? field.attribution.accuracy : 32;
  const decMax = field.attribution.precision ? field.attribution.precision : 15;
  let regex = new RegExp(`^(-?\\d{0,${intMax}})(\\.\\d{0,${decMax}})?$`);
  if (field.attribution.numberType && (field.attribution.numberType === 1 || field.attribution.numberType === 2)) {
    regex = new RegExp(`^\d{1,${intMax}}$|^$`);
  } else {
    regex = new RegExp(`^(-?\\d{0,${intMax}})(\\.\\d{0,${decMax}})?$`);
  }
  // 检查输入是否符合正则表达式
  if (!regex.test(val)) {
    // 不符合规则，修正输入值
    let correctedValue = val.replace(/[^0-9.-]/g, ''); // 移除非数字和非小数点字符
    correctedValue = correctedValue.replace(/(\..*)\./g, '$1'); // 移除多余的点
    correctedValue = correctedValue.replace(/^-/, ''); // 移除多余的负号
    // 修正整数部分
    const integerPart = correctedValue.split('.')[0];
    correctedValue = `${integerPart.slice(0, integerPart.length)}.${correctedValue.includes('.') ? correctedValue.split('.')[1] : ''}`;

    //修正小数部分
    const decimalPart = correctedValue.split('.')[1] || '';
    correctedValue = `${correctedValue.split('.')[0]}${decimalPart ? '.' + decimalPart.slice(0, decMax) : ''}`;

    // 最终修正值
    zditem.value = correctedValue;
  }
};
/**
 * 处理日期时间戳的长度
 * @param val 当前值
 * @param field 当前项
 * @param zditem 当前项
 */
const handleChangeDateRange = (val: any, field: any, zditem: any) => {
  if (Array.isArray(val) && val.length > 0) {
    const result = val.join('至');
    zditem.value = result;
  }
};
/**
 * 设置当前的属性组的内容
 * @param val 当前值
 * @param field 当前项
 * @param zditem 当前项
 */
const handleCheckBoxValue = (val: any, field: any, zditem: any) => {};
/**
 * 刷新表达式
 */
const refreshExpression = () => {
  ElMessageBox({
    title: '提示',
    message: h('p', null, [h('span', null, '该操作会查询本数据下的所有表达式字段，确定要执行刷新操作吗？')]),
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    verificationList.value = [];
  });
};
/**
 * 迭代原始树表达式
 * @param list 原始树表达式
 * @param mainId 最顶级id
 */
const getTreeExpress = (list: any, mainId: any) => {
  list.forEach((v: any) => {
    v.fieldGroupModelList.forEach((k: any) => {
      if (!k.ruleAttribution && k.groupScope == 2) {
        k.fieldModelList.forEach((q) => {
          if (q.attribution && q.attribution.expression) {
            //代表是表达式的
            const obj = {
              mainId: mainId, //最顶级id
              parcelId: v.id, //节点实例id
              linkId: k.linkId, //关联id
              groupName: k.typeName,
              fieldModel: q,
              ruleId: v.id,
              groupId: k.id
            };
            treeExpressList.value.push(obj);
          }
        });
      }
    });
    if (v.list) {
      getTreeExpress(v.list, mainId);
    }
  });
};
/**
 * 迭代获取树的所有表达式
 * @param list 树
 * @param mainId 最顶级id
 */
const getAllFiledExpressFromOne = (list: any, mainId: any) => {
  list.forEach((v: any) => {
    v.fieldGroupModels.forEach((k: any) => {
      if (!k.ruleAttribution && k.groupScope == 2) {
        k.fieldModelList.forEach((q) => {
          if (q.attribution && q.attribution.expression && !q.attribution.isOnceRefeshExpression) {
            //代表是表达式的
            const obj = {
              mainId: mainId, //最顶级id
              parcelId: v.id, //节点实例id
              linkId: k.linkId, //关联id
              groupName: k.typeName,
              fieldModel: q,
              groupId: k.id
            };
            expressList.value.push(obj);
          }
        });
      }
    });
    if (v.list) {
      getAllFiledExpressFromOne(v.list, mainId);
    }
  });
};

/**
 * 最终通过数据组装表达式集合
 * @param item 当前数据
 * @param mainId 最顶级id
 * @param list 表达式列表
 */
const getEndOneExpressData = (item: any, mainId: any, list: any) => {
  item.forEach((v: any) => {
    publicExpressList.value.forEach((k: any) => {
      if (v.geomArcgis && k.ruleId == v.ruleId) {
        //当前数据的ruleid和公共表达式列表相等
        const obj = JSON.parse(JSON.stringify(k));
        obj.parcelId = v.id;
        obj.mainId = mainId;
        list.push(obj);
      }
    });
    if (v.list && v.list.length != 0) {
      getEndOneExpressData(v.list, mainId, list);
    }
  });
};

/**
 * 右键复制
 * @param item 当前数据
 */
const copyText = (item: any) => {
  let text = '';
  if (item.field.attribution.expression) {
    text = item.field.attribution.expression;
  } else {
    text = item.label;
  }
  try {
    const tag = document.createElement('textarea'); // create textarea标签，注意：创建input标签则不会换行
    document.body.appendChild(tag); // 添加到body中
    tag.value = text; // 给textarea设置value属性为需要copy的内容
    tag.select(); // 选中
    document.execCommand('copy', false); // copy已经选中的内容
    ElMessage({
      message: '复制成功',
      type: 'success'
    });
    tag.remove();
  } catch (err) {}
};
/**
 * 改变级联
 * @param val 当前值
 * @param item 当前项
 */
const handleChange = (val: any, item: any) => {};
/**
 * 编辑子要素
 * @param item 当前项
 */
const editChild = async (item: any) => {
  const code = await getAllowUpdate();
  if (code < 0) {
    ElMessageBox.alert('请对表达式进行排序后再修改数据！！！', '提示', {
      confirmButtonText: '确定',
      callback: (action: any) => {}
    });
    return;
  }
  item.fieldList.forEach((v: any) => {
    if (v.field) {
      if (
        ['upload', 'xtqm', 'xtzw', 'xtsjjt'].includes(v.field.valueMethod) ||
        (v.field.valueMethod == 'idCardScan' && (v.field_index == '8' || v.field_index == '9'))
      ) {
        //图片类型或者身份证识别的正反面图片 并且没有处理过才执行
        if (Array.isArray(v.value)) {
          v.fieldList = [];
          v.value.forEach((k) => {
            if (k.url) {
              v.fieldList.push({
                url: `${base}/qjt/file/otherDownload/${k.url}?token=${token}`,
                oldUrl: k.url
              });
            }
          });
        }
      } else if (v.field.valueMethod == 'checkbox' || v.field.valueMethod == 'cascader') {
        //多选框处理
        if (v.value && !isArray(v.value)) {
          v.value = v.value.split(',');
        }
      }
    }
  });
  item.isEdit = true;
};

/**
 * 保存子要素
 * @param obj 当前项
 * @param index 当前索引
 */
const saveChild = (obj: any, index: any) => {
  const item = getJDData([checkedLinyeCopy.value], props.checkedNodeId);
  const fieldInstance: any = {};
  for (let i = 0; i < item.fieldGroupModels.length; i++) {
    if (item.fieldGroupModels[i].linkId == nowChecked.value.linkId) {
      fieldInstance.attribution = {};
      fieldInstance.groupId = item.fieldGroupModels[i].id;
      if (obj.parcelLinkId) {
        fieldInstance.parcelLinkId = obj.parcelLinkId;
      }
      fieldInstance.linkId = item.fieldGroupModels[i].linkId;
      fieldInstance.timeStamp = nowChecked.value.timeStamp || 0;
      fieldInstance.appId = 0;
      fieldInstance.linkTimeStamp = obj.linkTimeStamp || undefined;
      break;
    }
  }
  obj.fieldList.forEach((v: any) => {
    if (v.field) {
      if (v.field.valueMethod == 'upload' && Array.isArray(v.value)) {
        //图片需要处理方位角等信息
        const value = [];
        v.value.forEach((k) => {
          const ite: any = { url: k.url };
          if (k.fwj) {
            ite.fwj = k.fwj.value;
          }
          if (k.psdz) {
            ite.psdz = k.psdz.value;
          }
          if (k.psry) {
            ite.psry = k.psry.value;
          }
          if (k.pssb) {
            ite.pssb = k.pssb.value;
          }
          if (k.pssj) {
            ite.pssj = k.pssj.value;
          }
          if (k.wzxx) {
            ite.wzxx = k.wzxx.value;
          }
          value.push(ite);
        });
        fieldInstance.attribution[v.field.fieldName] = value;
      } else if (v.field.valueMethod == 'idCardScan') {
        //处理权利人
        if (v.field_index == '8' || v.field_index == '9') {
          //身份证正反面需要处理
          if (Array.isArray(v.value) && v.value.length != 0) {
            fieldInstance.attribution[`${v.field.fieldName}`] = v.value[0].url;
          }
        } else {
          fieldInstance.attribution[`${v.field.fieldName}`] = v.value;
        }
      } else if (v.field.valueMethod == 'select') {
        //下拉的需要反编译为value传给数据库
        // value 可能是vlaue也可能是label
        let ite_value = v.value;
        for (let i = 0; i < v.field.attribution.options.length; i++) {
          if (v.field.attribution.options[i].label == v.value) {
            ite_value = v.field.attribution.options[i].value;
            break;
          }
        }
        if (ite_value !== undefined && ite_value !== null && ite_value !== '') {
          fieldInstance.attribution[v.field.fieldName] = ite_value;
        } else {
          fieldInstance.attribution[v.field.fieldName] = undefined;
        }
      } else if (v.field.valueMethod == 'checkbox' || v.field.valueMethod == 'cascader') {
        //多选处理
        let ite_value = undefined;
        if (Array.isArray(v.value) && v.value.length != 0) {
          const list = JSON.parse(JSON.stringify(v.value));
          ite_value = list.join(',');
          v.value = list.join(',');
        }
        fieldInstance.attribution[v.field.fieldName] = ite_value;
      } else if (v.field.valueMethod == 'xttable') {
        //表格处理
        fieldInstance.attribution[v.field.fieldName] = JSON.stringify(v.value.tableTr);
      } else if (v.field.valueMethod == 'xtfj') {
        //附件也需要特殊处理
        const value = [];
        v.value.forEach((k) => {
          const it = {};
          it[`${v.field.fieldName}_0`] = k.url;
          it[`${v.field.fieldName}_1`] = k.title;
          value.push(it);
        });
        fieldInstance.attribution[v.field.fieldName] = value;
      } else {
        if (v.value !== undefined && v.value !== '' && v.value !== null) {
          fieldInstance.attribution[v.field.fieldName] = v.value;
        } else {
          fieldInstance.attribution[v.field.fieldName] = undefined;
        }
      }
    } else {
      //特殊的 指界人 存线
      const linkAttribution = {
        graphicalLine: v.value
      };
      fieldInstance.linkAttribution = linkAttribution;
    }
  });
  if (obj.linkType == 5 || obj.linkType == 6) {
    // 5是线子要素 6是点子要素 子要素保存的时候要整体保存
    const ite_attribution = JSON.parse(JSON.stringify(fieldInstance.attribution));
    const list = []; //子要素list
    nowChecked.value.list.forEach((o, odx) => {
      if (odx == index) {
        //相等的时候需要用修改过后的值
        list.push(ite_attribution);
      } else {
        const ite_obj = {};
        o.fieldList.forEach((q) => {
          ite_obj[q.field.fieldName] = q.value;
        });
        list.push(ite_obj);
      }
    });
    fieldInstance.attribution = { list: list };
  }
  // 如果 linkType=3 代表是线共享 需要绑定之前的选择的线数据 linkAttribution
  if (item.fieldInstance && item.fieldInstance.linkAttribution && obj.linkType == 3) {
    item.fieldInstance.linkAttribution = obj.linkAttribution;
  }

  item.fieldInstanceModels = [fieldInstance];
  item.timeTamp = getTimeStamp([checkedLinyeCopy.value]) || 0;
  delete item.fieldGroupModels;
  saveSimple([item]).then((res) => {
    obj.isEdit = false;
    if (res.code == 200) {
      // 这里反赋值parcelLinkId 只需要赋值给当前选中的多采要素 而不是所有
      obj.parcelLinkId = res.data[0].fieldInstanceModels[0].parcelLinkId;
      obj.timeStamp = res.data[0].fieldInstanceModels[0].timeStamp;
      setNodeTimeStamp([checkedLinyeCopy.value], res.data[0].timeTamp, res.data[0].id);
      //  关闭弹框，因为不好保存数据
      isEditAttr.value = false;
      // 需要查询接口用于判断是否后台会刷新表达式
      selectIfOrder({ moduleId: moduleId.value }).then((res) => {
        if (res.code == 200) {
          if (res.data == 1) {
            //-1 ： 需要排序 0：不用弹窗  1：刷新表达式中，请稍后查看
            ElMessageBox.alert('刷新请求已发送成功（不刷新子要素），请稍后查看', '提示', {
              confirmButtonText: '确定',
              callback: (action: any) => {
                // setTimeout(() => {
                //   // 在这添加是否切换公司的标识。
                //   // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
                //   // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
                //   sessionStorage.setItem('qiehuan_company', false);
                //   location.reload();
                // }, 1000);
              }
            });
          } else if (res.data == -1) {
            ElMessageBox.alert('您有表达式字段，但是未排序，所以不会对表达式字段进行更新！！！', '提示', {
              confirmButtonText: '确定',
              callback: (action: any) => {}
            });
          }
        } else {
          ElMessage.error(res.msg);
        }
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};
/**
 * 删除多采某条数据
 * @param obj 当前项
 * @param index 当前索引
 */
const delChild = (obj: any, index: any) => {
  ElMessageBox({
    title: '提示',
    message: h('p', null, [h('span', null, '确定要删除该条数据吗？')]),
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const item = getJDData([checkedLinyeCopy.value], props.checkedNodeId);
      const fieldInstance: any = {};
      for (let i = 0; i < item.fieldGroupModels.length; i++) {
        if (item.fieldGroupModels[i].linkId == nowChecked.value.linkId) {
          fieldInstance.attribution = {};
          fieldInstance.groupId = item.fieldGroupModels[i].id;
          if (obj.parcelLinkId) {
            fieldInstance.parcelLinkId = obj.parcelLinkId;
          }
          fieldInstance.linkId = item.fieldGroupModels[i].linkId;
          fieldInstance.timeStamp = nowChecked.value.timeStamp || 0;
          fieldInstance.appId = 0;
          break;
        }
      }
      fieldInstance.delFlag = 1;
      item.fieldInstanceModels = [fieldInstance];
      delete item.fieldGroupModels;
      saveSimple([item]).then((res) => {
        if (res.code == 200) {
          ElMessage({
            type: 'success',
            message: '操作成功'
          });
          checkedLinyeCopy.value.list.splice(index, 1);
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};
const changePick = () => {
  showMoreMenu.value = !showMoreMenu.value;
};

/**
 * 新增超期过渡费
 * @param item 当前项
 */
const addCQGDF = (item: any) => {
  editAtrr();
  cqgdfField.value = item.field;
  cqgdfValue.value = item.value.tableTr;
  cqgdfDialog.value = true;
};
const closeCQGDF = () => {
  cqgdfDialog.value = false;
  isEditAttr.value = false;
};
const submitCQGDF = (table: any) => {
  // 得到数据需要调用接口保存
  for (let i = 0; i < checkedLinyeCopy.value.fieldList.length; i++) {
    if (checkedLinyeCopy.value.fieldList[i].label.includes('超期过渡费') && checkedLinyeCopy.value.fieldList[i].type == 7) {
      checkedLinyeCopy.value.fieldList[i].value.tableTr = table;
      break;
    }
  }
  cqgdfDialog.value = false;
  // 完成调用保存
  saveEditAttr();
};

const getStartCountMonth = (startTimestamp: any, ignoreYears: any) => {
  // 验房日期先初始化为月的第一天
  const stdate = new Date(Number(startTimestamp));
  stdate.setDate(1);
  // 创建 Date 对象
  const date = new Date(stdate.getTime());
  // 增加两年
  date.setFullYear(date.getFullYear() + 2);
  // 增加一个月（注意月份是从0开始的，所以加1会得到下一个月）
  date.setMonth(date.getMonth() + 1);
  // 获取新的时间戳
  const timestampAfterTwoYearsAndNextMonth = date.getTime();
  return timestampAfterTwoYearsAndNextMonth;
};

//  根据时间 获取当前的超期过渡费
const changecqgdfRangDate = (val: any) => {
  let FWYSRQ = null;
  let GDMJ = 0;
  let GDQX = 0;
  checkedLinyeCopy.value.fieldList.forEach((v) => {
    if (v.label == '房屋验收日期') {
      //房屋验收日期
      FWYSRQ = v.value;
    } else if (v.label == '过渡面积') {
      GDMJ = v.value;
    } else if (v.label == '过渡期限') {
      GDQX = v.value;
    }
  });
  // 首先计算选择的时间是否合规 意思开始时间到结束时间一定要生成数据，为房屋验收时间+过渡期限的时间
  const startCountMonth = getStartCountMonth(FWYSRQ, GDQX);

  if (val[1] < startCountMonth) {
    // 结束时间小于应开始时间 为不合法
    ElMessage.error('您选择的结束时间小于超期过渡费发放开始时间！！！');
    cqgdfRangDate.value = [];
    return;
  }
  const months = getMonthsToStarAndEndInBegin(val[0], val[1], startCountMonth);
  addCqgdfTableList.value = [];
  months.forEach((v) => {
    const ite = {};
    cqgdfField.value.attribution.children.forEach((k) => {
      if (k.fieldCn == '月份选择') {
        ite[k.fieldName] = v;
      } else if (k.fieldCn == '过渡补偿价格') {
        let costValue = 0; //过渡补偿费用
        const vdx = getDiffMonth(startCountMonth, v);
        let xs = 1.1 ** (vdx + 1) * 10; //系数 (1.1^3*10).toFixed(2) 是截取后两位
        // vdx 应该是当前循环的月份距离开始补偿月份是第几个月
        if (vdx >= 16) {
          //系数 50为最大
          xs = 50;
        } else {
          //小于17次需要截取小数点两位
          xs = +truncateToTwoDecimalPlaces(xs);
        }
        if ([10, 11, 12, 13, 14].includes(vdx)) {
          xs = xs - 0.01;
        }
        costValue = +roundToTwoDecimalPlaces(Number(GDMJ) * xs);
        ite[k.fieldName] = costValue;
      } else {
        ite[k.fieldName] = '';
      }
    });
    addCqgdfTableList.value.push(ite);
  });
  // 得到最终数据了之后需要跟超期过渡费已支付账单进行比对，如果有已支付账单需要看是否在addCqgdfTableList数据里面存在 存在表示已支付
  addCqgdfTableList.value.forEach((v) => {
    if (cqgdfZfzd.value.includes(v.YFXZ)) {
      v.SFYFK = '是';
    }
  });
};

/**
 * 通过 需要计算时间戳和开始计算时间戳 得到两个时间戳之间的相隔月份
 * @param timestampA 开始时间戳
 * @param timestampB 结束时间戳
 * @returns 相隔月份
 */
const getDiffMonth = (timestampA: any, timestampB: any) => {
  // dateA 开始时间 dateB 晚一点的时间
  const dateA = new Date(timestampA);
  const dateB = new Date(timestampB);
  const yearDiff = dateB.getFullYear() - dateA.getFullYear();
  const monthDiff = dateB.getMonth() - dateA.getMonth();
  // 计算总月份差，注意要处理跨年情况
  return yearDiff * 12 + monthDiff;
};

/**
 * 通过开始时间戳和结束时间戳和计费开始时间戳 得到两个时间戳之间的相隔月份
 * @param startMonthTimestamp 开始时间戳
 * @param endMonthTimestamp 结束时间戳
 * @param billingStartMonthTimestamp 计费开始时间戳
 * @returns 相隔月份
 */
const getMonthsToStarAndEndInBegin = (startMonthTimestamp: any, endMonthTimestamp: any, billingStartMonthTimestamp: any) => {
  // 将时间戳转换为Date对象
  const startDate = new Date(startMonthTimestamp);
  const endDate = new Date(endMonthTimestamp);
  const billingStartDate = new Date(billingStartMonthTimestamp);

  // 初始化月份列表
  const validMonthTimestamps = [];

  // 确保计费开始时间不早于开始时间
  const currentMonth = new Date(Math.max(billingStartDate as any, startDate as any));

  // 以月份为单位循环，直至超过或等于结束时间
  while (currentMonth <= endDate) {
    // 为了确保每个月的第一天，我们将日期设置为当月的第一天
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0); // 清除时间部分，仅保留日期

    // 将当前月份的时间戳添加到列表中
    validMonthTimestamps.push(currentMonth.getTime());

    // 移至下一个月份
    currentMonth.setMonth(currentMonth.getMonth() + 1);
  }
  return validMonthTimestamps;
};
/**
 * 根据计算的结果 计算当前应交的超期费用
 * @param month 月份
 * @param GDMJ 过渡面积
 * @returns 应交超期费用
 */
const handleResultMap = (month: any, GDMJ: any) => {
  let result = 0;
  if (month == 1) {
    result = +(GDMJ * 11).toFixed(2);
  } else if (month == 2) {
    result = +(GDMJ * 12.1).toFixed(2);
  } else if (month == 3) {
    result = +(GDMJ * 13.31).toFixed(2);
  } else if (month == 4) {
    result = +(GDMJ * 14.64).toFixed(2);
  } else if (month == 5) {
    result = +(GDMJ * 16.1).toFixed(2);
  } else if (month == 6) {
    result = +(GDMJ * 17.71).toFixed(2);
  } else if (month == 7) {
    result = +(GDMJ * 19.48).toFixed(2);
  } else if (month == 8) {
    result = +(GDMJ * 21.43).toFixed(2);
  } else if (month == 9) {
    result = +(GDMJ * 23.57).toFixed(2);
  } else if (month == 10) {
    result = +(GDMJ * 25.93).toFixed(2);
  } else if (month == 11) {
    result = +(GDMJ * 28.52).toFixed(2);
  } else if (month == 12) {
    result = +(GDMJ * 31.37).toFixed(2);
  } else if (month == 13) {
    result = +(GDMJ * 34.51).toFixed(2);
  } else if (month == 14) {
    result = +(GDMJ * 37.96).toFixed(2);
  } else if (month == 15) {
    result = +(GDMJ * 41.76).toFixed(2);
  } else if (month == 16) {
    result = +(GDMJ * 45.94).toFixed(2);
  } else if (month >= 17) {
    result = +(GDMJ * 50).toFixed(2);
  }
  return result;
};
/**
 * 计算 验收日期 和 过度期限内不免费的日期
 * @param startDate 开始日期
 * @param years 年份
 * @returns 不免费的日期
 */
const generateMonthlyTimestampsExcludingFirstMonth = (startDate: any, years: any) => {
  const result = [];
  const currentDate = new Date(startDate);
  // 移到下一个月，以排除第一个月
  currentDate.setMonth(currentDate.getMonth() + 1);
  // 计算结束年份和月份
  const endYear = startDate.getFullYear() + years;
  const endMonth = startDate.getMonth();
  while (currentDate.getFullYear() < endYear || (currentDate.getFullYear() === endYear && currentDate.getMonth() <= endMonth)) {
    // 设置日期为当月最后一天
    const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    // 将时间戳添加到结果数组中
    result.push(lastDayOfMonth.getTime());
    // 移动到下一个月
    currentDate.setMonth(currentDate.getMonth() + 1);
  }
  return result;
};

/**
 * 根据选择的时间获取选择的月份间隙
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 月份间隙
 */
const getMonthDifferencePrecise = (startDate: any, endDate: any) => {
  if (startDate > endDate) {
    [startDate, endDate] = [endDate, startDate];
  }
  const startYear = startDate.getFullYear();
  const startMonth = startDate.getMonth();
  const startDay = startDate.getDate();
  const endYear = endDate.getFullYear();
  const endMonth = endDate.getMonth();
  const endDay = endDate.getDate();

  const yearDiff = endYear - startYear;
  const monthDiff = endMonth - startMonth;
  const dayDiff = endDay >= startDay ? 0 : -1;
  return yearDiff * 12 + monthDiff + dayDiff;
};

/**
 * 获取超期过路费的列表
 */
const initAddCqgdfTableList = () => {
  // 需要先初始化超期过渡费选择的时间
  let FWYSRQ = null;
  let GDMJ = 0;
  let GDQX = 0;
  checkedLinyeCopy.value.fieldList.forEach((v) => {
    if (v.label == '房屋验收日期') {
      //房屋验收日期
      FWYSRQ = v.value;
    } else if (v.label == '过渡面积') {
      GDMJ = v.value;
    } else if (v.label == '过渡期限') {
      GDQX = v.value;
    }
  });
  if (FWYSRQ) {
    // 验房日期先初始化为月的第一天
    const stdate = new Date(Number(FWYSRQ));
    stdate.setDate(1);
    // 创建 Date 对象
    const date = new Date(stdate.getTime());
    // 增加两年
    date.setFullYear(date.getFullYear() + 2);
    // 增加一个月（注意月份是从0开始的，所以加1会得到下一个月）
    date.setMonth(date.getMonth() + 1);
    // 获取新的时间戳
    const timestampAfterTwoYearsAndNextMonth = date.getTime();
    const nowDate = new Date();
    cqgdfRangDate.value = [timestampAfterTwoYearsAndNextMonth, nowDate.getTime()];
    changecqgdfRangDate(cqgdfRangDate.value);
  }
};
//  获取当前的超期选中的时间范围
const getMonthsInRange = (startTimestamp: any, ignoreYears: any, currentDate = new Date()) => {
  // 将开始日期的时间戳转换为 Date 对象
  const startDate = new Date(startTimestamp);
  // 计算出忽略两年后的日期（这里使用毫秒数表示两年：2 * 365 * 24 * 60 * 60 * 1000，注意闰年）
  // 为了简化，这里我们假设每年都是365天，实际中应考虑闰年
  const millisecondsInYear = 365 * 24 * 60 * 60 * 1000;
  const ignoreDate = new Date(startDate.getTime() + ignoreYears * millisecondsInYear);
  // 获取当前日期（默认为调用函数时的日期）
  // const currentDate = new Date(); // 如果不需要默认参数，可以取消注释这行代码
  // 初始化一个数组来存储月份
  const monthsInRange = [];
  // 设置起始月份为忽略日期后的第一个月（忽略日期的月份+1，如果超出11则年份+1，月份重置为0）
  const currentMonth = new Date(ignoreDate);
  currentMonth.setMonth(ignoreDate.getMonth() + 1);
  if (currentMonth.getMonth() === ignoreDate.getMonth()) {
    // 如果月份没变化，说明是12月之后，年份需要+1
    currentMonth.setFullYear(currentMonth.getFullYear() + 1);
    currentMonth.setMonth(0); // 重置月份为1月（0索引）
  }
  // 遍历直到当前日期或下个月超出当前日期
  while (currentMonth <= currentDate) {
    // 只存储年份和月份，创建一个新的 Date 对象避免引用问题
    monthsInRange.push(new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1).getTime());
    // 移动到下一个月
    currentMonth.setMonth(currentMonth.getMonth() + 1);
  }

  // 返回月份列表（每个元素都是一个表示该月第一天的 Date 对象）
  return monthsInRange;
};

/**
 * 截取小数点2位小数
 * @param number 数字
 * @returns 截取后的数字
 */
const truncateToTwoDecimalPlaces = (number: any) => {
  // 将数字转为字符串，便于后续操作
  const numberStr = number.toString();
  // 查找小数点的位置
  const decimalPointIndex = numberStr.indexOf('.');
  // 如果没有小数点，说明是整数，直接返回并添加.00
  if (decimalPointIndex === -1) {
    return number + '.00';
  }
  // 截取小数点后两位字符
  const integerPart = numberStr.slice(0, decimalPointIndex); // 整数部分
  const decimalPart = numberStr.slice(decimalPointIndex + 1, decimalPointIndex + 3); // 小数部分（仅取两位）
  // 若小数部分不足两位，则用0填充
  const truncatedNumberStr = integerPart + '.' + (decimalPart.length === 2 ? decimalPart : decimalPart + '0');
  // 将字符串转回数字并返回（注意：这可能会引入浮点误差，但在此场景下通常可接受）
  return parseFloat(truncatedNumberStr);
};
/**
 * 计算小数次数
 * @param number 数字
 * @returns 小数次数
 */
const roundToTwoDecimalPlaces = (number: any) => {
  return (Math.round(number * 100) / 100).toFixed(2);
};
/**
 * 新增超期过渡费
 */
const handleAddCQGDF = () => {
  addCQGDFMsg.value = {};
  activeMenu.value.fieldList.forEach((v) => {
    addCQGDFMsg.value[v.field.fieldName] = '';
  });
  visibleAddCQGDF.value = true;
};
/**
 * 新增安置房
 */
const handleAddAZF = () => {
  nowNodeMsg.value = getJDData([checkedLinyeCopy.value], props.checkedNodeId);
  // 需要给nowQLR赋值 TODO
  afzDialog.value = true;
};
/**
 * 关闭安置房
 */
const handleCloseAfzDialog = () => {
  afzDialog.value = false;
};
/**
 * 提交安置房
 */
const handleSubmitAddAzf = (obj: any) => {
  activeMenu.value.zxAzfList.push(obj);
  afzDialog.value = false;
};

/**
 * 组装附件
 */
const getFJ = (item: any, file: any) => {
  const list = [];
  if (isArray(item) && item.length != 0) {
    item.forEach((v) => {});
  }
  return list;
};

/**
 * 附件上传成功
 */
const handleSuccessFJ = (response: any, item: any, zditem: any) => {
  if (response.data && isArray(response.data)) {
    const obj: any = {};
    obj[`${item.fieldName}_0`] = response.data[0].path;
    obj[`${item.fieldName}_1`] = response.data[0].name;
    obj.url = response.data[0].path;
    obj.title = response.data[0].name;
    if (!zditem.value) {
      zditem.value = [];
    }
    zditem.value.push(obj);
  }
};

/**
 * 附件移除
 */
const handleRemoveFJ = (file: any, item: any, zditem: any) => {
  let num = 0;
  if (zditem.value && isArray(zditem.value)) {
    for (let index = 0; index < zditem.value.length; index++) {
      if (file.response.data[0].path == zditem.value[index].url) {
        num = index;
        break;
      }
    }
    zditem.value.splice(num, 1);
  }
};

/**
 * 移除照片
 */
const handleRemove = (file: any, item: any) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      for (let i = 0; i < item.value.length; i++) {
        if (item.value[i].uid == file.uid) {
          item.value.splice(i, 1);
          break;
        }
      }
    })
    .catch(() => {});
};
/**
 * 上传前验证
 */
const beforeAvatarUpload = (file: any, item: any) => {
  const type = getFileType(file.type);
  let flg = false;
  for (let index = 0; index < item.attribution.acceptType.length; index++) {
    if (type.includes(item.attribution.acceptType[index])) {
      flg = true;
      break;
    }
  }
  if (!flg) {
    ElMessage.error(`不支持上传${file.type}格式！！！`);
  }
  return flg;
};
/**
 * 删除执行
 */
const delZX = (obj: any, index: any) => {
  ElMessageBox.confirm('确定要删除该条数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const item = getJDData([checkedLinyeCopy.value], props.checkedNodeId);
      const fieldInstance: any = {};
      for (let i = 0; i < item.fieldGroupModels.length; i++) {
        if (item.fieldGroupModels[i].linkId == nowChecked.value.linkId) {
          fieldInstance.attribution = {};
          fieldInstance.groupId = item.fieldGroupModels[i].id;
          if (obj.parcelLinkId) {
            fieldInstance.parcelLinkId = obj.parcelLinkId;
          }
          fieldInstance.linkId = item.fieldGroupModels[i].linkId;
          fieldInstance.timeStamp = nowChecked.value.timeStamp || 0;
          fieldInstance.appId = 0;
          break;
        }
      }
      fieldInstance.delFlag = 1;
      item.fieldInstanceModels = [fieldInstance];
      delete item.fieldGroupModels;
      saveSimple([item]).then((res) => {
        if (res.code == 200) {
          ElMessage({
            type: 'success',
            message: '操作成功'
          });
          activeMenu.value.zxAzfList.splice(index, 1);
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};
/**
 *  关闭界址线找回
 */
const handleCloseJzxBack = () => {
  jzxBackDialog.value = false;
};
/**
 *  处理行政区划字段
 * @param msg 字段 值
 * @returns 处理后的字段值
 */
const getAreaCode = (msg: any) => {
  // 判断是对象还是字符串 特殊处理一下
  if (typeof msg === 'object') {
    return msg.fullName;
  } else {
    try {
      // 尝试将 msg 解析为 JSON 对象
      const parsedMsg = JSON.parse(msg);
      if (parsedMsg.fullName) {
        return parsedMsg.fullName;
      }
    } catch (error) {
      // 解析失败，说明 msg 是普通字符串，直接返回
      return msg;
    }
  }
};

/**
 * 得到是否允许更新 通过获取是否有表达式，如果有表达式，但是没有排序 不允许更新数据
 * @returns code -1 表达式未排序 0 没有表达式 1 有表达式且已排序
 */
const getAllowUpdate = async () => {
  return new Promise((resolve, reject) => {
    const params = {
      moduleId: moduleId.value
    };
    selectIfOrder(params).then((res) => {
      if (res.code == 200) {
        resolve(res.data);
      } else {
        ElMessage.error(res.msg);
        resolve(-1);
      }
    });
  });
};
/**
 * 返回节点的时间戳 版本号
 * @param nodes 节点
 * @returns 时间戳
 */
const getTimeStamp = (nodes: any) => {
  // 通过this.checkedNodeId 跟树结构this.checkedLinye里面的id进行对比 并返回对应的timeTamp
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    if (node.id === props.checkedNodeId) {
      return node.timeTamp;
    }
    if (node.list && node.list.length > 0) {
      const result = getTimeStamp(node.list);
      if (result !== null) {
        return result;
      }
    }
  }
  return null;
};

/**
 * // 用于判断是否能显示出来label
 * @param value 值
 * @returns option 选项
 * @returns 样式对象
 */
const filterOptionFun = (value: any, options: any) => {
  let flg = false;
  for (let i = 0; i < options.length; i++) {
    if (options[i].value == value) {
      flg = true;
      break;
    }
  }
  if (!flg && value) {
    return {
      color: 'red'
    };
  }
  return '';
};

/**
 * 主动刷新表达式
 * @returns 无
 */
const refExpress = async () => {
  const code = await getAllowUpdate();
  if (code < 0) {
    ElMessageBox.alert('请对表达式进行排序后再修改数据！！！', '提示', {
      confirmButtonText: '确定',
      callback: (action: any) => {}
    });
    return;
  }
  ElMessageBox.confirm('确定要主动刷新该数据的表达式吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      updateParcelFromMain([props.mainId], moduleId.value).then((res) => {
        if (res.code == 200) {
          ElMessageBox.alert('表达式刷新发送成功，请稍后查看！！！', '提示', {
            confirmButtonText: '确定',
            callback: (action: any) => {
              setTimeout(() => {
                // 在这添加是否切换公司的标识。
                // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
                // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
                sessionStorage.setItem('qiehuan_company', false);
                location.reload();
              }, 1000);
            }
          });
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};

/**
 * 根据ruleId 得到对应的属性组列表
 * @param tree 规则树
 * @param id 规则id
 * @returns 属性组列表
 */
const getFieldGroupModels = (tree: any, targetId: number) => {
  // 定义一个辅助函数用于递归查找
  const findNode = (nodes: any[]) => {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.id === targetId) {
        return node.fieldGroupModelList;
      }
      if (node.list && node.list.length > 0) {
        const result = findNode(node.list);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  return findNode(tree);
};

/**
 * 修改完成之后找到对应的数据把timeStamp值
 * @param node 节点树
 * @param timeStamp 更新的时间戳
 * @param id 节点id
 */
const setNodeTimeStamp = (node: any, timeStamp: number, id: number) => {
  for (let i = 0; i < node.length; i++) {
    if (node[i].id === id) {
      node[i].timeTamp = timeStamp;
      break;
    }
    if (node[i].list && node[i].list.length > 0) {
      setNodeTimeStamp(node[i].list, timeStamp, id);
    }
  }
};

defineExpose({
  setSelectTitleList
});
// --- onMounted --
onMounted(() => {
  moduleId.value = projectStore.proModuleId;
  // 判断是否有编辑权限
  const permissions = userStore.permissions;
  const value = 'data:manager:edit';
  const all_permission = '*:*:*';
  if (!permissions.includes(all_permission) && !permissions.includes(value)) {
    allowEdit.value = false;
  } else {
    allowEdit.value = true;
  }
  if (userStore.user['companyId'] == '79c582ec698b45ee8f475c518104c805') {
    //湖北交投
    editExpressPower.value = true;
  }
  isAZF.value = route.query.isAZF;
});
</script>
<style lang="scss" scoped>
.fj-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 8px;
  justify-content: space-between;
  cursor: pointer;
}
.fj-row :hover {
  color: #409eff;
}
.azf-group {
  margin: 10px;
}
.flex-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 10px;
}
.express-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 10px;
  margin-bottom: 10px;
  .express-label {
    color: #000;
    font-weight: bold;
  }
  .express-con {
    flex: 1;
    flex-wrap: wrap;
  }
}
.add-flex-row {
  display: flex;
  align-items: center;
  overflow-x: auto;
  padding: 0px 10px;
  margin: 10px 0px;
}

:deep(.el-select) {
  border: var(--current-color) solid 1px !important;
  font-size: 14px;
  border-radius: 4px;
}
.active-img {
  border: var(--current-color) solid 1px;
}
:deep(.el-upload--picture-card) {
  width: 59px;
  height: 59px;
  line-height: 59px;
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 59px;
  height: 59px;
  line-height: 59px;
}

.edit-div {
  height: 40px;
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 0px;
  z-index: 1;
  // background: rgb(0, 0, 0);
  width: 100%;
  border-radius: 0px 0px 8px 8px;
  justify-content: flex-end;
  padding-right: 12px;
  .edit-btn {
    color: #fff;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
  }
  .edit-btn-active {
    background: var(--current-color);
  }
  .edit-btn:hover {
    background: var(--current-color);
  }
  .sumbit-btn {
    color: #fff;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    background: var(--current-color);
    margin-left: 12px;
  }
  .reset-btn {
    color: #fff;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    background: #909399;
    margin-left: 12px;
  }
}
.edit-handle {
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0px 16px;
}
:deep(.el-checkbox__label) {
  font-size: 12px;
}

.label {
  font-size: 13px !important;
  font-weight: bold !important;
  color: #ffffffad !important;
}

.dialog-table {
  width: 100%;
  max-height: 500px;
  overflow: auto;
}
.table-class {
  max-height: 500px;
  width: 100%;
  border-spacing: 2px;
  border: none;
  border-left: solid 1px #e6e7eb;
  border-top: solid 1px #e6e7eb;
}
.table-class td {
  border-left: none;
  border-top: none;
  padding: 10px;
  word-break: keep-all;
}
.table-class tr {
  border-top: none;
}
.th-class {
  // border-left: 1px solid #e6e7eb;
  // border-top: 1px solid #e6e7eb;
  padding: 10px;
  word-break: keep-all;
  font-weight: bold;
}

:deep(.el-tree-node__expand-icon.is-leaf) {
  display: none;
}
:deep(.el-dropdown) {
  position: relative;
  color: #fff;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px 10px;
  background-color: var(--current-color);
  border-radius: 4px;
  height: 28px;
  padding: 0px 10px;
  cursor: pointer;
  white-space: nowrap;
}
.mini-main {
  height: 0px;
}
.msgInfo-main {
  width: 100%;
  height: 100%;
  font-size: 14px;
  border: #ededed solid 1px;
  display: flex;
  flex-direction: column;
  position: relative;
  .title {
    background: #f7f8f6;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    height: 40px;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    position: relative;
    .title-num {
      position: absolute;
      left: 10px;
      display: flex;
      align-items: center;
    }
  }

  .title-right {
    position: absolute;
    top: 0;
    right: 16px;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .deg-box {
    // position: absolute;
    // top: 0px;
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1;
    position: relative;
    overflow: auto;
    .handle-div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // background: rgb(0, 0, 0, 1);
      width: 100%;
      height: 56px;
      padding: 0px 5px;
    }
    .deg-close {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgb(0, 0, 0, 0.4);
      color: #fff;
      position: absolute;
      right: 25px;
      top: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 1;
      font-size: 16px;
    }
    .deg-img {
      width: calc(100%);
      height: 100%;
    }
    .sy-div {
      position: absolute;
      bottom: 0px;
      right: 0px;
      background: rgb(0, 0, 0, 0.4);
      font-size: 12px;
      color: #fff;
      .sy-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        padding: 0px 5px;
      }
    }
    .setting {
      position: absolute;
      top: 56px;
      left: 0px;
      background: rgb(0, 0, 0, 0.4);
      color: #fff;
      padding: 2px;
      cursor: pointer;
      z-index: 999;
      font-size: 12px;
      .checked-row {
        display: flex;
      }
    }
  }
  .info-select-tab {
    display: flex;
    flex-direction: row;
    // width: 512px;
    // height: 40px;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    .info-left {
      flex: 1;
      display: flex;
      flex-direction: row;
      height: auto;
      .menu-box {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        padding: 6px;
        // background: rgb(0, 0, 0, 0.4);
        .menu-item {
          min-width: 96px;
          padding: 5px 10px;
          cursor: pointer;
          margin-right: 5px;
          margin-bottom: 5px;
          border: #e0e0e0 solid 1px;
          // text-align-last:justify;
          text-align: center;
          background: rgb(0, 0, 0, 0.04);
          border-radius: 4px;
        }
        .menu-item:hover {
          background-color: #e0e0e0;
          // color: #fff;
        }
        .menu-active {
          background: #1890ff;
          color: #fff;
        }
      }
      .right-handle {
        // width: 100px;
        padding: 5px 10px;
      }
    }
    .info-right {
      // width: 100px;
      display: flex;
      justify-content: flex-end;
      margin-right: 10px;
    }
    .info-end-handle {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      margin-right: 10px;
    }
    .log-btn {
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      padding: 0px 10px;
      border-radius: 4px;
      margin-left: 10px;
    }
    .log-btn:hover {
      background: var(--current-color);
    }
    .active-log {
      background: var(--current-color);
    }
  }
  .content {
    // height: calc(100% - 80px);
    flex: 1;
    width: 100%;
    overflow: auto;
    padding-bottom: 45px;
    .content-normal {
      // height: 100%;

      // display: grid;
      // grid-template-columns: repeat(2, 1fr);
      border-top: 1px solid #46a6ffb0;
      border-left: 1px solid #46a6ffb0;
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      .grid-row {
        // width: 50%;
        flex: 1 1 50%; /* 每行两条，减去左右padding */
        display: flex;
        flex-direction: row;
        align-items: center;
        // min-height: 40px;
        padding: 8px 0px;
        border-bottom: 1px solid #46a6ffb0;
        border-right: 1px solid #46a6ffb0;
        // border-left: 1px solid #46a6ffb0;
        .label {
          width: 130px;
          flex-wrap: wrap;
          font-weight: bold;
          color: #111112 !important;
          font-size: 14px !important;
          padding-left: 5px;
          // border-right: 1px solid #46a6ffb0;
        }
        .hr {
          height: calc(100% + 16px);
          border-right: 1px solid #46a6ffb0;
        }
        .right-content {
          padding-left: 5px;
          padding-right: 5px;
          flex: 2;
          color: rgb(0, 0, 0, 0.7);
          display: flex;
          flex-wrap: wrap;
        }
      }
      // 这里展示表格的内容数据
      .grid-table-main {
        margin-top: 10px;
        display: flex;
        width: 100%;
        flex-direction: column;
        // margin-bottom: 30px;
        .table-list {
          width: 100%;
          .table-title {
            width: 100%;
            flex-wrap: wrap;
            font-weight: 600;
            color: #111112 !important;
            font-size: 14px !important;
            padding-left: 5px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-right: 8px;
          }
          .table-main {
            width: 100%;
            // max-height: 500px;
            overflow: auto;
            .table-class {
              // max-height: 500px;
              width: 100%;
              border-spacing: 2px;
              border: none;
              border-left: solid 1px #46a6ffb0;
              border-top: solid 1px #46a6ffb0;
              td {
                // border-left: none;
                // border-top: none;
                border-left: solid 1px #46a6ffb0;
                border-top: solid 1px #46a6ffb0;
                padding: 10px;
                word-break: keep-all;
              }
              tr {
                border-top: none;
              }
              .table-th-class {
                border-left: 1px solid #46a6ffb0;
                border-top: 1px solid #46a6ffb0;
                padding: 10px;
                word-break: keep-all;
                font-weight: bold;
              }
            }
          }
        }
      }
    }
    .info-row {
      display: flex;
      // justify-content: center;
      // align-items: center;
      width: 100%;
      flex-direction: row;
      padding: 10px 16px;
      // &:nth-child(even){
      //   background-color: red;
      // }
      &:nth-child(odd) {
        background: rgba(0, 0, 0, 0.5);
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
      }
      .label {
        width: 130px;
        font-size: 14px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        margin-right: 10px;
      }
      .content {
        flex: 1;
        max-width: 350px;
        font-size: 14px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        display: flex;
        flex-wrap: wrap;
      }
    }
    .conten-box {
      width: calc(100% - 20px);
      margin: 10px 10px 0px 10px;
      border-top: 1px solid #46a6ffb0;
      border-left: 1px solid #46a6ffb0;
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      .child-title {
        font-size: 12px;
        // color: hsla(0, 0%, 100%, 0.8);
        padding: 10px;
      }
      .child-row {
        flex: 1 1 50%; /* 每行两条，减去左右padding */
        padding-bottom: 10px;
        display: flex;
        flex-direction: row;
        border-bottom: 1px solid #46a6ffb0;
        border-right: 1px solid #46a6ffb0;
        // border-left: 1px solid #46a6ffb0;
        padding: 8px 0px;
        align-items: center;
        .child-item {
          display: flex;
          // align-items: center;
          // color: #fff;
          padding-left: 10px;
        }
        .child-left {
          width: 140px;
          font-weight: bold;
          color: #111112 !important;
          font-size: 14px !important;
          // color: #ffffffad !important;
          // border-right: 1px solid #46a6ffb0;
        }
        .hr {
          height: calc(100% + 16px);
          border-right: 1px solid #46a6ffb0;
        }
        .child-right {
          flex: 1;
          color: rgb(0, 0, 0, 0.7);
        }
      }
      .child-footer {
        color: var(--current-color);
        display: flex;
        justify-content: flex-end;
        padding-right: 20px;
        padding-bottom: 10px;
        cursor: pointer;
      }
    }
  }
  .content ::-webkit-scrollbar {
    width: 1px;
  }
  /*滚动条样式*/
  .content::-webkit-scrollbar {
    width: 4px;
  }
  .content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  .content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
  }
  .info-bottom {
    position: absolute;
    bottom: 0;
    background: #f7f8f6;
    height: 40px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 10px;
  }
}
.no-img {
  width: 59px;
  height: 59px;
  border-radius: 6px;
  background: rgba(248, 248, 248, 0.1);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  // margin-top: 5px;
  border: dashed 1px rgba(255, 255, 255, 0.5);
  font-size: 12px;
}
.img-box {
  width: 140px;
  height: 110px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 8px;
}
.video-box {
  width: 100%;
  height: 100px;
}
.discern-box {
  // width: 100%;
  // height: 100%;
  position: relative;
  margin-right: 10px;
  margin-bottom: 10px;
  .footer-div {
    position: absolute;
    bottom: 0px;
    height: 30px;
    background: rgb(0, 0, 0, 0.7);
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--current-color);
    font-size: 12px;
    cursor: pointer;
    z-index: 1;
  }
}
.content-spe {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 10px;
  grid-row-gap: 10px;
}
.spe-content {
  display: flex;
  max-height: 500px;
  overflow: auto;
  flex-direction: row;
  .left-img {
    flex: 1;
  }
  .right-content {
    margin-left: 10px;
    width: 100px;
    font-size: 12px;
    color: #666;
    .spe-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}
.content-img {
  flex: 1;
  // display: grid;
  // grid-template-columns: repeat(3, 1fr);
  // grid-template-rows: 1fr;
  // grid-column-gap: 10px;
  // grid-row-gap: 10px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.content-img-table {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 10px;
  grid-row-gap: 10px;
}
.content-video {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 10px;
  grid-row-gap: 10px;
}
.fj-content {
  display: flex;
  flex-direction: column;
  // color: #fff;
  .fj-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 32px;
    cursor: pointer;
  }
  .fj-item:hover {
    color: var(--current-color);
  }
}
.jc-div {
  position: absolute;
  left: 10px;
}
.jc-div1 {
  position: absolute;
  // left: 85px;
}
.dialog-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  .dialog-item {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    .label {
      width: 120px;
      text-align: right;
      margin-right: 10px;
    }
    .flex-content {
      flex: 1;
    }
  }
}
.fj-item span:hover {
  text-decoration: underline;
  opacity: 0.8;
}
</style>
